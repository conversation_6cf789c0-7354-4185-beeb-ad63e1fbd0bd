import re
import json
import os

def extract_prompt_info(content):
    prompts = []
    # Regex to find prompts with title and optional function
    # It will match patterns like '## 1. Title - Function' or '## 2. Title'
    markdown_prompts = re.findall(r'^##\s*(\d+)\.\s*([^\r\n]+)([\s\S]*?)(?=^##\s*\d+\.|\Z)', content, flags=re.MULTILINE)

    for p in markdown_prompts:
        prompt_id = int(p[0])
        title_full = p[1].strip()
        body = p[2].strip()

        if ' - ' in title_full:
            title, function = title_full.split(' - ', 1)
        else:
            title = title_full
            function = ''

        usage = ''
        # For prompts without the new structure, the whole body is the description.
        description = body

        # Check for the new structure with "使用方法" and "提示词"
        if '### 使用方法' in body:
            # Extract usage
            usage_match = re.search(r'###\s*使用方法\s*([\s\S]*?)(?=\s*###\s*提示词|$)', body, re.DOTALL)
            if usage_match:
                usage = usage_match.group(1).strip()

            # Extract prompt content (description)
            prompt_match = re.search(r'###\s*提示词\s*([\s\S]*)', body, re.DOTALL)
            if prompt_match:
                description = prompt_match.group(1).strip()
            elif usage_match:
                # If usage exists but '### 提示词' section doesn't, description should be empty.
                description = ''
        
        prompts.append({
            'id': prompt_id,
            'title': title.strip(),
            'function': function.strip(),
            'usage': usage,
            'description': description
        })

    return prompts

def create_index(md_file_path, json_file_path):
    try:
        with open(md_file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        prompts = extract_prompt_info(content)

        with open(json_file_path, 'w', encoding='utf-8') as f:
            json.dump(prompts, f, ensure_ascii=False, indent=4)

        print(f"Index file created at: {json_file_path}")
        print(f"Processed {len(prompts)} prompts.")

    except FileNotFoundError:
        print(f"Error: The file {md_file_path} was not found.")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    # Assuming the script is in the 'Prompt' directory, and the md file is in the same directory.
    script_dir = os.path.dirname(os.path.abspath(__file__))
    md_file = os.path.join(script_dir, '李继刚提示词大全.md')
    json_file = os.path.join(script_dir, 'prompt_index.json')
    create_index(md_file, json_file)