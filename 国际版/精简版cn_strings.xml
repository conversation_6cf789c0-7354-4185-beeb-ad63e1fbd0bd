<resources>
    <string name="app_name">测亩易国际版</string> <!-- 应用名称：App在各国际语言版本中统一显示为 "aFarm"。 -->

    <!-- 测量模式 -->
    <string name="zidong">手持模式</string> <!-- 测量方式选项：用户手持手机，利用手机GPS定位，沿着目标地块边界行走一圈，App自动记录轨迹以确定地块边界并计算面积。 -->
    <string name="shoudong">测量模式</string> <!-- 页面标题/功能分类：标识一个包含多种土地测量方式选择的页面或功能区域的标题。 -->
    <string name="jizai">作业模式</string> <!-- 测量方式选项（通常基于车载模式）：特指在车辆（已配备定位设备）上进行农田作业时，按照特定路径（如绕圈、S型、平行跨行等）行驶，用以记录作业轨迹、计算作业面积及其他相关数据。 -->
    <string name="jingzhun">精准模式</string> <!-- 手动打点测量方式选项：一种通过移动地图，将地块边界上的目标点对准屏幕中心的十字准星来进行标记的测量方式，以提高手动打点的精确度（相对于直接在地图上用手指点击标记）。 -->
    <string name="chezai">车载模式</string> <!-- 测量方式选项：将外部定位设备（如高精度GPS天线、蓝牙或4G定位终端）安装在车辆上，通过记录车辆行驶轨迹来测量地块边界、计算面积或记录其他相关数据。 -->
    
    <!-- 农事收支记录功能 -->
    <string name="new_expenditure">新增支出</string> <!-- 操作按钮/菜单项：点击后进入添加一笔新的农业经营相关费用支出的界面。 -->
    <string name="new_revenue">新增收入</string> <!-- 操作按钮/菜单项：点击后进入添加一笔新的农业经营相关收益的界面。 -->
    <string name="wodezhangben">我的账本</string> <!-- 功能模块入口/页面标题：用户查看、管理和分析其所有农业收支记录的个人财务中心。 -->
    
    <!-- 核心农业对象 -->
    <string name="dikuai">地块</string> <!-- 核心概念/UI标签：指农业生产管理的基本单元，即一块具有明确边界和属性的土地。App中用于指代用户测量、保存和管理的田块。 -->
</resources>
    
    <!-- 位置 -->
    <string name="map_location_tips">获取定位权限会定位到你的当前位置，获取更好的用户体验。</string>
    
    <!-- bottom_view: App底部导航栏标签 -->
    <string name="bottom_view_measure">测量</string> <!-- 导航功能按钮：点击进入App的核心测量功能区，提供多种土地面积和距离的测量方式入口。 -->
    <string name="bottom_view_field">地块</string> <!-- 导航功能按钮：点击进入地块列表页面，用于查看和管理用户所有已测量并保存的地块信息。 -->
    <string name="bottom_view_tool">工具</string> <!-- 导航功能按钮：点击进入实用工具集页面，提供如面积单位换算等多种辅助小功能。 -->
    <string name="bottom_view_mine">我的</string> <!-- 导航功能按钮：点击进入用户个人中心页面，包含账户管理、App设置、帮助与反馈等标准功能。 -->

    <!-- home: App主界面/地图界面的相关功能与元素 -->
    <string name="home_search">地块名称</string> <!-- UI元素/搜索框占位符文本：提示用户可以在此输入地块名称进行搜索，以便快速定位或查找已保存的地块。 -->
    <string name="home_guide">指南</string> <!-- UI元素/按钮或链接文本：点击后打开App的使用指南、操作教程或帮助文档。 -->
    <string name="home_hide">隐藏</string> <!-- UI元素/按钮或开关文本：用于隐藏地图上的特定图层、标记、地块或其他可视元素。 -->
    <string name="home_show">显示</string> <!-- UI元素/按钮或开关文本：用于显示地图上先前被隐藏的特定图层、标记、地块或其他可视元素。 -->
    <string name="home_share">分享</string> <!-- UI元素/按钮文本：点击后允许用户将当前查看的地块信息、测量结果或App本身通过社交媒体、消息等方式分享给他人。 -->
    <string name="home_route">路线</string> <!-- UI元素/按钮文本：点击后启动导航功能，规划并显示从当前位置到选定地块或标记点的行进路线。 -->
    <string name="home_add_plot">画地块</string> <!-- UI元素/核心功能按钮：点击后进入手动在地图上通过打点或绘制方式创建新地块边界的功能界面。 -->
    <string name="home_to_distance">测距</string> <!-- UI元素/测量工具按钮：点击后激活距离测量工具，允许用户在地图上选择两点或多点以测量它们之间的直线距离或累计路径长度。 -->
    <string name="home_to_auto">走一圈</string> <!-- UI元素/测量方式按钮：与“手持模式”类似，指通过用户持设备沿地块边界行走，App自动记录GPS轨迹来测量地块面积和周长的功能。 -->
    <string name="home_add_mark">打标记</string> <!-- UI元素/工具按钮：允许用户在地图上的任意位置添加自定义标记点（例如标记水源、障碍物、地块入口等），并可附带名称或备注。 -->
    <string name="home_map_number">审图号：</string> <!-- UI元素/地图信息标签：显示地图数据提供商或地图服务所依据的官方地图审核批准编号。冒号“：”为固定文本部分。 -->
    <string name="home_location_service">开启定位服务</string> <!-- UI元素/提示或按钮文本：当App检测到系统定位服务未开启时，提示并引导用户去手机系统设置中开启定位服务。 -->
    <string name="home_location_error">没有定位权限，定位当前位置失败</string> <!-- UI元素/错误提示信息：当App因缺少定位权限而无法获取用户当前位置时显示的错误消息。 -->
    <string name="home_location_to_open">去开启</string> <!-- UI元素/按钮文本：常用于权限请求失败或定位服务未开启的提示中，点击后通常会跳转到App的权限设置页面或手机的系统设置页面，引导用户开启相关权限或服务。 -->
    <string name="home_permission_specification">权限说明</string> <!-- UI元素/页面标题或链接文本：点击后展示App需要各项权限（如定位、存储、相机等）的具体原因和用途说明。 -->
    <string name="home_permission_refuse">拒绝了一些必要权限，请查看开启教程后重试</string> <!-- UI元素/错误提示信息：当用户拒绝了App运行所必需的某些权限后，显示的提示信息，通常会引导用户查看权限开启教程并重试。 -->
    <string name="home_permission_specification_avatar">• 用于用户编辑头像图片</string> <!-- 权限说明列表项：具体解释某项权限（例如相机或存储权限）的用途，此条特指用于用户选择或拍摄并编辑个人头像图片。项目符号“• ”为固定文本部分。 -->

    <string name="picture_prompt">提示</string> <!-- UI元素/通用对话框标题：常用于图片选择、上传、处理等相关操作中，作为信息提示或确认对话框的标题。 -->

    <string name="reference_yield">参考产量</string> <!-- 农业数据/UI标签或输入字段名：指为某个地块记录或输入的预估或历史农作物产量数值，用作参考。 -->

     <!-- add_plot: "画地块"功能界面的相关元素与提示 -->
    <string name="add_plot">画地块</string> <!-- UI元素/页面标题或功能入口名称：与 `home_add_plot` 类似，指手动在地图上通过打点或绘制方式创建新地块边界的功能界面或其标题。 -->
    <string name="add_plot_search">点击搜索</string> <!-- UI元素/按钮或可交互区域文本：提示用户点击此处以通过输入地名、坐标等方式搜索特定位置，方便快速定位到要绘制地块的区域。 -->
    <string name="add_plot_tutorial">教程</string> <!-- UI元素/按钮或链接文本：点击后展示关于如何使用“画地块”功能的图文或视频教程。 -->
    <string name="add_plot_area">面积</string> <!-- UI元素/标签或数据显示区域：用于显示当前正在绘制或已选定地块的计算面积。 -->
    <string name="add_plot_mu">亩</string> <!-- 面积单位/UI文本：中国常用的土地面积单位。在画地块界面中用于显示面积数值的单位。1亩 ≈ 666.67平方米。 -->
    <string name="add_plot_length">长度</string> <!-- UI元素/标签或数据显示区域：用于显示当前正在绘制地块的周长，或在测距模式下显示线段长度。 -->
    <string name="add_plot_length_0">长度 0米</string> <!-- UI元素/初始状态显示文本：在开始画地块或测距时，长度的初始默认值为0米。 -->
    <string name="add_plot_meters">米</string> <!-- 长度单位/UI文本：国际标准长度单位。在画地块界面中用于显示长度或周长数值的单位。 -->
    <string name="add_plot_square_meter">平方米</string> <!-- 面积单位/UI文本：国际标准面积单位。在画地块界面中用于显示面积数值的单位。 -->
    <string name="add_plot_hectare">公顷</string> <!-- 面积单位/UI文本：较大的土地面积单位。在画地块界面中用于显示面积数值的单位。1公顷 = 10000平方米。 -->
    <string name="add_plot_operation_tip1">操作提示：请在地图上打点</string> <!-- UI元素/操作指导信息：引导用户开始在地图上通过点击来添加定义地块边界的第一个或后续的顶点。 -->
    <string name="add_plot_operation_tip2">操作提示：拖动点可调整边界，打点完成后可点击保存</string> <!-- UI元素/操作指导信息：说明用户可以通过拖动已标记的点来调整地块边界，并在完成所有点的标记后点击保存按钮。 -->
    <string name="add_plot_full_screen">开启\n全屏</string> <!-- UI元素/按钮文本（含换行）：点击后将地图或画地块界面切换到全屏显示模式，以获得更大的操作区域。 -->
    <string name="add_plot_enable_full_screen">关闭\n全屏</string> <!-- UI元素/按钮文本（含换行）：点击后退出全屏显示模式，恢复到标准界面布局。通常在已开启全屏后显示此按钮。 -->
    <string name="add_plot_show_plot">显示\n地块</string> <!-- UI元素/按钮或开关文本（含换行）：用于在地图上显示当前已绘制或选中的地块边界。 -->
    <string name="add_plot_hide_plot">隐藏\n地块</string> <!-- UI元素/按钮或开关文本（含换行）：用于在地图上隐藏当前已绘制或选中的地块边界，以便查看地图背景或其他元素。 -->
    <string name="add_plot_color_setting">颜色\n设置</string> <!-- UI元素/按钮文本（含换行）：点击后打开地块边界线、填充颜色或透明度等显示样式的设置选项。 -->
    <string name="add_plot_plot_list">地块\n列表</string> <!-- UI元素/按钮文本（含换行）：点击后导航至已保存地块的列表页面，方便用户查看或选择其他地块。 -->
    <string name="add_plot_add_entry">添加\n入口</string> <!-- UI元素/工具按钮文本（含换行）：允许用户在已绘制的地块上标记一个或多个入口位置。 -->
    <string name="add_plot_delete_entry">删除\n入口</string> <!-- UI元素/工具按钮文本（含换行）：用于删除地块上已标记的某个入口位置。 -->
    <string name="add_plot_start_finding_land">开始找地边</string> <!-- UI元素/按钮文本：启动一种可能存在的半自动或自动地块边界识别功能（例如基于卫星图像边缘检测）。 -->
    <string name="add_plot_finish_finding_land">结束找地边</string> <!-- UI元素/按钮文本：结束上述的半自动或自动地块边界识别过程。 -->
    <string name="add_plot_search_satellites">正在搜索卫星请等待</string> <!-- UI元素/系统状态提示信息：当App正在尝试获取GPS卫星信号以进行定位或测量时显示的等待提示。 -->
    <string name="add_plot_layer">图层</string> <!-- UI元素/按钮或菜单项文本：点击后允许用户切换地图的显示图层，如标准地图、卫星影像图、地形图等。 -->
    <string name="add_plot_my_location">我的位置</string> <!-- UI元素/按钮文本：点击后将地图视图的中心定位到用户当前的GPS位置。 -->
    <string name="add_plot_delete">删除</string> <!-- UI元素/通用操作按钮文本：用于删除当前选中的对象，如地块边界上的一个点、一个标记、或整个未保存的地块。 -->
    <string name="add_plot_save">保存</string> <!-- UI元素/核心操作按钮文本：点击后将当前已绘制完成并确认的地块信息（边界、面积、名称等）保存到用户数据中。 -->
    <string name="add_plot_amap">高德软件有限公司</string> <!-- 技术信息/版权声明文本：指明App使用的地图服务或SDK来源于高德软件有限公司。 -->
    <string name="add_plot_tips1">请先画地块，才可以添加入口</string> <!-- UI元素/操作前置条件提示信息：告知用户在执行“添加入口”操作前，必须先完成地块边界的绘制。 -->
    <string name="add_plot_tips2">至少需要打两个点</string> <!-- UI元素/操作有效性提示信息：说明要形成一条有效的线段（用于测距或作为地块的第一条边）至少需要在地图上标记两个点。 -->
    <string name="add_plot_tips3">系统检测到未开启GPS定位服务</string> <!-- UI元素/系统状态警告信息：当某些依赖GPS定位的功能（如“我的位置”、“手持模式”测量）被尝试使用，但系统GPS服务未开启时显示。 -->
    <string name="add_plot_speak1">现在开始点击第二个点</string> <!-- 语音助手/TTS播报内容：在用户打下第一个点后，通过语音提示用户进行下一步操作，即点击标记第二个点。 -->
    <string name="add_plot_speak2">好的，现在我们得到了一条线。如果您只需要测量长度，现在就可以点击保存结束测量了。如需测量面积，请继续在地图上打点。</string> <!-- 语音助手/TTS播报内容：在用户打下第二个点形成线段后，通过语音提供当前状态反馈，并引导用户根据需求选择保存长度或继续打点形成多边形, 从而测量面积。 -->
    <string name="add_plot_speak3">非常好，当前面积</string> <!-- 语音助手/TTS播报内容前缀：在用户画地块过程中，当形成有效面积后，用作语音播报当前面积数值的前缀，通常后接面积数值和单位。例如“非常好，当前面积 5.2 亩”。 -->
    <string name="add_plot_speak4">当前面积</string> <!-- 语音助手/TTS播报内容前缀：与上一条类似，用作语音播报实时更新的面积数值的前缀。例如“当前面积 6.8 亩”。 -->
    <string name="add_plot_speak5">亩，继续在地图上绕着您要测量的地块打点，最后保存就可以了。保存后的地块可以在地块编辑里编辑地块信息和打点坐标的</string> <!-- 语音助手/TTS播报内容后缀与指导：通常接在播报的面积单位（如“亩”）之后。前半部分继续指导用户完成打点并保存，后半部分告知用户保存后的地块信息是可编辑的。 -->
    <string name="add_plot_dialog_1">网络不好，地块列表更新会不及时，请在有网络的情况下查看。</string> <!-- UI元素/对话框提示信息内容：当网络连接状况不佳时，提示用户地块列表的同步和更新可能会有延迟，建议在网络良好时查看。 -->
    <string name="add_plot_dialog_2">是</string> <!-- UI元素/对话框按钮文本：在各类确认或选择对话框中，代表肯定、同意或执行操作的选项。例如“是”、“确定”、“Yes”。 -->
    <string name="add_plot_dialog_3">否</string> <!-- UI元素/对话框按钮文本：在各类确认或选择对话框中，代表否定、不同意或取消操作的选项。例如“否”、“取消”、“No”。 -->
    <string name="add_plot_dialog_4">是否放弃当前操作</string> <!-- UI元素/对话框标题或内容：用于询问用户是否要中止当前正在进行但尚未保存的操作（如画地块、编辑信息等）。 -->

    <!-- 通用设置或地图选项相关文本 -->
    <string name="text_transparency">透明度</string> <!-- UI元素/设置项标签或标题：用于调整地图上地块、图层或其他元素的显示透明度。 -->
    <string name="text_color">颜色</string> <!-- UI元素/设置项标签或标题：用于选择或更改地图上地块、图层或其他元素的显示颜色。 -->
    <string name="text_confirm">确定</string> <!-- UI元素/通用按钮文本：在对话框、设置界面等处，表示确认当前的选择、输入或操作。 -->
    <string name="text_cancel">取消</string> <!-- UI元素/通用按钮文本：在对话框、设置界面等处，表示取消当前的操作或关闭当前界面而不保存更改。 -->
    <string name="text_earth_map">地球影像</string> <!-- UI元素/地图类型选项文本：选择显示卫星或航空拍摄的真实地球表面影像作为地图背景。通常称为“卫星地图”或“影像图”。 -->
    <string name="text_standard_map">标准地图</string> <!-- UI元素/地图类型选项文本：选择显示包含街道、行政区划、兴趣点等要素的标准矢量地图作为背景。 -->
    <string name="text_star_map">星图影像</string> <!-- UI元素/地图图层选项文本：指由“中科星图”公司提供的特定卫星影像地图图层。 -->
    <string name="shoujiGPSyilianjei">手机北斗已连接</string> <!-- UI元素/状态提示信息：表示App已成功连接并使用手机内置的北斗卫星导航系统进行定位。 (注：“北斗”为中国特定的卫星系统，国际化时可能需考虑通用GPS或GNSS的表述) -->
    <string name="current_length">当前长度</string> <!-- UI元素/数据显示标签：在进行距离测量或地块周长测量时，实时显示当前已测量的长度值。 -->
    <string name="satellites_num">卫星数量</string> <!-- UI元素/数据显示标签：显示当前GPS/GNSS接收器搜索到并用于定位的卫星颗数。 -->
    <string name="satellites_unit">颗</string> <!-- 数量单位/UI文本：用于“卫星数量”的计量单位。 -->
    <string name="mobile_tips1">操作提示：点击结束，保存地块。</string> <!-- UI元素/操作指导信息：在“手持模式”或类似通过行走轨迹测量地块的模式下，提示用户完成行走后，点击“结束”按钮以保存测量得到的地块数据。 -->
    <string name="mobile_tips2">(请尽量亮屏操作)</string> <!-- UI元素/操作建议信息（含括号）：建议用户在进行GPS相关的测量操作时保持手机屏幕常亮，以确保GPS信号的持续接收和测量的准确性。 -->
    <string name="start">开始</string> <!-- UI元素/通用操作按钮文本：用于启动一项测量任务、记录过程或其他操作。 -->
    <string name="pause">暂停</string> <!-- UI元素/通用操作按钮文本：用于暂停当前正在进行的测量任务、记录过程或其他操作，通常可以后续选择“继续”或“结束”。 -->

    <string name="no_location_title">没有开启定位权限，定位当前位置失败</string> <!-- UI元素/错误提示标题或信息：当App因缺少定位权限或定位服务未开启，导致无法获取用户当前位置时显示的提示。 -->
    <string name="canyin">餐饮</string> <!-- 支出类别选项/UI标签：在记账功能中，用于将支出归类为“餐饮”费用。 -->
    <string name="zhusu">住宿</string> <!-- 支出类别选项/UI标签：在记账功能中，用于将支出归类为“住宿”费用。 -->
    <string name="weixiu">维修保养</string> <!-- 支出类别选项/UI标签：在记账功能中，用于将支出归类为农业机械、设备等的“维修保养”费用。 -->
    <string name="you">油</string> <!-- 支出类别选项/UI标签：在记账功能中，用于将支出归类为农业机械、车辆等使用的“燃油”（如汽油、柴油）费用。 -->
    <string name="wuliu">物流吊装</string> <!-- 支出类别选项/UI标签：在记账功能中，用于将支出归类为农产品运输、设备搬运吊装等“物流吊装”相关的费用。 -->
    <string name="rengong">人工</string> <!-- 支出类别选项/UI标签：在记账功能中，用于将支出归类为雇佣劳动力所支付的“人工”成本。 -->
    <string name="qita">其他</string> <!-- 支出/收入类别选项/UI标签：在记账功能中，用于归类不属于预设类别的其他各项“支出”或“收入”。 -->   
    <string name="zidingyibiaoti">自定义标题</string> <!-- UI元素/输入框标签或功能描述：允许用户为某条支出或收入记录，或者其他项目（如标记点），输入一个自定义的标题或名称。 -->
    <string name="qitashouru">其他收入</string> <!-- 收入类别选项/UI标签：在记账功能中，用于将收入归类为不属于预设类别的“其他收入”。 -->
    <string name="riqi">日期</string> <!-- UI元素/标签或数据显示字段：用于显示或选择某条记录（如支出、收入、测量等）发生的“日期”。 -->

    <string name="meiyou"> </string> <!-- UI占位符/空状态文本：表示此处没有内容或数据。其值是一个空格，用于在界面上占据一定空间或表示空状态。 -->

    <string name="yingjianshebeiyilianjie">硬件设备已连接</string> <!-- UI元素/状态提示信息：告知用户外部硬件设备（如高精度GPS接收器或其他辅助测量设备）已成功连接到App。 -->

    <string name="pay">支出</string> <!-- 财务功能/UI标签或分类：指农业经营活动中发生的各类成本或费用。 -->
    <string name="income">收入</string> <!-- 财务功能/UI标签或分类：指农业经营活动中获得的各类收益或进账。 -->
    
    <string name="rationale_location_all">此应用需要定位等权限，同意吗？</string> <!-- UI元素/权限请求对话框文本：在App请求定位及其他相关权限前，向用户说明权限用途并征求同意的提示信息。 -->
    <string name="quit">退出应用</string> <!-- UI元素/操作按钮或菜单项文本：用户点击后会完全关闭并退出当前应用程序。 -->
    <string name="quit_confirmed">退出应用</string> <!-- UI元素/确认对话框按钮文本：当用户选择退出应用时，为防止误操作而弹出的二次确认对话框中的确认按钮文本。其值与上一个“退出应用”相同，但上下文是二次确认。 -->
    <string name="term_of_use_text_1">欢迎使用测亩易，我们非常重视您的个人信息和隐私保护。</string> <!-- UI元素/法律条款文本片段：通常作为用户协议或隐私政策的开篇欢迎语，并强调对用户个人信息和隐私的保护。 (注：“测亩易”在此处应与app_name的实际显示保持一致或按需调整) -->
    <string name="term_of_use_text_2">在您使用测亩易服务之前，请仔细阅读《用户协议》和《隐私政策》我们将严格按照您同意的各项条款使用您的个人信息，以便为您提供更好的服务。</string> <!-- UI元素/法律条款文本片段：提示用户在使用App服务前应仔细阅读完整的《用户协议》和《隐私政策》，并申明将按约定使用用户信息以提供服务。 (注：中文书名号《》在翻译时需适配目标语言规范) -->
    <string name="term_of_use_second_confirmation_text_1">您需要同意隐私保护指引才能使用测亩易。如果您不同意，很遗憾我们无法为您提供服务。</string> <!-- UI元素/法律条款文本片段或强制同意提示：明确告知用户必须同意相关的隐私保护指引才能继续使用App，否则无法提供服务。 -->
    <string name="term_of_use_second_confirmation_text_2">您可以通过阅读完整版《用户协议》和《隐私政策》来了解详细信息。</string> <!-- UI元素/法律条款文本片段或引导提示：引导用户去阅读完整版本的《用户协议》和《隐私政策》以获取更详细的信息。 (注：中文书名号《》在翻译时需适配目标语言规范) -->

    <string name="wuqunzu">无分组</string> <!-- UI元素/状态标签或列表项文本：表示某个项目（如地块、记录等）没有被分配到任何用户自定义的分组或类别中。 -->
    <string name="changjianwenti">常见问题</string> <!-- UI元素/帮助功能入口文本（如链接或菜单项）：点击后通常会导航到包含用户经常遇到的问题及其解答的页面（FAQ）。 -->

    <string name="jiaoyishouru">交易收入</string> <!-- 财务功能/收入类别选项或标签：特指通过农产品销售、土地租赁等市场交易行为获得的收入。 -->

    <string name="transparency70">#4c</string> <!-- 技术参数/颜色值：一个十六进制的颜色代码，表示带有约70%透明度的某种颜色（具体颜色取决于前两位，这里#4c是透明度值）。此字符串通常不直接显示给用户，而是由程序用于UI元素的颜色渲染，翻译时一般保持原样。 -->
    <string name="zouyiquan">走一圈</string> <!-- UI元素/测量方式按钮或标签：与“手持模式”(`zidong`)或`home_to_auto`类似，指用户通过持设备沿地块边界行走，App自动记录GPS轨迹来测量地块面积和周长的功能。 -->
    <string name="xiaoyicemu">小易测亩</string> <!-- 功能名称/宣传语：可能是App内一个特定、简化或快捷的土地面积（亩）测量功能模块的名称，或者是一个品牌相关的宣传短语。 (注：“小易”可能与App品牌相关) -->

    <string name="evaluation">去评价</string> <!-- UI元素/操作按钮或链接文本：引导用户前往应用商店（如Google Play, App Store）或其他反馈渠道对本App进行评分和提交用户评价。 -->
    <string name="already_evaluation">已评价</string> <!-- UI元素/状态显示文本：表明当前用户已经对本App完成过评价操作。 -->

    <!-- 存储,摄像头 -->
    <string name="update_photo_tips">存储/摄像头权限使用说明：用于相片的拍照、存储、裁剪功能。</string> <!-- UI元素/权限说明文本：解释App请求“存储”和“摄像头”权限的原因，主要用于支持用户拍摄照片、从设备存储中选择照片、保存编辑后的照片以及进行图片裁剪等操作。 -->
    <string name="update_camera_photo_tips">相机权限使用说明：用于获取拍照功能。</string> <!-- UI元素/权限说明文本：专门解释App请求“相机”权限的原因，即为了能够使用设备的摄像头进行拍照。 -->
    <string name="update_camera_Gallery_tips">存储权限使用说明：用于获取相册功能。</string> <!-- UI元素/权限说明文本：专门解释App请求“存储”权限的原因之一，即为了能够访问设备相册（图库）以供用户选择图片。 -->
    <!-- 位置，存储,摄像头 -->
    <string name="camera_location_tips">存储/摄像头/位置权限使用说明：用于获取当前定位、相册以及拍照等功能。</string> <!-- UI元素/权限说明文本：综合解释App请求“存储”、“摄像头”和“位置”这三项权限的原因，说明它们组合起来用于实现获取用户当前地理位置、访问相册以及进行拍照等多种功能。 -->
    <!-- 位置 -->
    <string name="weather_location_tips">位置权限使用说明：用于获取当前位置信息进而获取天气的功能。</string> <!-- UI元素/权限说明文本：解释App请求“位置”权限的一个特定用途，即获取用户当前位置信息以便提供对应的天气预报服务。 -->
    <!-- 存储 -->
    <string name="heat_location_tips">存储权限使用说明：用于获取当前图层显示功能。</string> <!-- UI元素/权限说明文本：解释App请求“存储”权限的一个特定用途，可能是为了加载、缓存或显示地图上的热力图或其他自定义图层数据。 -->
    <!-- 通讯录,联系人 -->
    <string name="contact_tips">联系人/通讯录权限使用说明：用于获取当前联系人功能。</string> <!-- UI元素/权限说明文本：解释App请求访问用户“联系人”或“通讯录”权限的原因，通常用于分享信息给联系人、邀请好友或从联系人中选择某些信息等功能。 -->
    <!-- 应用列表 -->
    <string name="get_package_tips">应用列表权限使用说明：用于操作支付时获取是否已安装相应支付软件功能。</string> <!-- UI元素/权限说明文本：解释App请求获取用户设备上“已安装应用列表”权限的原因，特定用于在进行支付操作时，检查用户是否已安装了必要的第三方支付应用（如微信支付、支付宝等）。 -->

    <string name="no_location_content">获取定位权限会定位到你的当前位置，获取更好的用户体验。</string> <!-- UI元素/权限说明或提示信息文本：进一步解释App请求定位权限的好处，即通过定位到用户当前位置来优化App功能，提升用户体验。 -->
    <string name="jump_wx">即将打开微信APP，是否允许？</string> <!-- UI元素/跳转确认对话框文本：当App需要跳转到微信应用（例如进行支付或分享）之前，向用户弹出的确认提示。 -->
    <string name="jump_ali">即将打开支付宝APP，是否允许？</string> <!-- UI元素/跳转确认对话框文本：当App需要跳转到支付宝应用（例如进行支付）之前，向用户弹出的确认提示。 -->
    <string name="jump_phone">即将打开电话页面，是否允许？</string> <!-- UI元素/跳转确认对话框文本：当App需要打开系统的拨号或电话应用（例如拨打客服电话）之前，向用户弹出的确认提示。 -->
    <string name="add_plot_no_data">没有搜索到该地块</string> <!-- UI元素/搜索结果提示信息：在用户使用地块搜索功能后，如果未能找到与搜索条件匹配的地块，则显示此提示。 -->
    
    <string name="language">语言</string> <!-- UI元素/设置项标签或页面标题：指App界面显示语言的选择设置。 -->
    <string name="language_zh">中文</string> <!-- UI元素/语言选项文本：在语言设置中，代表选择“中文”作为App界面语言的选项。 -->
    <string name="language_en">英语</string> <!-- UI元素/语言选项文本：在语言设置中，代表选择“英语”作为App界面语言的选项。 -->

    <string name="term_of_use_title">用户协议及隐私政策</string> <!-- UI元素/页面标题或文档标题：用于显示App的《用户协议》和《隐私政策》的页面或部分的标题。 -->
    <string name="sure">同意</string> <!-- UI元素/通用操作按钮文本：表示用户确认、接受或同意某个条款、操作或提示。例如，在用户协议确认场景下表示同意协议内容。 -->
    <string name="input_phone">请输入手机号</string> <!-- UI元素/输入框占位符文本(placeholder)：提示用户在此输入框中输入手机号码。 -->
    <string name="input_code">请输入验证码</string> <!-- UI元素/输入框占位符文本(placeholder)：提示用户在此输入框中输入收到的短信验证码。 -->
    <string name="get_code">获取验证码</string> <!-- UI元素/操作按钮文本：用户点击此按钮以请求系统发送短信验证码到其手机。 -->
    <string name="is_read">已阅读并同意</string> <!-- UI元素/复选框标签文本：通常与一个复选框配合使用，表示用户声明已阅读并同意相关条款（如用户协议和隐私政策）。 -->
    <string name="user_agreement">《用户协议》</string> <!-- UI元素/可点击链接文本或文档名称：指App的《用户协议》文档。通常可点击以查看详细内容。(注：中文书名号《》在翻译时需适配目标语言规范) -->
    <string name="and">和</string> <!-- 连接词/UI文本：用于连接两个并列的词语或短语，例如连接“《用户协议》”和“《隐私政策》”。 -->
    <string name="privacy_policy">《隐私政策》</string> <!-- UI元素/可点击链接文本或文档名称：指App的《隐私政策》文档。通常可点击以查看详细内容。(注：中文书名号《》在翻译时需适配目标语言规范) -->
    <string name="login">立即登录</string> <!-- UI元素/操作按钮文本：用户点击此按钮以执行登录操作。 -->
    <string name="is_login">正在登录</string> <!-- UI元素/状态提示信息文本：表示App当前正在处理用户的登录请求。 -->
    <string name="is_save">正在保存</string> <!-- UI元素/状态提示信息文本：表示App当前正在处理数据保存操作。 -->
    <string name="is_load">正在加载</string> <!-- UI元素/状态提示信息文本：表示App当前正在加载数据或内容。 -->

    <string name="copyright_info">联农创世    版权所有</string> <!-- UI元素/版权信息文本：显示App的版权归属方“联农创世”。(注：公司名称通常不翻译，多个空格用于排版，翻译时可能需要调整) -->
    <string name="network_unsupported_use_sms">当前网络环境不支持认证,请使用验证码登录</string> <!-- UI元素/网络状态或登录方式提示：告知用户因当前网络环境问题（可能指无法进行一键登录认证），需要转而使用短信验证码的方式进行登录。 -->
    <string name="check_mobile_data">检查移动网络是否打开</string> <!-- UI元素/操作建议或错误排查提示：提醒用户检查其设备的移动数据网络是否已开启，通常在网络相关功能失败时显示。 -->
    <string name="resend_countdown_prefix">还剩</string> <!-- UI元素/倒计时文本前缀：用于显示重新发送验证码的倒计时秒数之前的部分，例如：“还剩 60 秒”。 -->
    <string name="resend_countdown_suffix">秒</string> <!-- UI元素/倒计时文本后缀：用于显示重新发送验证码的倒计时秒数之后的部分，即单位“秒”。 -->
    <string name="resend_verification">重新发送</string> <!-- UI元素/操作按钮文本：用户点击此按钮以再次请求发送短信验证码，通常在首次发送失败或验证码过期后可用。 -->
    <string name="phone_required">手机号不能为空</string> <!-- UI元素/表单验证错误提示：当用户尝试提交表单（如登录、注册）但未填写手机号码时显示的错误信息。 -->
    <string name="sms_sent_success">验证码发送成功</string> <!-- UI元素/操作成功状态提示：告知用户短信验证码已成功发送至其手机。 -->
    <string name="sms_not_expired">验证码未过期</string> <!-- UI元素/状态提示信息：告知用户当前已获取的短信验证码仍在有效期内，无需重复请求。 -->
    <string name="sms_send_failure">验证码发送失败</string> <!-- UI元素/操作失败状态提示：告知用户短信验证码未能成功发送。 -->
    <string name="sms_retry_after_expiry">请验证码过时后再点击</string> <!-- UI元素/操作限制提示：告知用户需要在当前验证码失效（过期）后才能再次点击获取新的验证码。 -->
    <string name="invalid_phone_format">请输入正确的手机号</string> <!-- UI元素/表单验证错误提示：当用户输入的手机号码格式不符合规范时显示的错误信息。 -->
    <string name="credentials_required">手机号或者验证码不能为空</string> <!-- UI元素/表单验证错误提示：当用户尝试登录但手机号或验证码至少有一项未填写时显示的错误信息。 -->
    <string name="user_not_found">用户不存在</string> <!-- UI元素/登录错误提示：当用户尝试使用一个未注册或不存在的手机号码登录时显示的错误信息。 -->
    <string name="sms_expired">验证码过期</string> <!-- UI元素/验证错误提示：当用户输入的短信验证码已超过其有效期限时显示的错误信息。 -->
    <string name="sms_incorrect">验证码不正确</string> <!-- UI元素/验证错误提示：当用户输入的短信验证码与系统发送的不匹配时显示的错误信息。 -->
    <string name="login_failed">登录失败</string> <!-- UI元素/通用操作失败提示：表示用户登录尝试未能成功，可能由多种原因导致。 -->
    <string name="network_timeout">网络超时，请稍后在试</string> <!-- UI元素/网络错误提示：告知用户由于网络连接超时导致当前操作失败，建议稍后重试。(注：原文“在试”应为“再试”) -->
    <string name="agreement_required">请查看用户协议和隐私政策并勾选</string> <!-- UI元素/表单验证错误提示：当用户尝试进行需要同意协议的操作（如注册、登录）但未勾选同意用户协议和隐私政策的复选框时显示的错误信息。 -->
    <string name="sms_send_error">短信验证码发送失败</string> <!-- UI元素/操作失败状态提示：与`sms_send_failure`类似，但可能用于更具体的发送错误场景或作为通用短信发送错误提示。 -->
    <string name="carrier_issue_use_sms">因运营商问题，请使用验证码登录</string> <!-- UI元素/特殊情况说明提示：告知用户由于其手机运营商方面的问题（可能影响一键登录等认证方式），建议使用短信验证码进行登录。 -->
    <string name="enable_data_for_one_click">请启用移动数据后\n再使用一键登录功能</string> <!-- UI元素/操作前置条件提示（含换行）：告知用户在使用“一键登录”功能前，必须先确保其设备的移动数据网络已开启。 -->
    <string name="sms_login_title">验证码登录</string> <!-- UI元素/页面标题：用于短信验证码登录页面的主标题。 -->

    <string name="plot_name_required">地块名称不能为空</string> <!-- UI元素/表单验证错误提示：在保存地块信息时，如果用户未填写地块名称，则显示此错误信息。 -->
    <string name="negotiated_area_required">协商面积不能为空</string> <!-- UI元素/表单验证错误提示：在保存地块信息时，如果“协商面积”字段（可能是一个用户可编辑的、与实际测量面积不同的约定面积）未填写，则显示此错误信息。 -->
    <string name="other_settings">其他设置</string> <!-- UI元素/设置分类标题或入口：指向App内其他非主要或杂项的设置选项。 -->
    <string name="save_confirmation">是否要保存</string> <!-- UI元素/确认对话框标题或内容：在用户执行可能导致数据更改的操作（如编辑地块信息后退出）时，询问用户是否要保存这些更改。 -->
    <string name="enable_gps_prompt">请开启GPS导航...</string> <!-- UI元素/系统功能引导提示：提醒或引导用户开启设备的GPS定位服务（“导航”在此泛指定位功能，省略号“...”表示一种引导语气）。 -->
    <string name="mark_first_prompt">请先打一个标记</string> <!-- UI元素/操作顺序提示信息：在执行某些需要先有标记点才能进行的操作前（例如某些特定测量或编辑功能），如果用户尚未添加任何标记，则显示此提示。 -->
    <string name="first_mark_instruction">请在您要测量的地块边缘点击第一个标记点，如果没有点准，您可以撤回或者按住标记点，用手指调整位置</string> <!-- UI元素/详细操作指导文本：为用户提供关于如何在地图上添加和精确调整第一个测量标记点的详细步骤说明，包括撤销和拖动调整的操作。 -->

</resources>