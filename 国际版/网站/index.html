<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UNITED F&G (HK) AGRICULTURAL TECHNOLOGY CO., LIMITED - Digital Agriculture Expert</title>
    <meta name="description" content="UNITED F&G (HK) focuses on digital agriculture, providing intelligent solutions for agricultural production organizations and agricultural machinery manufacturers. Advanced agricultural technology solutions.">
    
    <!-- TailwindCSS 3.0+ -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Framer Motion -->
    <script src="https://unpkg.com/framer-motion@11/dist/framer-motion.js"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    
    <!-- Chart.js for data visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            overflow-x: hidden;
        }
        
        /* 超大字体样式 */
        .hero-company-name {
            font-size: clamp(2rem, 6vw, 4rem);
            font-weight: 800;
            line-height: 1.2;
        }
        
        .hero-title {
            font-size: clamp(4rem, 12vw, 10rem);
            font-weight: 900;
            line-height: 0.9;
            background: linear-gradient(135deg, #3B5998 0%, #2d4373 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .section-title {
            font-size: clamp(3rem, 8vw, 6rem);
            font-weight: 800;
            line-height: 1.1;
        }
        
        .big-number {
            font-size: clamp(4rem, 10vw, 8rem);
            font-weight: 900;
            line-height: 1;
            background: linear-gradient(135deg, #3B5998 0%, #2d4373 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        /* Bento Grid 卡片样式 */
        .bento-card {
            border: 1px solid rgba(229, 231, 235, 0.3);
            border-radius: 1.5rem;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            z-index: 1;
        }
        
        .bento-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(59, 89, 152, 0.05) 0%, transparent 50%);
            opacity: 0;
            transition: opacity 0.6s ease;
            z-index: -1;
        }
        
        .bento-card:hover::before {
            opacity: 1;
        }
        
        .bento-card:hover,
        .bento-card.hovered {
            transform: translateY(-12px) scale(1.02);
            box-shadow: 0 32px 64px -12px rgba(59, 89, 152, 0.2);
            border-color: rgba(59, 89, 152, 0.3);
            z-index: 10;
        }
        
        /* 高亮色渐变背景 */
        .highlight-gradient {
            background: linear-gradient(135deg, rgba(59, 89, 152, 0.15) 0%, rgba(59, 89, 152, 0.05) 100%);
        }
        
        .success-gradient {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(16, 185, 129, 0.05) 100%);
        }
        
        .tech-gradient {
            background: linear-gradient(135deg, rgba(139, 92, 246, 0.15) 0%, rgba(139, 92, 246, 0.05) 100%);
        }
        
        .warning-gradient {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.15) 0%, rgba(245, 158, 11, 0.05) 100%);
        }
        
        .rose-gradient {
            background: linear-gradient(135deg, rgba(236, 72, 153, 0.15) 0%, rgba(236, 72, 153, 0.05) 100%);
        }
        
        .emerald-gradient {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(16, 185, 129, 0.05) 100%);
        }
        
        /* 导航栏样式 */
        .nav-link {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }
        
        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 0;
            height: 2px;
            background: #3B5998;
            transition: width 0.3s ease;
        }
        
        .nav-link:hover::after {
            width: 100%;
        }
        
        .nav-link:hover {
            color: #3B5998;
            transform: translateY(-1px);
        }
        
        /* CTA按钮样式 */
        .cta-button {
            background: linear-gradient(135deg, #E31937 0%, #c41530 100%);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        
        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s;
        }
        
        .cta-button:hover::before {
            left: 100%;
        }
        
        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 20px 40px -8px rgba(227, 25, 55, 0.4);
        }
        
        /* Apple式滚动动效 */
        .parallax-bg {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            position: relative;
            overflow: hidden;
            z-index: 1;
        }
        
        .parallax-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233B5998' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            animation: float 20s ease-in-out infinite;
            z-index: -1;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        /* 滚动指示器 */
        .scroll-indicator {
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% {
                transform: translateY(0);
            }
            40%, 43% {
                transform: translateY(-15px);
            }
            70% {
                transform: translateY(-8px);
            }
            90% {
                transform: translateY(-3px);
            }
        }
        
        /* 证书图片样式 */
        .certificate-img {
            max-height: 280px;
            object-fit: contain;
            border-radius: 16px;
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
            transition: transform 0.4s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .certificate-img:hover {
            transform: scale(1.05) rotate(1deg);
        }
        
        /* 技术图标样式 */
        .tech-icon {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }
        
        .tech-card:hover .tech-icon {
            transform: scale(1.1);
        }
        
        /* Logo样式 */
        .logo-img {
            max-height: 80px;
            object-fit: contain;
            transition: transform 0.3s ease;
        }
        
        .logo-img:hover {
            transform: scale(1.05);
        }
        
        /* 技术卡片样式 */
        .tech-card {
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
        }
        
        .tech-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px -12px rgba(59, 89, 152, 0.2);
        }
        
        /* 成功案例图片 */
        .case-image {
            width: 100%;
            border-radius: 16px;
            transition: transform 0.4s ease;
        }
        
        .case-image:hover {
            transform: scale(1.02);
        }
        
        /* 农田识别对比图样式 */
        .recognition-image {
            width: 100%;
            height: auto;
            max-height: 400px;
            object-fit: cover;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            transition: all 0.4s ease;
        }
        
        .recognition-container {
            position: relative;
            overflow: hidden;
            border-radius: 16px;
            background: #f8fafc;
        }
        
        .recognition-container:hover .recognition-image {
            transform: scale(1.03);
        }
        
        /* 识别标签样式 */
        .recognition-label {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        /* 数据可视化容器 */
        .chart-container {
            position: relative;
            height: 300px;
            padding: 2rem;
        }
        
        /* 勾线图形样式 */
        .line-graphic {
            stroke: #3B5998;
            stroke-width: 2;
            fill: none;
            stroke-dasharray: 1000;
            stroke-dashoffset: 1000;
            animation: draw 3s ease-in-out forwards;
        }
        
        @keyframes draw {
            to {
                stroke-dashoffset: 0;
            }
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .hero-title {
                font-size: clamp(3rem, 8vw, 6rem);
            }
            
            .big-number {
                font-size: clamp(3rem, 8vw, 5rem);
            }
        }
        
        /* 毛玻璃效果 */
        .glass-effect {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        /* 确保section层级正确 */
        section {
            position: relative;
            z-index: 2;
            background: transparent;
        }
        
        /* 非parallax背景的section使用纯色背景 */
        section:not(.parallax-bg) {
            background: #ffffff;
        }
        
        /* 确保导航栏在最上层 */
        nav {
            z-index: 100;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-gray-100 text-gray-800">
    <!-- Navigation -->
    <nav class="fixed top-0 w-full glass-effect z-50 border-b border-white/20">
        <div class="max-w-7xl mx-auto px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <div class="flex items-center">
                </div>
                <div class="hidden md:flex space-x-8 items-center">
                </div>
                <!-- 移动端菜单按钮 -->
                <div class="md:hidden">
                    <button class="text-gray-700 hover:text-gray-900">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="min-h-screen flex items-center justify-center pt-20 px-6 lg:px-8 parallax-bg relative">
        <div class="max-w-7xl mx-auto text-center" data-aos="fade-up" data-aos-duration="1000">
            <div class="mb-16">
                <!-- Company Name -->
                <h1 class="hero-company-name text-gray-900 mb-12">
                    UNITED F&G (HK) AGRICULTURAL TECHNOLOGY CO., LIMITED
                </h1>
                
                <!-- Core Positioning - Extra Large Font Emphasis -->
                <div class="mb-16">
                    <div class="hero-title mb-6">Digital Agriculture Expert</div>
                    <p class="text-2xl text-gray-500 uppercase tracking-wider font-light">Leading Agricultural Technology Solutions</p>
                </div>
                

                

            </div>
            
            <!-- Scroll Indicator -->
            <div class="scroll-indicator" data-aos="fade-up" data-aos-delay="800">
                <i class="fas fa-chevron-down text-4xl text-gray-400"></i>
            </div>
        </div>
        
        <!-- 背景装饰元素 -->
        <div class="absolute top-1/4 left-10 w-20 h-20 border-2 border-blue-200 rounded-full opacity-30"></div>
        <div class="absolute bottom-1/4 right-10 w-16 h-16 border-2 border-green-200 rounded-full opacity-30"></div>
    </section>



    <!-- Core Technology Capabilities -->
    <section class="py-32 px-6 lg:px-8 parallax-bg">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-24" data-aos="fade-up">
                <h2 class="section-title text-gray-900 mb-8">Core Technology Capabilities</h2>
                <p class="text-3xl text-gray-600 font-light">Advanced Agricultural Intelligence Solutions</p>
            </div>
            
            <!-- Large Model AI Technology - Special Layout for Multi-Scenario Applications -->
            <div class="mb-12">
                <div class="bento-card tech-card tech-gradient" data-aos="fade-up" data-aos-delay="100">
                    <div class="p-10">
                        <!-- Title and Description -->
                        <div class="flex items-center justify-center mb-8">
                            <div class="flex-shrink-0 mr-8">
                                <i class="fas fa-brain tech-icon text-[#8B5CF6]"></i>
                            </div>
                            <div class="text-center">
                                <h3 class="text-4xl font-bold text-gray-900 mb-4">Large Model AI Technology</h3>
                                <p class="text-2xl text-gray-600 mb-2">Intelligent Agricultural Q&A and Decision Support</p>
                                <p class="text-lg text-gray-500">AI-Powered Agricultural Intelligence</p>
                            </div>
                        </div>
                        
                        <!-- AI Application Scenarios Display -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
                            <!-- Disease and Pest Recognition -->
                            <div class="relative group">
                                <div class="text-center mb-4">
                                    <span class="recognition-label inline-block px-6 py-2 bg-purple-50 text-purple-700 rounded-full text-base font-semibold shadow-lg border-purple-200">Disease & Pest Recognition</span>
                                    <p class="text-sm text-gray-500 mt-2 font-medium">Intelligent Diagnosis System</p>
                                </div>
                                <div class="recognition-container">
                                    <img src="pics/pest_disease_recognition.png" alt="Disease & Pest Recognition AI" class="recognition-image w-full h-auto aspect-[828/1166] max-h-[500px]">
                                    <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                    <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        <i class="fas fa-search-plus text-white text-lg bg-black/50 p-2 rounded-full"></i>
                                    </div>
                                    <div class="absolute bottom-4 left-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        <span class="bg-purple-500 text-white px-3 py-1 rounded-full text-xs font-semibold">
                                            <i class="fas fa-bug mr-1"></i>Smart Diagnosis
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Agricultural Machinery AI -->
                            <div class="relative group">
                                <div class="text-center mb-4">
                                    <span class="recognition-label inline-block px-6 py-2 bg-blue-50 text-blue-700 rounded-full text-base font-semibold shadow-lg border-blue-200">Machinery Expert</span>
                                    <p class="text-sm text-gray-500 mt-2 font-medium">Equipment Maintenance & Support</p>
                                </div>
                                <div class="recognition-container">
                                    <img src="pics/agricultural_machine_ai.jpg" alt="Agricultural Machinery AI Expert" class="recognition-image w-full h-auto aspect-[828/1166] max-h-[500px]">
                                    <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                    <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        <i class="fas fa-search-plus text-white text-lg bg-black/50 p-2 rounded-full"></i>
                                    </div>
                                    <div class="absolute bottom-4 left-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-semibold">
                                            <i class="fas fa-tractor mr-1"></i>Maintenance
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Planting Expert -->
                            <div class="relative group">
                                <div class="text-center mb-4">
                                    <span class="recognition-label inline-block px-6 py-2 bg-green-50 text-green-700 rounded-full text-base font-semibold shadow-lg border-green-200">Planting Expert</span>
                                    <p class="text-sm text-gray-500 mt-2 font-medium">Cultivation Guidance</p>
                                </div>
                                <div class="recognition-container">
                                    <img src="pics/planting_expert_ai.png" alt="Planting Expert AI" class="recognition-image w-full h-auto aspect-[828/1166] max-h-[500px]">
                                    <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                    <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        <i class="fas fa-search-plus text-white text-lg bg-black/50 p-2 rounded-full"></i>
                                    </div>
                                    <div class="absolute bottom-4 left-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        <span class="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-semibold">
                                            <i class="fas fa-seedling mr-1"></i>Planting Advice
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- AI Technology Features -->
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 text-center">
                            <div class="flex flex-col items-center">
                                <i class="fas fa-comments text-2xl text-[#8B5CF6] mb-3"></i>
                                <p class="text-lg font-semibold text-gray-800">Intelligent Q&A</p>
                                <p class="text-sm text-gray-600">Smart Conversation</p>
                            </div>
                            <div class="flex flex-col items-center">
                                <i class="fas fa-eye text-2xl text-[#8B5CF6] mb-3"></i>
                                <p class="text-lg font-semibold text-gray-800">Image Recognition</p>
                                <p class="text-sm text-gray-600">Visual Analysis</p>
                            </div>
                            <div class="flex flex-col items-center">
                                <i class="fas fa-lightbulb text-2xl text-[#8B5CF6] mb-3"></i>
                                <p class="text-lg font-semibold text-gray-800">Decision Support</p>
                                <p class="text-sm text-gray-600">Strategic Guidance</p>
                            </div>
                            <div class="flex flex-col items-center">
                                <i class="fas fa-graduation-cap text-2xl text-[#8B5CF6] mb-3"></i>
                                <p class="text-lg font-semibold text-gray-800">Expert Knowledge</p>
                                <p class="text-sm text-gray-600">Professional Expertise</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- IoT System - Full Width Display -->
            <div class="mb-12">
                <!-- IoT System - Special Layout for Wide Image -->
                <div class="bento-card tech-card highlight-gradient" data-aos="fade-up" data-aos-delay="200">
                    <div class="p-10">
                        <!-- Title and Description -->
                        <div class="flex items-center justify-center mb-8">
                            <div class="flex-shrink-0 mr-8">
                                <i class="fas fa-network-wired tech-icon text-[#3B5998]"></i>
                            </div>
                            <div class="text-center">
                                <h3 class="text-4xl font-bold text-gray-900 mb-4">IoT System</h3>
                                <p class="text-2xl text-gray-600 mb-2">Device Monitoring & Data Collection</p>
                                <p class="text-lg text-gray-500">Real-time Agricultural Intelligence</p>
                            </div>
                        </div>
                        
                        <!-- IoT System Display -->
                        <div class="relative group mb-8">
                            <div class="text-center mb-6">
                                <span class="recognition-label inline-block px-8 py-3 bg-blue-50 text-blue-700 rounded-full text-lg font-semibold shadow-lg border-blue-200">Complete IoT Solution</span>
                                <p class="text-base text-gray-500 mt-3 font-medium">Integrated Monitoring Platform</p>
                            </div>
                            <div class="recognition-container">
                                <img src="pics/iot_system.png" alt="Complete IoT System Solution" class="w-full h-auto object-contain rounded-2xl shadow-lg">
                                <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                    <i class="fas fa-search-plus text-white text-xl bg-black/50 p-3 rounded-full"></i>
                                </div>
                                <!-- Left APP Interface Label -->
                                <div class="absolute top-4 left-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                    <span class="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-semibold">
                                        <i class="fas fa-mobile-alt mr-2"></i>Data Monitor Interface
                                    </span>
                                </div>
                                <!-- Right Device Label -->
                                <div class="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                    <span class="bg-green-500 text-white px-4 py-2 rounded-full text-sm font-semibold">
                                        <i class="fas fa-microchip mr-2"></i>Smart Monitor
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- System Features -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                            <div class="flex flex-col items-center">
                                <i class="fas fa-chart-bar text-2xl text-[#3B5998] mb-3"></i>
                                <p class="text-lg font-semibold text-gray-800">Real-time Data Monitoring</p>
                                <p class="text-sm text-gray-600">Live Performance Tracking</p>
                            </div>
                            <div class="flex flex-col items-center">
                                <i class="fas fa-wifi text-2xl text-[#3B5998] mb-3"></i>
                                <p class="text-lg font-semibold text-gray-800">Wireless Data Transmission</p>
                                <p class="text-sm text-gray-600">Seamless Connectivity</p>
                            </div>
                            <div class="flex flex-col items-center">
                                <i class="fas fa-cogs text-2xl text-[#3B5998] mb-3"></i>
                                <p class="text-lg font-semibold text-gray-800">Smart Device Management</p>
                                <p class="text-sm text-gray-600">Intelligent Control</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Agricultural Field Recognition - Full Width Display -->
            <div class="mb-12">
                
                <!-- Agricultural Field Recognition - Special Layout for Comparison -->
                <div class="bento-card tech-card success-gradient" data-aos="fade-up" data-aos-delay="300">
                    <div class="p-10">
                        <!-- Title and Description -->
                        <div class="flex items-center justify-center mb-8">
                            <div class="flex-shrink-0 mr-8">
                                <i class="fas fa-satellite tech-icon text-[#10B981]"></i>
                            </div>
                            <div class="text-center">
                                <h3 class="text-4xl font-bold text-gray-900 mb-4">Agricultural Field Recognition</h3>
                                <p class="text-2xl text-gray-600 mb-2">Precise Boundary Detection and Analysis</p>
                                <p class="text-lg text-gray-500">Advanced Field Mapping Technology</p>
                            </div>
                        </div>
                        
                        <!-- Before and After Recognition Comparison -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <div class="relative group">
                                <div class="text-center mb-6">
                                    <span class="recognition-label inline-block px-8 py-3 text-gray-700 rounded-full text-lg font-semibold shadow-lg">Before Recognition</span>
                                    <p class="text-base text-gray-500 mt-3 font-medium">Original Field Image</p>
                                </div>
                                <div class="recognition-container">
                                    <img src="pics/field_recognition_before.png" alt="Before Field Recognition" class="recognition-image">
                                    <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                    <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        <i class="fas fa-search-plus text-white text-xl bg-black/50 p-3 rounded-full"></i>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="relative group">
                                <div class="text-center mb-6">
                                    <span class="recognition-label inline-block px-8 py-3 bg-green-50 text-green-700 rounded-full text-lg font-semibold shadow-lg border-green-200">After Recognition</span>
                                    <p class="text-base text-gray-500 mt-3 font-medium">Processed Field Map</p>
                                </div>
                                <div class="recognition-container">
                                    <img src="pics/field_recognition_after.png" alt="After Field Recognition" class="recognition-image">
                                    <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                    <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        <i class="fas fa-search-plus text-white text-xl bg-black/50 p-3 rounded-full"></i>
                                    </div>
                                    <div class="absolute bottom-4 left-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        <span class="bg-green-500 text-white px-4 py-2 rounded-full text-sm font-semibold">
                                            <i class="fas fa-check mr-2"></i>Recognition Complete
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Technology Features -->
                        <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                            <div class="flex flex-col items-center">
                                <i class="fas fa-crosshairs text-2xl text-[#10B981] mb-3"></i>
                                <p class="text-lg font-semibold text-gray-800">Precise Boundary Detection</p>
                                <p class="text-sm text-gray-600">Accurate Field Mapping</p>
                            </div>
                            <div class="flex flex-col items-center">
                                <i class="fas fa-chart-area text-2xl text-[#10B981] mb-3"></i>
                                <p class="text-lg font-semibold text-gray-800">Automatic Area Calculation</p>
                                <p class="text-sm text-gray-600">Instant Measurement</p>
                            </div>
                            <div class="flex flex-col items-center">
                                <i class="fas fa-map-marked-alt text-2xl text-[#10B981] mb-3"></i>
                                <p class="text-lg font-semibold text-gray-800">Field Information Annotation</p>
                                <p class="text-sm text-gray-600">Detailed Field Data</p>
                            </div>
                                                 </div>
                     </div>
                </div>
            </div>
            
            <!-- Digital Agriculture Software Development - Special Layout for APP Screenshots -->
            <div class="mb-12">
                <div class="bento-card tech-card warning-gradient" data-aos="fade-up" data-aos-delay="400">
                    <div class="p-10">
                        <!-- Title and Description -->
                        <div class="flex items-center justify-center mb-8">
                            <div class="flex-shrink-0 mr-8">
                                <i class="fas fa-code tech-icon text-[#F59E0B]"></i>
                            </div>
                            <div class="text-center">
                                <h3 class="text-4xl font-bold text-gray-900 mb-4">Digital Agriculture Software Development</h3>
                                <p class="text-2xl text-gray-600 mb-2">8 Years of Industry Deep Understanding and Practice</p>
                                <p class="text-lg text-gray-500">Professional Agricultural Solutions</p>
                            </div>
                        </div>
                        
                        <!-- APP Function Display -->
                        <div class="grid grid-cols-2 md:grid-cols-5 gap-6 mb-8">
                            <!-- Machinery Monitoring -->
                            <div class="relative group">
                                <div class="text-center mb-4">
                                    <span class="recognition-label inline-block px-4 py-2 bg-green-50 text-green-700 rounded-full text-sm font-semibold shadow-lg border-green-200">Machinery Monitor</span>
                                    <p class="text-xs text-gray-500 mt-2 font-medium">Real-time Tracking</p>
                                </div>
                                <div class="recognition-container">
                                    <img src="pics/agricultural_machine_monitoring.jpg" alt="Machinery Monitoring Interface" class="recognition-image w-full h-auto aspect-[9/16] max-h-[500px] border-2 border-gray-600 rounded-2xl">
                                    <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                    <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        <i class="fas fa-search-plus text-white text-sm bg-black/50 p-2 rounded-full"></i>
                                    </div>
                                    <div class="absolute bottom-4 left-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                                            <i class="fas fa-tractor mr-1"></i>Live Monitor
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Field List -->
                            <div class="relative group">
                                <div class="text-center mb-4">
                                    <span class="recognition-label inline-block px-4 py-2 bg-blue-50 text-blue-700 rounded-full text-sm font-semibold shadow-lg border-blue-200">Field Management</span>
                                    <p class="text-xs text-gray-500 mt-2 font-medium">Land Management</p>
                                </div>
                                <div class="recognition-container">
                                    <img src="pics/field_list.png" alt="Field List Management" class="recognition-image w-full h-auto aspect-[9/16] max-h-[500px] border-2 border-gray-600 rounded-2xl">
                                    <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                    <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        <i class="fas fa-search-plus text-white text-sm bg-black/50 p-2 rounded-full"></i>
                                    </div>
                                    <div class="absolute bottom-4 left-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        <span class="bg-blue-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                                            <i class="fas fa-map mr-1"></i>Field Management
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Work Statistics -->
                            <div class="relative group">
                                <div class="text-center mb-4">
                                    <span class="recognition-label inline-block px-4 py-2 bg-purple-50 text-purple-700 rounded-full text-sm font-semibold shadow-lg border-purple-200">Work Statistics</span>
                                    <p class="text-xs text-gray-500 mt-2 font-medium">Performance Analytics</p>
                                </div>
                                <div class="recognition-container">
                                    <img src="pics/work_statistics.jpg" alt="Work Statistics Analysis" class="recognition-image w-full h-auto aspect-[9/16] max-h-[500px] border-2 border-gray-600 rounded-2xl">
                                    <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                    <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        <i class="fas fa-search-plus text-white text-sm bg-black/50 p-2 rounded-full"></i>
                                    </div>
                                    <div class="absolute bottom-4 left-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        <span class="bg-purple-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                                            <i class="fas fa-chart-bar mr-1"></i>Data Analytics
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Work Orders -->
                            <div class="relative group">
                                <div class="text-center mb-4">
                                    <span class="recognition-label inline-block px-4 py-2 bg-orange-50 text-orange-700 rounded-full text-sm font-semibold shadow-lg border-orange-200">Work Orders</span>
                                    <p class="text-xs text-gray-500 mt-2 font-medium">Task Management</p>
                                </div>
                                <div class="recognition-container">
                                    <img src="pics/work_order.jpg" alt="Work Order Management" class="recognition-image w-full h-auto aspect-[9/16] max-h-[500px] border-2 border-gray-600 rounded-2xl">
                                    <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                    <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        <i class="fas fa-search-plus text-white text-sm bg-black/50 p-2 rounded-full"></i>
                                    </div>
                                    <div class="absolute bottom-4 left-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        <span class="bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                                            <i class="fas fa-clipboard-list mr-1"></i>Task Management
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Machinery Details -->
                            <div class="relative group">
                                <div class="text-center mb-4">
                                    <span class="recognition-label inline-block px-4 py-2 bg-red-50 text-red-700 rounded-full text-sm font-semibold shadow-lg border-red-200">Equipment Details</span>
                                    <p class="text-xs text-gray-500 mt-2 font-medium">Device Information</p>
                                </div>
                                <div class="recognition-container">
                                    <img src="pics/agricultural_machine_details.jpg" alt="Agricultural Equipment Details" class="recognition-image w-full h-auto aspect-[9/16] max-h-[500px] border-2 border-gray-600 rounded-2xl">
                                    <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                    <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        <i class="fas fa-search-plus text-white text-sm bg-black/50 p-2 rounded-full"></i>
                                    </div>
                                    <div class="absolute bottom-4 left-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        <span class="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                                            <i class="fas fa-cog mr-1"></i>Device Info
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Software Development Features -->
                        <div class="grid grid-cols-1 md:grid-cols-5 gap-6 text-center">
                            <div class="flex flex-col items-center">
                                <i class="fas fa-map-marked-alt text-2xl text-[#F59E0B] mb-3"></i>
                                <p class="text-lg font-semibold text-gray-800">Field Generation</p>
                                <p class="text-sm text-gray-600">Automatic Mapping</p>
                            </div>
                            <div class="flex flex-col items-center">
                                <i class="fas fa-mobile-alt text-2xl text-[#F59E0B] mb-3"></i>
                                <p class="text-lg font-semibold text-gray-800">Mobile-First Design</p>
                                <p class="text-sm text-gray-600">Responsive Interface</p>
                            </div>
                            <div class="flex flex-col items-center">
                                <i class="fas fa-database text-2xl text-[#F59E0B] mb-3"></i>
                                <p class="text-lg font-semibold text-gray-800">Data-Driven Solutions</p>
                                <p class="text-sm text-gray-600">Intelligent Analytics</p>
                            </div>
                            <div class="flex flex-col items-center">
                                <i class="fas fa-users text-2xl text-[#F59E0B] mb-3"></i>
                                <p class="text-lg font-semibold text-gray-800">User Experience Focus</p>
                                <p class="text-sm text-gray-600">Intuitive Design</p>
                            </div>
                            <div class="flex flex-col items-center">
                                <i class="fas fa-sync-alt text-2xl text-[#F59E0B] mb-3"></i>
                                <p class="text-lg font-semibold text-gray-800">Continuous Iteration</p>
                                <p class="text-sm text-gray-600">Regular Updates</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Trajectory-Based Intelligent Algorithm - Full Width Display -->
            <div class="mb-12">
                <!-- Trajectory-Based Intelligent Algorithm - Special Layout for Comparison -->
                <div class="bento-card tech-card rose-gradient" data-aos="fade-up" data-aos-delay="500">
                    <div class="p-10">
                        <!-- Title and Description -->
                        <div class="flex items-center justify-center mb-8">
                            <div class="flex-shrink-0 mr-8">
                                <i class="fas fa-route tech-icon text-[#EC4899]"></i>
                            </div>
                            <div class="text-center">
                                <h3 class="text-4xl font-bold text-gray-900 mb-4">Trajectory-Based Intelligent Algorithm</h3>
                                <p class="text-2xl text-gray-600 mb-2">Trajectory Analysis and Intelligent Optimization</p>
                                <p class="text-lg text-gray-500">Advanced Path Intelligence</p>
                            </div>
                        </div>
                        
                        <!-- Trajectory Recognition Before and After Comparison -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <div class="relative group">
                                <div class="text-center mb-6">
                                    <span class="recognition-label inline-block px-8 py-3 text-gray-700 rounded-full text-lg font-semibold shadow-lg">Original Trajectory</span>
                                    <p class="text-base text-gray-500 mt-3 font-medium">Raw Path Data</p>
                                </div>
                                <div class="recognition-container">
                                    <img src="pics/trajectory_recognition_1.png" alt="Original Machinery Trajectory" class="recognition-image">
                                    <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                    <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        <i class="fas fa-search-plus text-white text-xl bg-black/50 p-3 rounded-full"></i>
                                    </div>
                                    <div class="absolute bottom-4 left-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        <span class="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-semibold">
                                            <i class="fas fa-tractor mr-2"></i>Machinery Path
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="relative group">
                                <div class="text-center mb-6">
                                    <span class="recognition-label inline-block px-8 py-3 bg-rose-50 text-rose-700 rounded-full text-lg font-semibold shadow-lg border-rose-200">Recognized Field</span>
                                    <p class="text-base text-gray-500 mt-3 font-medium">Processed Field Map</p>
                                </div>
                                <div class="recognition-container">
                                    <img src="pics/trajectory_recognition_2.png" alt="Field Generated from Trajectory Recognition" class="recognition-image">
                                    <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                    <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        <i class="fas fa-search-plus text-white text-xl bg-black/50 p-3 rounded-full"></i>
                                    </div>
                                    <div class="absolute bottom-4 left-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        <span class="bg-rose-500 text-white px-4 py-2 rounded-full text-sm font-semibold">
                                            <i class="fas fa-check mr-2"></i>Field Generated
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Algorithm Features -->
                        <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                            <div class="flex flex-col items-center">
                                <i class="fas fa-chart-line text-2xl text-[#EC4899] mb-3"></i>
                                <p class="text-lg font-semibold text-gray-800">Trajectory Data Analysis</p>
                                <p class="text-sm text-gray-600">Path Intelligence</p>
                            </div>
                            <div class="flex flex-col items-center">
                                <i class="fas fa-brain text-2xl text-[#EC4899] mb-3"></i>
                                <p class="text-lg font-semibold text-gray-800">Intelligent Algorithm Processing</p>
                                <p class="text-sm text-gray-600">Smart Processing</p>
                            </div>
                            <div class="flex flex-col items-center">
                                <i class="fas fa-map text-2xl text-[#EC4899] mb-3"></i>
                                <p class="text-lg font-semibold text-gray-800">Automatic Field Generation</p>
                                <p class="text-sm text-gray-600">Smart Mapping</p>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </section>



    <!-- Footer -->
    <footer id="footer" class="bg-gray-900 text-white py-24 px-6 lg:px-8 relative overflow-hidden">
        <!-- Background Decoration -->
        <div class="absolute inset-0 opacity-10">
            <svg class="w-full h-full" viewBox="0 0 1000 1000" xmlns="http://www.w3.org/2000/svg">
                <path class="line-graphic" d="M0,500 Q250,300 500,500 T1000,500" />
                <path class="line-graphic" d="M0,300 Q250,100 500,300 T1000,300" />
                <path class="line-graphic" d="M0,700 Q250,500 500,700 T1000,700" />
            </svg>
        </div>
        
        <div class="max-w-7xl mx-auto relative z-10">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
                <div class="lg:col-span-2" data-aos="fade-right">
                    <div class="mb-8">
                        <h3 class="text-4xl font-bold mb-4">UNITED F&G (HK) AGRICULTURAL TECHNOLOGY CO., LIMITED</h3>
                    </div>
                    <p class="text-2xl text-gray-400 mb-8 font-light">Digital Agriculture Expert · Advanced Agricultural Technology Solutions</p>
                    <div class="text-xl text-gray-400 space-y-3">
                        <p>Leading provider of digital agriculture technology solutions</p>
                        <p>Specializing in AI-powered agricultural intelligence and IoT systems</p>
                    </div>
                </div>
                <div data-aos="fade-up" data-aos-delay="200">
                    <h4 class="text-2xl font-semibold mb-8">Contact Information</h4>
                    <div class="space-y-4 text-gray-400">
                        <p class="text-lg flex items-center">
                            <i class="fas fa-map-marker-alt mr-4 text-xl"></i>
                            RM1005, 10/F, HO KING COMMERCIAL CENTRE, 2-16 FA YUEN STREET, MONGKOK, KLN, HONG KONG
                        </p>
                        <p class="text-lg flex items-center">
                            <i class="fas fa-envelope mr-4 text-xl"></i>
                            <EMAIL>
                        </p>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-20 pt-12 text-center text-gray-500">
                <p class="text-xl mb-2">&copy; 2024 UNITED F&G (HK) AGRICULTURAL TECHNOLOGY CO., LIMITED. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // 初始化AOS动画
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            offset: 100
        });

        function scrollToFooter() {
            document.getElementById('footer').scrollIntoView({ 
                behavior: 'smooth' 
            });
        }

        // Apple风格滚动动效
        document.addEventListener('DOMContentLoaded', function() {
            // 平滑滚动
            document.documentElement.style.scrollBehavior = 'smooth';
            
            // 移除可能导致层级问题的视差滚动
            // 保持简洁，避免复杂的transform导致层级堆叠问题
            
            // 增强的hover效果 - 优化z-index处理
            document.querySelectorAll('.bento-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    // 使用CSS类而不是直接修改style，避免层级冲突
                    this.classList.add('hovered');
                });
                
                card.addEventListener('mouseleave', function() {
                    this.classList.remove('hovered');
                });
            });
            
            // 技术卡片点击效果
            document.querySelectorAll('.tech-card').forEach(card => {
                card.addEventListener('click', function() {
                    // 添加点击动画
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
            
            // 导航栏滚动效果
            window.addEventListener('scroll', function() {
                const nav = document.querySelector('nav');
                if (window.scrollY > 100) {
                    nav.classList.add('backdrop-blur-xl');
                } else {
                    nav.classList.remove('backdrop-blur-xl');
                }
            });
        });

        // 移动端菜单切换
        function toggleMobileMenu() {
            // 移动端菜单逻辑
            console.log('Mobile menu toggle');
        }
    </script>
</body>
</html> 