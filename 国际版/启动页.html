<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>aFarm</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
            background: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .splash-container {
            width: 360px;
            height: 640px;
            background: linear-gradient(180deg, #1B5E20 0%, #2E7D32 50%, #388E3C 100%);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            padding: 80px 40px 60px;
        }

        .top-section {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .app-logo {
            width: 90px;
            height: 90px;
            background: rgba(255, 255, 255, 0.98);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .app-logo img {
            width: 70px;
            height: 70px;
            object-fit: contain;
        }

        .app-name {
            font-size: 56px;
            font-weight: 200;
            color: white;
            letter-spacing: -1px;
            text-align: center;
        }

        .bottom-section {
            text-align: center;
        }

        .ai-badge {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 20px;
            font-weight: 700;
            letter-spacing: 3px;
            text-transform: uppercase;
            margin-bottom: 30px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .hero-text {
            font-size: 42px;
            font-weight: 700;
            color: white;
            line-height: 1.0;
            margin-bottom: 15px;
            letter-spacing: -1.5px;
        }

        .hero-highlight {
            color: #FFD54F;
            display: block;
            margin-bottom: 4px;
        }

        .subtitle {
            font-size: 18px;
            font-weight: 300;
            color: rgba(255, 255, 255, 0.85);
            letter-spacing: 0.5px;
        }
    </style>
</head>
<body>
    <div class="splash-container">
        <div class="top-section">
            <div class="app-logo">
                <img src="images/logo.png" alt="aFarm">
            </div>
            <h1 class="app-name">aFarm</h1>
        </div>
        
        <div class="bottom-section">
            <div class="ai-badge">AI Powered</div>
            <h2 class="hero-text">
                <span class="hero-highlight">One Tap</span>
                Farm Mapping
            </h2>
            <p class="subtitle">Intelligent. Instant. Precise.</p>
        </div>
    </div>
</body>
</html>
