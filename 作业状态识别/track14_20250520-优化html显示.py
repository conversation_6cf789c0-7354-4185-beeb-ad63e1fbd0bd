from threadpoolctl import threadpool_info
print(threadpool_info())

import json, pandas as pd, numpy as np, pyproj, time, datetime, gc, os
from math import radians, cos, sin, asin, sqrt
from sklearn.cluster import DBSCAN
from scipy.spatial import <PERSON>aunay, QhullError
from shapely.ops import unary_union, polygonize
from shapely.geometry import Polygon, LineString, Point
import traceback

DEPLOYMENT_MODE = False # DEPLOYMENT_MODE = True: 部署模式，无任何控制台输出
if not DEPLOYMENT_MODE: # 测试模式
    def debug_print(*args, **kwargs):
        print(*args, **kwargs)
    def debug_traceback_print_exc():
        traceback.print_exc()
else: # 部署模式
    def debug_print(*args, **kwargs):
        pass
    def debug_traceback_print_exc():
        pass

EARTH_RADIUS_KM = 6371 # 地球半径(公里)，算距离用
SPEED_FILTER_MIN_KMH = 1 # 最低速度(公里/小时)，太慢的点不要
SPEED_FILTER_MAX_KMH = 25 # 最高速度(公里/小时)，太快的点不要
TRAJECTORY_BASE_SAMPLING_INTERVAL_M = 10.0 # 轨迹插值常规间距(米)
SEGMENT_LENGTH_FOR_AVG_SPEED_M = 200.0 # 判断快慢的轨迹段长度(米)
SPEED_THRESHOLD_KMH_FOR_DYNAMIC_SAMPLING = 10.0 # 高速插值速度门槛(公里/小时)
HIGH_SPEED_SAMPLING_INTERVAL_M = 100.0 # 轨迹插值高速间距(米)
DBSCAN_EPS_METERS = 15 # 聚类邻域半径(米)，点多近算一群
DBSCAN_MIN_SAMPLES = 9 # 聚类最少点数，几个点才算一群
MAX_EDGE_LENGTH_M_FOR_POLYGON = 30 # 地块边界最大边长(米)，边太长不像地块
BORDER_EXPANSION_M_FACTOR = 0.5 # 地块边界扩展系数(基于栅格大小)
GRID_SIZE_METERS = 2.3 # 栅格边长(米)，算轨迹覆盖的格子大小
EFFECTIVE_MIN_CLUSTER_SIZE_POINTS = 30 # 有效点簇最少点数，点太少不成簇
EFFECTIVE_POINTS_THRESHOLD_FOR_POLYGON = 30 # 画地块边界最少点数
CLUSTER_TRIMMING_POINTS_COUNT = 15 # 点簇两头各剪掉几个点
MIN_COVERAGE_AREA_M2_THRESHOLD = 300 # 地块面积太小(平方米)则无效
MIN_WORK_EFFICIENCY_MU_HR_THRESHOLD = 1.5 # 作业效率太低(亩/小时)则地块无效
MAX_TRAVEL_SPEED_THRESHOLD_KMH = 15 # 地块内原始点均速太快(公里/小时)则无效
WORK_TIME_CALC_TIME_THRESHOLD_SECONDS = 60 # 算作业时间，点之间隔太久(秒)不算
GENERATE_HTML_MAPS = True # 是否生成HTML地图看结果
_SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__)) # 程序自己找路用，不用改
OUTPUT_DIR_BASE = r"F:\Python\作业状态识别\result" # 所有结果放这里
TEST_OUTPUT_SUBDIR_MAPS = "html_maps" # 地图放这个子文件夹
FINAL_OUTPUT_SUBDIR_JSON = "json_data" # JSON数据放这个子文件夹

def load_trajectory_data(filename: str):
    try:
        data = pd.read_json(filename, encoding='utf-8')
        if not all(col in data.columns for col in ['latitude', 'longitude', 'time']):
            debug_print(f"❌ 错误：JSON文件 {filename} 缺少必需列 (latitude, longitude, time)")
            return None
        data['latitude'] = pd.to_numeric(data['latitude'], errors='coerce')
        data['longitude'] = pd.to_numeric(data['longitude'], errors='coerce')
        data['time'] = pd.to_datetime(data['time'], errors='coerce')
        initial_count = len(data)
        data.dropna(subset=['latitude', 'longitude', 'time'], inplace=True)
        cleaned_count = initial_count - len(data)
        if data.empty:
            debug_print(f"❌ 错误：文件 {filename} 有效数据为空。")
            return None
        debug_print(f"从 {filename} 加载 {len(data)} 条数据 (清理了 {cleaned_count} 条无效数据)。")
        return data
    except FileNotFoundError:
        debug_print(f"❌ 错误: 文件 '{filename}' 未找到.")
        return None
    except ValueError as e:
        debug_print(f"❌ 错误: 解析或类型转换失败 - {e}")
        return None
    except Exception as e:
        debug_print(f"❌ 未知错误: {e}")
        return None
def calculate_haversine_distances_vectorized(lat1, lon1, lat2, lon2, earth_radius_km_param):
    lat1, lon1, lat2, lon2 = map(np.radians, [lat1, lon1, lat2, lon2])
    dlat = lat2 - lat1; dlon = lon2 - lon1
    a = np.sin(dlat / 2.0)**2 + np.cos(lat1) * np.cos(lat2) * np.sin(dlon / 2.0)**2
    a = np.clip(a, 0, 1); c = 2 * np.arcsin(np.sqrt(a)); km = earth_radius_km_param * c
    return km
def haversine_single_point(lon1, lat1, lon2, lat2, earth_radius_km_param):
    lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])
    dlon = lon2 - lon1; dlat = lat2 - lat1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * asin(sqrt(a)); meters = earth_radius_km_param * 1000 * c
    return meters
def calculate_trajectory_metrics(data: pd.DataFrame):
    df = data.sort_values(by='time').copy()
    if len(df) < 2:
        df['distance_km'] = 0.0; df['speed_kmh'] = 0.0
        return df
    distances_km = calculate_haversine_distances_vectorized(
        df['latitude'].values[:-1], df['longitude'].values[:-1],
        df['latitude'].values[1:], df['longitude'].values[1:],
        EARTH_RADIUS_KM)
    df['distance_km'] = np.concatenate(([0.0], distances_km))
    time_deltas_hours = df['time'].diff().dt.total_seconds().div(3600)
    time_deltas_hours = time_deltas_hours.replace(0, np.nan)
    time_deltas_hours[time_deltas_hours < 1e-9] = np.nan
    df['speed_kmh'] = df['distance_km'] / time_deltas_hours
    df['speed_kmh'] = df['speed_kmh'].fillna(0)
    df = df.replace([np.inf, -np.inf], np.nan)
    df['speed_kmh'] = df['speed_kmh'].fillna(0)
    df['original_index'] = df.index
    return df
def filter_trajectory_by_speed(data_input: pd.DataFrame, speed_min: float, speed_max: float):
    data = data_input.copy()
    if 'speed_kmh' not in data.columns:
        debug_print("❌ 错误: 'speed_kmh' 列不存在，无法筛选。")
        return pd.DataFrame(columns=data.columns)
    data['speed_kmh'] = pd.to_numeric(data['speed_kmh'], errors='coerce')
    data = data.dropna(subset=['speed_kmh'])
    filtered_data = data[(data['speed_kmh'] >= speed_min) & (data['speed_kmh'] <= speed_max)].copy()
    removed_count = len(data_input) - len(filtered_data)
    debug_print(f"速度筛选：保留 {len(filtered_data)} 个点，移除 {removed_count} 个点。")
    return filtered_data
def interpolate_trajectory_dynamically(input_df: pd.DataFrame):
    if input_df.empty or not all(c in input_df.columns for c in ['latitude', 'longitude', 'distance_km', 'time']):
        return pd.DataFrame(columns=['latitude', 'longitude'])
    if len(input_df) < 2:
        if all(c in input_df.columns for c in ['latitude', 'longitude']):
            return input_df[['latitude', 'longitude']].copy()
        return pd.DataFrame(columns=['latitude', 'longitude'])
    path_points_list = [{'latitude': input_df.iloc[0]['latitude'], 'longitude': input_df.iloc[0]['longitude']}]
    current_segment_points_indices = [0]
    accumulated_dist_in_segment_m = 0.0
    df_sorted = input_df.sort_values(by='time').reset_index(drop=True)
    df_sorted['time_diff_seconds'] = df_sorted['time'].diff().dt.total_seconds().fillna(0)
    for i in range(1, len(df_sorted)):
        segment_dist_m = df_sorted.loc[i, 'distance_km'] * 1000
        accumulated_dist_in_segment_m += segment_dist_m
        current_segment_points_indices.append(i)
        if accumulated_dist_in_segment_m >= SEGMENT_LENGTH_FOR_AVG_SPEED_M or i == len(df_sorted) - 1:
            start_idx = current_segment_points_indices[0]; end_idx = current_segment_points_indices[-1]
            current_processing_segment_df = df_sorted.iloc[start_idx : end_idx + 1]
            avg_speed_kmh_in_segment = 0
            if 'speed_kmh' in current_processing_segment_df.columns and not current_processing_segment_df['speed_kmh'].empty:
                avg_speed_kmh_in_segment = current_processing_segment_df['speed_kmh'].mean()
            else:
                total_dist_km_proc_seg = current_processing_segment_df['distance_km'].sum()
                total_time_sec_proc_seg = current_processing_segment_df['time_diff_seconds'].sum()
                if total_time_sec_proc_seg > 1e-9: avg_speed_kmh_in_segment = (total_dist_km_proc_seg / (total_time_sec_proc_seg / 3600.0))
            current_sampling_interval_m = TRAJECTORY_BASE_SAMPLING_INTERVAL_M
            if avg_speed_kmh_in_segment > SPEED_THRESHOLD_KMH_FOR_DYNAMIC_SAMPLING: current_sampling_interval_m = HIGH_SPEED_SAMPLING_INTERVAL_M
            cps_lats = current_processing_segment_df['latitude'].values
            cps_lons = current_processing_segment_df['longitude'].values
            cps_dist_km_values = current_processing_segment_df['distance_km'].values
            accumulated_distance_within_processing_segment_m = 0.0
            last_gen_pt_lon, last_gen_pt_lat = path_points_list[-1]['longitude'], path_points_list[-1]['latitude']
            dist_from_last_gen_to_seg_start_m = 0
            if path_points_list:
                dist_from_last_gen_to_seg_start_m = haversine_single_point(
                    last_gen_pt_lon, last_gen_pt_lat,
                    cps_lons[0], cps_lats[0], EARTH_RADIUS_KM)
            next_sample_target_dist_overall = current_sampling_interval_m - dist_from_last_gen_to_seg_start_m
            if next_sample_target_dist_overall < 1e-3:
                next_sample_target_dist_overall = current_sampling_interval_m
            for k in range(len(cps_lats) - 1):
                p1_lat, p1_lon = cps_lats[k], cps_lons[k]
                p2_lat, p2_lon = cps_lats[k + 1], cps_lons[k + 1]
                micro_segment_dist_m = cps_dist_km_values[k + 1] * 1000.0
                if micro_segment_dist_m < 1e-9:
                    continue
                micro_segment_end_dist_m = accumulated_distance_within_processing_segment_m + micro_segment_dist_m
                while next_sample_target_dist_overall <= micro_segment_end_dist_m:
                    dist_into_micro_seg_m = next_sample_target_dist_overall - accumulated_distance_within_processing_segment_m
                    fraction = max(0.0, min(1.0, dist_into_micro_seg_m / micro_segment_dist_m))
                    inter_lat = p1_lat + fraction * (p2_lat - p1_lat)
                    inter_lon = p1_lon + fraction * (p2_lon - p1_lon)
                    dist_to_last_added_point_m = haversine_single_point(inter_lon, inter_lat, last_gen_pt_lon,
                                                                        last_gen_pt_lat, EARTH_RADIUS_KM)
                    if dist_to_last_added_point_m > 1e-3:
                        path_points_list.append({'latitude': inter_lat, 'longitude': inter_lon})
                        last_gen_pt_lon, last_gen_pt_lat = inter_lon, inter_lat
                    next_sample_target_dist_overall += current_sampling_interval_m
                accumulated_distance_within_processing_segment_m += micro_segment_dist_m
            accumulated_dist_in_segment_m = 0.0; current_segment_points_indices = [i]
    last_orig_point_df = df_sorted.iloc[-1]
    if path_points_list:
        last_gen_pt = path_points_list[-1]
        dist_to_last_orig_m = haversine_single_point(last_gen_pt['longitude'],last_gen_pt['latitude'],last_orig_point_df['longitude'],last_orig_point_df['latitude'],EARTH_RADIUS_KM)
        if dist_to_last_orig_m > 1e-3 : path_points_list.append({'latitude':last_orig_point_df['latitude'],'longitude':last_orig_point_df['longitude']})
    elif not df_sorted.empty: path_points_list.append({'latitude':last_orig_point_df['latitude'],'longitude':last_orig_point_df['longitude']})
    output_df = pd.DataFrame(path_points_list)
    if not output_df.empty: output_df.reset_index(drop=True, inplace=True)
    return output_df
def cluster_trajectory_points_dbscan(data: pd.DataFrame, eps_meters: float, min_samples: int):
    if data.empty or len(data) < min_samples:
        data_copy = data.copy(); data_copy['cluster'] = -1
        debug_print(f"识别完成：输入点过少(<{min_samples})，无有效簇，0 个噪声点。")
        return data_copy
    eps_radians = eps_meters / (EARTH_RADIUS_KM * 1000)
    coords_rad = np.radians(data[['latitude', 'longitude']].values)
    if np.isnan(coords_rad).any():
        data_copy = data.copy(); data_copy['cluster'] = -1
        debug_print("❌ 坐标数据含NaN，无法DBSCAN。所有点标记为噪声。")
        return data_copy
    db = DBSCAN(eps=eps_radians, min_samples=min_samples, metric='haversine', algorithm='ball_tree', n_jobs=1, leaf_size=50)
    try:
        db.fit(coords_rad); cluster_labels = db.labels_
        data_copy = data.copy(); data_copy['cluster'] = cluster_labels
    except ValueError as e:
        data_copy = data.copy(); data_copy['cluster'] = -1; cluster_labels = data_copy['cluster'].values
        debug_print(f"❌ DBSCAN出错: {e}，所有点标记为噪声。")
        return data_copy
    num_clusters = len(set(cluster_labels)) - (1 if -1 in cluster_labels else 0)
    num_noise = list(cluster_labels).count(-1)
    debug_print(f"识别完成：发现 {num_clusters} 个簇，{num_noise} 个噪声点。")
    return data_copy
def triangulate_points_delaunay(points_lonlat):
    if points_lonlat is None or not hasattr(points_lonlat,'__len__') or len(points_lonlat) < 3: return None
    try:
        points_array = np.array(points_lonlat,dtype=float)
        if np.isnan(points_array).any(): return None
        noise = 5e-6; points_perturbed = np.copy(points_array)
        points_perturbed[:,0] += np.random.uniform(-noise,noise,size=points_array.shape[0])
        points_perturbed[:,1] += np.random.uniform(-noise,noise,size=points_array.shape[0])
        return Delaunay(points_perturbed)
    except QhullError: return None
    except Exception: return None
def _is_valid_triangle(triangle_indices, points_array, max_length_m):
    for i in range(3):
        p1_idx, p2_idx = triangle_indices[i], triangle_indices[(i+1)%3]
        if p1_idx >= len(points_array) or p2_idx >= len(points_array): return False
        pt1, pt2 = points_array[p1_idx], points_array[p2_idx]
        if pd.isna(pt1[0])or pd.isna(pt1[1])or pd.isna(pt2[0])or pd.isna(pt2[1]): return False
        if haversine_single_point(pt1[0],pt1[1],pt2[0],pt2[1],EARTH_RADIUS_KM) > max_length_m: return False
    return True
def _extract_outer_edges(triangles_simplices, points_array):
    if not triangles_simplices: return []
    edge_count = {}
    for tri_indices in triangles_simplices:
        for i in range(3):
            p1_idx,p2_idx = tri_indices[i],tri_indices[(i+1)%3]
            if p1_idx >= len(points_array)or p2_idx >= len(points_array): continue
            edge = tuple(sorted((p1_idx,p2_idx)))
            edge_count[edge] = edge_count.get(edge,0)+1
    return [edge for edge,count in edge_count.items() if count==1]
def extract_cluster_polygon_boundary(points_lonlat, max_edge_len_m: float):
    if points_lonlat is None or not hasattr(points_lonlat,'__len__') or len(points_lonlat) < 3: return None
    points_array = np.array(points_lonlat, dtype=float)
    if len(points_array) < 3: return None
    tri = triangulate_points_delaunay(points_array)
    if tri is None: return None
    valid_triangles_indices = [s for s in tri.simplices if _is_valid_triangle(s,tri.points,max_edge_len_m)]
    if not valid_triangles_indices: return None
    outer_edges_indices = _extract_outer_edges(valid_triangles_indices, tri.points)
    if not outer_edges_indices: return None
    edge_lines = []
    for edge_idx_pair in outer_edges_indices:
        p1_coords,p2_coords = tri.points[edge_idx_pair[0]],tri.points[edge_idx_pair[1]]
        if not (np.isnan(p1_coords).any()or np.isnan(p2_coords).any()):
            edge_lines.append(LineString([p1_coords,p2_coords]))
    if not edge_lines: return None
    try:
        merged_lines = unary_union(edge_lines); polygons = list(polygonize(merged_lines))
        if polygons: return max(polygons,key=lambda p:p.area)
        else: return None
    except Exception: return None
def determine_utm_projection(centroid_lat: float, centroid_lon: float):
    if pd.isna(centroid_lat) or pd.isna(centroid_lon): return None, None, None
    try:
        utm_zone = int((centroid_lon + 180) / 6) + 1
        hemisphere = 'north' if centroid_lat >= 0 else 'south'
        utm_crs_str = f"+proj=utm +zone={utm_zone} +{hemisphere} +ellps=WGS84 +datum=WGS84 +units=m +no_defs"
        proj_utm = pyproj.CRS(utm_crs_str); proj_wgs84 = pyproj.CRS('EPSG:4326')
        to_utm = pyproj.Transformer.from_crs(proj_wgs84, proj_utm, always_xy=True)
        to_lonlat = pyproj.Transformer.from_crs(proj_utm, proj_wgs84, always_xy=True)
        return proj_utm, to_utm, to_lonlat
    except Exception: return None, None, None
def _add_intersected_grids_dda(x1, y1, x2, y2, grid_size, grid_set):
    gx1, gy1 = int(x1 // grid_size), int(y1 // grid_size)
    gx2, gy2 = int(x2 // grid_size), int(y2 // grid_size)
    grid_set.add((gx1, gy1))
    if gx1 == gx2 and gy1 == gy2: return

    dx = x2 - x1; dy = y2 - y1
    step_x = 1 if dx > 0 else -1 if dx < 0 else 0
    step_y = 1 if dy > 0 else -1 if dy < 0 else 0
    cgx, cgy = gx1, gy1

    if dx == 0: # Vertical line
        while cgy != gy2: cgy += step_y; grid_set.add((cgx, cgy))
        return
    if dy == 0: # Horizontal line
        while cgx != gx2: cgx += step_x; grid_set.add((cgx, cgy))
        return

    t_max_x = (((gx1 + (1 if dx > 0 else 0)) * grid_size) - x1) / dx if dx != 0 else float('inf')
    t_max_y = (((gy1 + (1 if dy > 0 else 0)) * grid_size) - y1) / dy if dy != 0 else float('inf')
    t_delta_x = abs(grid_size / dx) if dx != 0 else float('inf')
    t_delta_y = abs(grid_size / dy) if dy != 0 else float('inf')

    while not (cgx == gx2 and cgy == gy2):
        if t_max_x < t_max_y:
            t_max_x += t_delta_x; cgx += step_x
        else:
            t_max_y += t_delta_y; cgy += step_y
        grid_set.add((cgx, cgy))

def calculate_trajectory_coverage_grid(traj_points_latlon, grid_size_m: float, transformer_to_utm, plot_polygon_utm=None):
    if not traj_points_latlon or transformer_to_utm is None: return 0, set()
    traj_utm = [transformer_to_utm.transform(lon, lat) for lat, lon in traj_points_latlon]
    grid_set = set()
    if not traj_utm: return 0, set()

    prepared_plot_polygon = None
    if plot_polygon_utm:
        from shapely.prepared import prep
        prepared_plot_polygon = prep(plot_polygon_utm)

    if len(traj_utm) == 1:
        x, y = traj_utm[0]
        if prepared_plot_polygon:
            grid_x_idx, grid_y_idx = int(x // grid_size_m), int(y // grid_size_m)
            if prepared_plot_polygon.contains(Point(x, y)):
                 grid_set.add((grid_x_idx, grid_y_idx))
        else:
            grid_set.add((int(x // grid_size_m), int(y // grid_size_m)))
        return len(grid_set) * (grid_size_m**2), grid_set

    for i in range(len(traj_utm) - 1):
        x1,y1=traj_utm[i]; x2,y2=traj_utm[i+1]
        if prepared_plot_polygon:
            cx, cy = (x1+x2)/2, (y1+y2)/2
            if not prepared_plot_polygon.contains(Point(cx,cy)): continue
        _add_intersected_grids_dda(x1, y1, x2, y2, grid_size_m, grid_set)
    area_m2 = len(grid_set) * (grid_size_m**2)
    return area_m2, grid_set
def _process_single_cluster_analysis(cluster_id, cluster_data_df, original_gps_data_df):
    try:
        points_lonlat = cluster_data_df[['longitude','latitude']].values
        if len(points_lonlat) < EFFECTIVE_POINTS_THRESHOLD_FOR_POLYGON: return cluster_id, None
        outer_polygon_shape = extract_cluster_polygon_boundary(points_lonlat, MAX_EDGE_LENGTH_M_FOR_POLYGON)
        if not (outer_polygon_shape and outer_polygon_shape.is_valid and not outer_polygon_shape.is_empty): return cluster_id, None
        outer_polygon_coords_lonlat = list(outer_polygon_shape.exterior.coords)
        plot_centroid_lat = np.mean([c[1] for c in outer_polygon_coords_lonlat])
        plot_centroid_lon = np.mean([c[0] for c in outer_polygon_coords_lonlat])
        _, to_utm, to_lonlat = determine_utm_projection(plot_centroid_lat, plot_centroid_lon)
        utm_area_m2 = 0; expanded_polygon_coords_lonlat_shapely = []
        utm_coords = []
        if to_utm and to_lonlat:
            try:
                utm_coords = [to_utm.transform(lon,lat) for lon,lat in outer_polygon_coords_lonlat]
                if len(utm_coords) >= 3:
                    utm_poly_shape = Polygon(utm_coords)
                    expanded_utm_poly = utm_poly_shape.buffer(GRID_SIZE_METERS * BORDER_EXPANSION_M_FACTOR)
                    utm_area_m2 = expanded_utm_poly.area
                    expanded_coords_utm = list(expanded_utm_poly.exterior.coords)
                    expanded_polygon_coords_lonlat_shapely = [to_lonlat.transform(x,y) for x,y in expanded_coords_utm]
            except Exception as e_utm:
                debug_print(f"UTM处理异常 {cluster_id}: {e_utm}")
        work_hours = 0.0; avg_speed_original_points_kmh = 0.0; cluster_path_length_m = 0.0
        relevant_original_points_df = pd.DataFrame()
        if original_gps_data_df is not None and 'time' in original_gps_data_df.columns and not original_gps_data_df.empty:
            try:
                from shapely.prepared import prep
                import shapely
                prepared_polygon = prep(outer_polygon_shape)
                use_vectorized_shapely = False
                if hasattr(shapely, 'points') and hasattr(shapely, 'contains'):
                    try:
                        point_geoms = shapely.points(original_gps_data_df['longitude'].values,
                                                     original_gps_data_df['latitude'].values)
                        mask = shapely.contains(outer_polygon_shape, point_geoms)
                        relevant_original_points_df = original_gps_data_df[mask].copy().sort_values(by='time')
                        use_vectorized_shapely = True
                    except Exception:
                        relevant_original_points_df = pd.DataFrame()
                        use_vectorized_shapely = False
                if not use_vectorized_shapely:
                    relevant_indices = []
                    for pt_row in original_gps_data_df.itertuples(index=True, name='GPSPoint'):
                        if prepared_polygon.contains(Point(pt_row.longitude, pt_row.latitude)):
                            relevant_indices.append(pt_row.Index)
                    if relevant_indices:
                        relevant_original_points_df = original_gps_data_df.loc[relevant_indices].copy().sort_values(
                            by='time')
                    else:
                        relevant_original_points_df = pd.DataFrame(columns=original_gps_data_df.columns)
                if not relevant_original_points_df.empty:
                    if len(relevant_original_points_df) >= 2:
                        lats = relevant_original_points_df['latitude'].values; lons = relevant_original_points_df['longitude'].values
                        dists_km = calculate_haversine_distances_vectorized(lats[:-1],lons[:-1],lats[1:],lons[1:],EARTH_RADIUS_KM)
                        total_dist_km = np.sum(dists_km); cluster_path_length_m = total_dist_km * 1000
                        time_diffs_s = relevant_original_points_df['time'].diff().dt.total_seconds()
                        work_hours = time_diffs_s[time_diffs_s <= WORK_TIME_CALC_TIME_THRESHOLD_SECONDS].sum()/3600.0
                        if work_hours > 1e-6: avg_speed_original_points_kmh=round(total_dist_km/work_hours,1)
            except Exception as e_orig:
                debug_print(f"原始点相关计算失败 {cluster_id}: {e_orig}")
        utm_poly_for_grid_check = None
        if len(utm_coords) >= 3:
            try: utm_poly_for_grid_check = Polygon(utm_coords)
            except: pass
        traj_points_for_grid_calc_from_cluster = cluster_data_df[['latitude','longitude']].values.tolist() if not cluster_data_df.empty else []
        if len(traj_points_for_grid_calc_from_cluster) < 2:
            coverage_area_m2_grid, grid_set_cluster = 0, set()
        else:
            coverage_area_m2_grid, grid_set_cluster = calculate_trajectory_coverage_grid(
                traj_points_for_grid_calc_from_cluster, GRID_SIZE_METERS, to_utm, plot_polygon_utm=utm_poly_for_grid_check
            )
        area_mu = round(utm_area_m2 / 666.67, 2) if utm_area_m2 > 0 else 0
        coverage_area_mu_grid = round(coverage_area_m2_grid / 666.67, 2)
        track_per_mu_m = int(round(cluster_path_length_m / coverage_area_mu_grid)) if coverage_area_mu_grid > 0 else 0
        work_efficiency_mu_hr = round(coverage_area_mu_grid / work_hours, 2) if work_hours > 1e-6 else 0
        is_valid_plot = True;
        reasons_invalid = []
        MIN_VALID_WORK_HOURS = 0.02  # (0.02小时 = 72秒)
        if utm_area_m2 < MIN_COVERAGE_AREA_M2_THRESHOLD:
            is_valid_plot = False;
            reasons_invalid.append(f"地块面积过小({int(utm_area_m2)}m²)")
        if is_valid_plot:
            if work_hours < MIN_VALID_WORK_HOURS:
                if coverage_area_mu_grid > 0.01:  # 如果时长不足但仍有面积
                    is_valid_plot = False;
                    reasons_invalid.append(
                        f"作业时长过短({work_hours * 3600:.1f}秒, 少于{MIN_VALID_WORK_HOURS * 3600:.0f}秒)")
            else:  # work_hours >= MIN_VALID_WORK_HOURS，作业时长被认为足够进行效率和速度评估
                if work_efficiency_mu_hr < MIN_WORK_EFFICIENCY_MU_HR_THRESHOLD:
                    is_valid_plot = False;
                    reasons_invalid.append(f"工作效率过低({work_efficiency_mu_hr:.2f}亩/时)")
                if avg_speed_original_points_kmh > MAX_TRAVEL_SPEED_THRESHOLD_KMH:
                    is_valid_plot = False;
                    reasons_invalid.append(f"原始点行进速度过快({avg_speed_original_points_kmh}km/h)")
        plot_data = {
            'cluster_id': cluster_id, 'polygon_area_m2': int(utm_area_m2), 'polygon_area_mu': area_mu,
            'trajectory_length_m': int(cluster_path_length_m), 'grid_coverage_area_m2': int(coverage_area_m2_grid), 'grid_coverage_area_mu': coverage_area_mu_grid,
            'track_per_mu_m': track_per_mu_m, 'work_hours': round(work_hours,2), 'work_efficiency_mu_hr': work_efficiency_mu_hr, 'avg_speed_original_points_kmh': avg_speed_original_points_kmh,
            'outer_polygon_coords_lonlat': outer_polygon_coords_lonlat,
            'expanded_polygon_coords_lonlat': expanded_polygon_coords_lonlat_shapely, 'grid_set_utm_indices': list(grid_set_cluster),
            'is_valid': is_valid_plot, 'reasons_invalid': ", ".join(reasons_invalid) if reasons_invalid else "有效",
            'utm_transformers': (to_utm, to_lonlat) if to_utm and to_lonlat else (None, None)}
        return cluster_id, plot_data
    except Exception: return cluster_id, None
def analyze_plot_candidates_from_clusters(clustered_data_df: pd.DataFrame, original_gps_data_df_cleaned: pd.DataFrame, valid_cluster_ids: list):
    all_plot_info = {}; all_valid_grids_union = set()
    plot_idx_counter = 1
    for cluster_id in valid_cluster_ids:
        cluster_data_subset = clustered_data_df[clustered_data_df['cluster'] == cluster_id].copy()
        try:
            _, plot_data = _process_single_cluster_analysis(cluster_id, cluster_data_subset, original_gps_data_df_cleaned)
            if plot_data and plot_data.get('is_valid', False):
                plot_idx_str = str(plot_idx_counter); all_plot_info[plot_idx_str] = plot_data
                if GENERATE_HTML_MAPS:
                    if 'grid_set_utm_indices' in plot_data and plot_data['grid_set_utm_indices']:
                        all_valid_grids_union.update(map(tuple, plot_data['grid_set_utm_indices']))
                plot_idx_counter += 1
        except Exception: pass
    valid_plot_count = len(all_plot_info)
    if valid_cluster_ids : debug_print(f"地块分析完成. 有效地块数: {valid_plot_count}.")
    else: debug_print(f"地块分析完成. 有效地块数: 0.")
    return all_plot_info, all_valid_grids_union
def generate_plot_summary_json_structure(plot_info_dict: dict):
    summary = {"area": [], "traj_area": [], "distance": [], "hour": [], "track_per_mu": []}
    sorted_plot_ids = sorted(plot_info_dict.keys(), key=lambda x: int(x))
    for plot_id_str in sorted_plot_ids:
        info = plot_info_dict[plot_id_str]
        if not info.get('is_valid', True): continue
        summary["area"].append(info['polygon_area_m2'])
        summary["traj_area"].append(info['grid_coverage_area_m2'])
        summary["distance"].append(round(info['trajectory_length_m'] / 1000.0, 3))
        summary["hour"].append(info['work_hours'])
        track_per_mu_val = 0
        if info['grid_coverage_area_mu'] > 0:
            track_per_mu_val = round(info['trajectory_length_m'] / info['grid_coverage_area_mu'], 2)
        summary["track_per_mu"].append(track_per_mu_val)
    return summary
def generate_plot_boundaries_json_structure(plot_info_dict: dict):
    final_boundaries_payload = {"out": [], "in": []}
    sorted_plot_ids = sorted(plot_info_dict.keys(), key=lambda x: int(x))
    for plot_id_str in sorted_plot_ids:
        info = plot_info_dict[plot_id_str]
        if not info.get('is_valid', True): continue
        if 'outer_polygon_coords_lonlat' in info and info['outer_polygon_coords_lonlat']:
            one_plot_outer_ring = [{"longitude": lon, "latitude": lat} for lon, lat in info['outer_polygon_coords_lonlat']]
            if one_plot_outer_ring:
                final_boundaries_payload["out"].append(one_plot_outer_ring)
    return final_boundaries_payload
def calculate_dataframe_centroid(df: pd.DataFrame, lat_col='latitude', lon_col='longitude'):
    if df is None or df.empty or lat_col not in df.columns or lon_col not in df.columns: return None, None
    valid_points = df.dropna(subset=[lat_col, lon_col])
    if valid_points.empty: return None, None
    return valid_points[lat_col].mean(), valid_points[lon_col].mean()
def filter_and_trim_clusters(clustered_data: pd.DataFrame, min_cluster_size: int, trim_points_count: int):
    final_data = clustered_data.copy(); valid_cluster_ids = []
    if 'cluster' not in final_data.columns or final_data[final_data['cluster'] != -1].empty:
        return final_data, valid_cluster_ids
    cluster_counts = final_data[final_data['cluster'] != -1]['cluster'].value_counts()
    valid_ids_by_size = cluster_counts[cluster_counts >= min_cluster_size].index.tolist()
    invalid_ids_by_size = cluster_counts[cluster_counts < min_cluster_size].index.tolist()
    if invalid_ids_by_size:
        final_data.loc[final_data['cluster'].isin(invalid_ids_by_size), 'cluster'] = -1
    current_valid_clusters_in_data = sorted(list(final_data[final_data['cluster'].isin(valid_ids_by_size)]['cluster'].unique()))
    for cluster_id in current_valid_clusters_in_data:
        cluster_points = final_data[final_data['cluster'] == cluster_id]
        if len(cluster_points) > trim_points_count * 2 and len(cluster_points) > min_cluster_size :
            cluster_points_sorted = cluster_points.sort_index()
            head_indices = cluster_points_sorted.iloc[:trim_points_count].index
            tail_indices = cluster_points_sorted.iloc[-trim_points_count:].index
            final_data.loc[head_indices, 'cluster'] = -1; final_data.loc[tail_indices, 'cluster'] = -1
        else: pass
    final_cluster_counts = final_data[final_data['cluster'] != -1]['cluster'].value_counts()
    valid_cluster_ids = sorted(list(final_cluster_counts[final_cluster_counts >= min_cluster_size].index.tolist()))
    clusters_too_small_after_trim = final_cluster_counts[final_cluster_counts < min_cluster_size].index.tolist()
    if clusters_too_small_after_trim:
        final_data.loc[final_data['cluster'].isin(clusters_too_small_after_trim), 'cluster'] = -1
        valid_cluster_ids = sorted(list(final_data[final_data['cluster']!=-1]['cluster'].value_counts().loc[lambda x: x >= min_cluster_size].index))
    return final_data, valid_cluster_ids
if GENERATE_HTML_MAPS:
    import folium
    from folium import plugins
    TIANDITU_KEY = "e7f645439e09d5fdbc5158bacab6d024"
    DEFAULT_MAP_CENTER_LAT = 39.9
    DEFAULT_MAP_CENTER_LON = 116.4
    DEFAULT_MAP_ZOOM = 17
    PLOT_COLORS = ['#FF4500', '#32CD32', '#1E90FF', '#FFD700', '#FF69B4', '#8A2BE2']
    LAYER_DEFAULT_VISIBILITY = { "原始轨迹动画 (全部)": True, "速度清洗后原始点": False,"人造轨迹点 (插值)": False, "作业点 (按簇)": False, "行驶/噪声点 (插值)": False,"作业区域边界": True,"作业区域编号": True, "轨迹覆盖栅格 (所有有效)": False }
    def create_folium_base_map(center_lat: float, center_lon: float, zoom: int):
        map_vis = folium.Map(location=[center_lat, center_lon], zoom_start=zoom, tiles=None)
        folium.TileLayer(tiles='http://{s}.google.com/vt/lyrs=s&x={x}&y={y}&z={z}', attr='Google Satellite', name='Google Satellite', overlay=True, control=True, subdomains=['mt0', 'mt1', 'mt2', 'mt3'], max_zoom=20).add_to(map_vis)
        if TIANDITU_KEY:
            tdt_url = f'http://t0.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={{z}}&TILEROW={{y}}&TILECOL={{x}}&tk={TIANDITU_KEY}'
            folium.TileLayer(tiles=tdt_url, attr='天地图卫星影像', name='天地图卫星影像', overlay=False, control=True, max_zoom=20).add_to(map_vis)
        folium.TileLayer('openstreetmap', name='OpenStreetMap').add_to(map_vis)
        plugins.MeasureControl(position='topright', primary_length_unit='meters', primary_area_unit='sqmeters', active_color='red', completed_color='red').add_to(map_vis)
        return map_vis
    def add_raw_trajectory_antpath_to_map(map_obj, raw_data_df: pd.DataFrame, layer_name: str, color: str = '#02affc'):
        if raw_data_df is None or raw_data_df.empty: return
        show_default = LAYER_DEFAULT_VISIBILITY.get(layer_name, True)
        layer = folium.FeatureGroup(name=layer_name, show=show_default)
        locations = raw_data_df[['latitude', 'longitude']].dropna().values.tolist()
        if locations and len(locations) >= 2:
            plugins.AntPath(locations, color=color, weight=2, opacity=0.5, delay=80000, pulse_color='#2a93c3').add_to(layer)
        layer.add_to(map_obj)
    def add_points_layer_to_map(map_obj, points_df: pd.DataFrame, layer_name: str, color: str, radius: float, tooltip_prefix: str = "点", speed_col: str = None, original_index_col: str = None, cluster_col: str = None):
        if points_df is None or points_df.empty: return
        show_default = LAYER_DEFAULT_VISIBILITY.get(layer_name, True)
        layer = folium.FeatureGroup(name=layer_name, show=show_default)
        for idx, row in points_df.iterrows():
            lat, lon = row.get('latitude'), row.get('longitude')
            if pd.isna(lat) or pd.isna(lon): continue
            popup_parts = [f"{tooltip_prefix} (DF索引: {idx})"]
            if original_index_col and original_index_col in row and pd.notna(row[original_index_col]): popup_parts.append(f"原始索引: {row[original_index_col]}")
            if 'time' in row and pd.notna(row['time']): popup_parts.append(f"时间: {row['time']}")
            if speed_col and speed_col in row and pd.notna(row[speed_col]): popup_parts.append(f"速度: {row[speed_col]:.2f} km/h")
            if cluster_col and cluster_col in row and pd.notna(row[cluster_col]): popup_parts.append(f"簇: {row[cluster_col]}")
            folium.CircleMarker(location=[lat, lon], radius=radius, color=color, fill=True, fill_color=color, fill_opacity=0.6, popup="<br>".join(popup_parts), tooltip=f"{tooltip_prefix} {idx}").add_to(layer)
        layer.add_to(map_obj)
    def add_clustered_points_layers_to_map(map_obj, clustered_data_df: pd.DataFrame, valid_cluster_ids: list, work_layer_name: str, noise_layer_name: str):
        if clustered_data_df is None or clustered_data_df.empty or 'cluster' not in clustered_data_df.columns: return
        show_work = LAYER_DEFAULT_VISIBILITY.get(work_layer_name, True); show_noise = LAYER_DEFAULT_VISIBILITY.get(noise_layer_name, True)
        work_layer = folium.FeatureGroup(name=work_layer_name, show=show_work); noise_layer = folium.FeatureGroup(name=noise_layer_name, show=show_noise)
        for idx, row in clustered_data_df.iterrows():
            cluster_id, lat, lon = row['cluster'], row['latitude'], row['longitude']
            if pd.isna(lat) or pd.isna(lon): continue
            popup = f"点索引(插值后): {idx}<br>簇: {cluster_id}"
            if 'original_index' in row: popup += f"<br>基于原索: {row['original_index']}"
            if 'time' in row : popup += f"<br>时间: {row.get('time', 'N/A')}"
            if 'speed_kmh' in row: popup += f"<br>速度: {row.get('speed_kmh', np.nan):.2f} km/h"
            if cluster_id != -1 and cluster_id in valid_cluster_ids:
                try: color = PLOT_COLORS[valid_cluster_ids.index(cluster_id) % len(PLOT_COLORS)]
                except ValueError: color = 'black'
                folium.CircleMarker(location=[lat,lon],radius=2,color=color,fill=True,fill_color=color,fill_opacity=0.7,popup=popup).add_to(work_layer)
            else: folium.CircleMarker(location=[lat,lon],radius=1.5,color='gray',fill=True,fill_color='gray',fill_opacity=0.56,popup=popup).add_to(noise_layer)
        work_layer.add_to(map_obj); noise_layer.add_to(map_obj)
    def _create_info_box_html_content(plot_info_dict: dict):
        html_parts = ['<div style="font-size: 14px; font-family: sans-serif;">']
        total_polygon_area_mu = 0; total_grid_coverage_area_mu = 0; valid_plot_count = 0
        sorted_plot_ids = sorted(plot_info_dict.keys(), key=lambda x: int(x))
        for plot_id_str in sorted_plot_ids:
            info = plot_info_dict[plot_id_str]
            if not info.get('is_valid', False): continue
            valid_plot_count +=1; total_polygon_area_mu += info['polygon_area_mu']; total_grid_coverage_area_mu += info['grid_coverage_area_mu']
            html_parts.append(f"<p><strong>地块 {plot_id_str} (簇 {info['cluster_id']})</strong><br>"
                              f"  多边形面积: {info['polygon_area_m2']} m² (约 {info['polygon_area_mu']} 亩)<br>"
                              f"  轨迹长度: {info['trajectory_length_m']} 米<br>"
                              f"  栅格覆盖面积: {info['grid_coverage_area_m2']} m² (约 {info['grid_coverage_area_mu']} 亩)<br>"
                              f"  每亩轨迹: {info['track_per_mu_m']} 米<br>"
                              f"  作业时间: {info['work_hours']:.1f} 小时<br>"
                              f"  <span style='color:#ff6600;font-weight:bold;'>工作效率: {info['work_efficiency_mu_hr']:.2f} 亩/小时</span><br>"
                              f"  <span style='color:#009688;font-weight:bold;'>行进速度(原始点): {info['avg_speed_original_points_kmh']:.1f} km/h</span></p>")
        if valid_plot_count == 0:
            html_parts.append('<p>无有效作业区域。</p>')
        else:
            html_parts.insert(1, f"<p><strong>总有效多边形面积: {round(total_polygon_area_mu, 1)} 亩</strong></p>"
                                 f"<p><strong>总有效栅格覆盖面积: {round(total_grid_coverage_area_mu, 1)} 亩</strong></p>")
        html_parts.append('</div>')
        return "".join(html_parts)
    def add_plots_visualization_to_map(map_obj, plot_info_dict: dict):
        if not plot_info_dict: return
        show_polygon = LAYER_DEFAULT_VISIBILITY.get('作业区域边界', True); show_label = LAYER_DEFAULT_VISIBILITY.get('作业区域编号', True)
        polygon_layer = folium.FeatureGroup(name='作业区域边界', show=show_polygon); label_layer = folium.FeatureGroup(name='作业区域编号', show=show_label)
        sorted_plot_ids = sorted(plot_info_dict.keys(), key=lambda x: int(x))
        for plot_id_str in sorted_plot_ids:
            plot_data = plot_info_dict[plot_id_str]
            if not plot_data.get('is_valid', False): continue
            cluster_id_for_plot = plot_data['cluster_id']; plot_numeric_id = int(plot_id_str)
            color = PLOT_COLORS[(plot_numeric_id - 1) % len(PLOT_COLORS)]
            poly_to_draw_coords_lonlat = plot_data.get('expanded_polygon_coords_lonlat', plot_data.get('outer_polygon_coords_lonlat'))
            if poly_to_draw_coords_lonlat:
                poly_folium_coords = [(lat, lon) for lon, lat in poly_to_draw_coords_lonlat]
                if len(poly_folium_coords) >= 3:
                    folium.Polygon(locations=poly_folium_coords, color='white', weight=2, fill=True, fill_color='green', fill_opacity=0.4, tooltip=f"作业区域 {plot_id_str} (簇 {cluster_id_for_plot})").add_to(polygon_layer)
            original_outer_coords = plot_data.get('outer_polygon_coords_lonlat')
            if original_outer_coords:
                lons = [c[0] for c in original_outer_coords if pd.notna(c[0])]
                lats = [c[1] for c in original_outer_coords if pd.notna(c[1])]
                if lons and lats:
                    center_lon_calc = np.mean(lons); center_lat_calc = np.mean(lats)
                    if pd.notna(center_lat_calc) and pd.notna(center_lon_calc):
                        folium.Marker(location=[center_lat_calc, center_lon_calc], icon=folium.DivIcon(html=f'<div style="font-size:16pt;color:{color};font-weight:bold;">{plot_id_str}</div>'), tooltip=f"地块 {plot_id_str}").add_to(label_layer)
        polygon_layer.add_to(map_obj); label_layer.add_to(map_obj)
        info_box_content_html = _create_info_box_html_content(plot_info_dict)
        info_box_full_html = f'''
    <div id="info-box" style="position: fixed;top: 80px;left: 20px;z-index: 1000;background: rgba(255, 255, 255, 0.92);backdrop-filter: blur(8px);-webkit-backdrop-filter: blur(8px);padding: 20px 24px;border-radius: 16px;box-shadow: 0 8px 24px rgba(0,0,0,0.15);max-height: 80vh;overflow-y: auto;font-family: 'Helvetica Neue', 'Segoe UI', 'Roboto', sans-serif;font-size: 14px;line-height: 1.6;color: #333;min-width: 320px;border: 1px solid rgba(0,0,0,0.08);">
        <h2 style="margin-top: 0; margin-bottom: 16px; font-size: 18px; font-weight: 700; display: flex; align-items: center;">
            <span style="font-size:20px; margin-right:8px;">🗂️</span>地块信息</h2>
        <div style="height:1px; background:#e0e0e0; margin:12px 0;"></div><div id="info-content">{info_box_content_html}</div></div>
    <button id="toggle-button" style="position: fixed;top: 30px;left: 20px;z-index: 1001;background: #ffffff;border: 1px solid rgba(0,0,0,0.15);border-radius: 50%;width: 36px;height: 36px;font-size: 20px;font-weight: bold;color: #333;cursor: pointer;box-shadow: 0 4px 12px rgba(0,0,0,0.2);display: flex;align-items: center;justify-content: center;transition: all 0.3s ease;">–</button>
    <script>var infoBox = document.getElementById("info-box"); var toggleButton = document.getElementById("toggle-button"); var expanded = true; toggleButton.onclick = function() {{ if (expanded) {{ infoBox.style.display = "none"; toggleButton.textContent = "+"; }} else {{ infoBox.style.display = "block"; toggleButton.textContent = "–"; }} expanded = !expanded; }};</script>'''
        map_obj.get_root().html.add_child(folium.Element(info_box_full_html))
    def add_grid_cells_layer_to_map(map_obj, all_grids_utm_indices_union: set, grid_size_m: float, transformer_to_lonlat, layer_name: str):
        if not all_grids_utm_indices_union or transformer_to_lonlat is None: return
        show_default = LAYER_DEFAULT_VISIBILITY.get(layer_name, True)
        grid_layer = folium.FeatureGroup(name=layer_name, show=show_default)
        for grid_x, grid_y in all_grids_utm_indices_union:
            x_min_utm = grid_x * grid_size_m; y_min_utm = grid_y * grid_size_m
            x_max_utm = x_min_utm + grid_size_m; y_max_utm = y_min_utm + grid_size_m
            corners_utm = [(x_min_utm, y_min_utm), (x_max_utm, y_min_utm), (x_max_utm, y_max_utm), (x_min_utm, y_max_utm)]
            corners_lonlat = [transformer_to_lonlat.transform(x,y) for x,y in corners_utm]
            polygon_locs_latlon = [(lat, lon) for lon, lat in corners_lonlat]
            folium.Polygon(locations=polygon_locs_latlon, color='green', weight=0.5, fill=True, fill_color='green', fill_opacity=0.15).add_to(grid_layer)
        grid_layer.add_to(map_obj)
def ensure_output_dirs():
    json_dir = os.path.join(OUTPUT_DIR_BASE, FINAL_OUTPUT_SUBDIR_JSON)
    maps_dir = os.path.join(OUTPUT_DIR_BASE, TEST_OUTPUT_SUBDIR_MAPS)
    os.makedirs(json_dir, exist_ok=True)
    if GENERATE_HTML_MAPS: os.makedirs(maps_dir, exist_ok=True)
    return json_dir, maps_dir
def process_single_file(input_filepath: str):
    file_basename = os.path.basename(input_filepath)
    output_prefix = os.path.splitext(file_basename)[0]
    current_timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    json_output_dir, map_output_dir = ensure_output_dirs()
    start_time_file = time.time()
    raw_data_df = load_trajectory_data(input_filepath)
    if raw_data_df is None: return False
    data_with_speed_df = calculate_trajectory_metrics(raw_data_df)
    cleaned_data_for_path_df = filter_trajectory_by_speed(data_with_speed_df, SPEED_FILTER_MIN_KMH,SPEED_FILTER_MAX_KMH)
    if cleaned_data_for_path_df.empty:
        debug_print(f"❌ {file_basename}: 速度清洗后无数据，处理终止。")
        return False
    raw_traj_lat = cleaned_data_for_path_df['latitude'].values; raw_traj_lon = cleaned_data_for_path_df['longitude'].values
    if len(raw_traj_lat) > 1 and len(raw_traj_lon) > 1:
        raw_traj_dists = calculate_haversine_distances_vectorized(raw_traj_lat[:-1], raw_traj_lon[:-1], raw_traj_lat[1:],raw_traj_lon[1:], EARTH_RADIUS_KM)
        original_cleaned_path_len_m = np.sum(raw_traj_dists) * 1000
        debug_print(f"清洗后原始轨迹总长度: {original_cleaned_path_len_m:.1f} 米")

    if 'time' in raw_data_df.columns and len(raw_data_df) > 1:
        try:
            sorted_times = raw_data_df['time'].dropna().sort_values()
            if len(sorted_times) >= 2:
                total_duration_hours = (sorted_times.iloc[-1] - sorted_times.iloc[0]).total_seconds() / 3600
                debug_print(f"原始数据总时长: {total_duration_hours:.3f} 小时")
        except Exception: pass
    interpolated_df = interpolate_trajectory_dynamically(cleaned_data_for_path_df.copy())
    if interpolated_df.empty:
        debug_print(f"❌ {file_basename}: 人造轨迹点生成失败，处理终止。")
        return False
    debug_print(f"插值点数: {len(interpolated_df)}")
    clustered_interpolated_df = cluster_trajectory_points_dbscan(interpolated_df, DBSCAN_EPS_METERS,DBSCAN_MIN_SAMPLES)
    final_clustered_df, valid_cluster_ids = filter_and_trim_clusters(clustered_interpolated_df,EFFECTIVE_MIN_CLUSTER_SIZE_POINTS,CLUSTER_TRIMMING_POINTS_COUNT)
    map_center_lat, map_center_lon, transformer_to_lonlat_global = None, None, None
    if GENERATE_HTML_MAPS:
        map_center_lat, map_center_lon = DEFAULT_MAP_CENTER_LAT, DEFAULT_MAP_CENTER_LON
        df_for_map_center = None
        if valid_cluster_ids and not final_clustered_df[final_clustered_df['cluster'].isin(valid_cluster_ids)].empty: df_for_map_center = final_clustered_df[final_clustered_df['cluster'].isin(valid_cluster_ids)]
        elif not final_clustered_df.empty: df_for_map_center = final_clustered_df
        elif not cleaned_data_for_path_df.empty: df_for_map_center = cleaned_data_for_path_df
        elif not raw_data_df.empty: df_for_map_center = raw_data_df
        if df_for_map_center is not None and not df_for_map_center.empty :
            map_center_lat_calc, map_center_lon_calc = calculate_dataframe_centroid(df_for_map_center)
            if map_center_lat_calc is not None and map_center_lon_calc is not None:
                map_center_lat, map_center_lon = map_center_lat_calc, map_center_lon_calc
                _, _, g_to_lonlat = determine_utm_projection(map_center_lat, map_center_lon)
                if g_to_lonlat: transformer_to_lonlat_global = g_to_lonlat
    all_plots_info_dict, all_valid_grids_union = {}, set()
    if valid_cluster_ids:
        all_plots_info_dict, all_valid_grids_union = analyze_plot_candidates_from_clusters(final_clustered_df, cleaned_data_for_path_df.copy(), valid_cluster_ids)
    else: debug_print(f"地块分析完成. 有效地块数: 0.")
    plot_summary_json = generate_plot_summary_json_structure(all_plots_info_dict)
    summary_json_filename = os.path.join(json_output_dir, f"{output_prefix}transformed_data_info_{current_timestamp}.json")
    with open(summary_json_filename, 'w', encoding='utf-8') as f: json.dump(plot_summary_json, f, separators=(',', ':'))
    plot_boundaries_json = generate_plot_boundaries_json_structure(all_plots_info_dict)
    boundary_json_filename = os.path.join(json_output_dir, f"{output_prefix}_boundaries_{current_timestamp}.json")
    with open(boundary_json_filename, 'w', encoding='utf-8') as f: json.dump(plot_boundaries_json, f, separators=(',', ':'))
    if GENERATE_HTML_MAPS:
        folium_map = create_folium_base_map(map_center_lat, map_center_lon, DEFAULT_MAP_ZOOM)
        add_raw_trajectory_antpath_to_map(folium_map, raw_data_df, "原始轨迹动画 (全部)")
        add_points_layer_to_map(folium_map, interpolated_df, "人造轨迹点 (插值)", color='#FF6600', radius=1)
        add_clustered_points_layers_to_map(folium_map, final_clustered_df, valid_cluster_ids, "作业点 (按簇)",
                                           "行驶/噪声点 (插值)")
        add_plots_visualization_to_map(folium_map, all_plots_info_dict)
        if transformer_to_lonlat_global: add_grid_cells_layer_to_map(folium_map, all_valid_grids_union,
                                                                     GRID_SIZE_METERS, transformer_to_lonlat_global,
                                                                     "轨迹覆盖栅格 (所有有效)")
        all_lats_for_fit = []
        all_lons_for_fit = []
        if all_plots_info_dict:
            for plot_info_item in all_plots_info_dict.values():
                if plot_info_item.get('is_valid', False) and 'outer_polygon_coords_lonlat' in plot_info_item:
                    coords = plot_info_item['outer_polygon_coords_lonlat']
                    if coords:
                        for lon_coord, lat_coord in coords:
                            if pd.notna(lat_coord) and pd.notna(lon_coord):
                                all_lons_for_fit.append(lon_coord)
                                all_lats_for_fit.append(lat_coord)

        if all_lats_for_fit and all_lons_for_fit:
            min_lat_fit, max_lat_fit = min(all_lats_for_fit), max(all_lats_for_fit)
            min_lon_fit, max_lon_fit = min(all_lons_for_fit), max(all_lons_for_fit)
            if (max_lat_fit > min_lat_fit) or (max_lon_fit > min_lon_fit):
                folium_map.fit_bounds([[min_lat_fit, min_lon_fit], [max_lat_fit, max_lon_fit]])

        folium.LayerControl().add_to(folium_map)
        map_filename = os.path.join(map_output_dir, f"{output_prefix}_map_{current_timestamp}.html")
        folium_map.save(map_filename)
    end_time_file = time.time()
    debug_print(f"耗时: {end_time_file - start_time_file:.2f} 秒\n")
    return True
if __name__ == '__main__':
    json_input_dir = r"F:\Python\作业状态识别\Json"
    debug_print(f"📂 将从目录 {json_input_dir} 读取JSON文件")
    file_paths_to_process = [os.path.join(json_input_dir, f) for f in os.listdir(json_input_dir) if f.endswith('.json')]
    if not file_paths_to_process:
        debug_print("🚫 未找到待处理的JSON文件。")
        exit()
    debug_print(f"📋 计划处理 {len(file_paths_to_process)} 个文件:")
    for file_path in file_paths_to_process: debug_print(f"  - {os.path.basename(file_path)}")
    overall_start_time = time.time(); results_summary = []
    debug_print(f"⚙️ 将串行处理文件。地图生成状态: {'启用' if GENERATE_HTML_MAPS else '禁用'}")
    for fp in file_paths_to_process:
        try:
            success = process_single_file(fp)
            results_summary.append({'file': os.path.basename(fp), 'success': success})
        except Exception as e_single_file:
            results_summary.append({'file': os.path.basename(fp), 'success': False, 'message': f"处理文件 {os.path.basename(fp)} 时发生严重错误: {e_single_file}"})
            debug_traceback_print_exc()
    successful_files = sum(1 for r in results_summary if r['success'])
    failed_files = len(results_summary) - successful_files
    overall_end_time = time.time()
    debug_print(f"\n🏁 所有文件处理完成! 总耗时: {overall_end_time - overall_start_time:.2f} 秒")
    debug_print(f"🟢 成功: {successful_files} 个文件\n🔴 失败: {failed_files} 个文件")
    if failed_files > 0:
        debug_print("\n详细失败信息:")
        for r in results_summary:
            if not r['success']: debug_print(f"  - 文件 {r['file']}: {r.get('message', '未知原因失败')}")
