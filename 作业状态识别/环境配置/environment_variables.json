{"COMPUTERNAME": "MRQ", "CONDA_DEFAULT_ENV": "base", "CONDA_EXE": "C:\\Users\\<USER>\\miniconda3\\Scripts\\conda.exe", "CONDA_PREFIX": "C:\\Users\\<USER>\\miniconda3", "CONDA_PROMPT_MODIFIER": "(base) ", "CONDA_PYTHON_EXE": "C:\\Users\\<USER>\\miniconda3\\python.exe", "CONDA_SHLVL": "1", "HOMEPATH": "\\Users\\Administrator", "PATH": "c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.python-2024.12.3-win32-x64\\python_files\\deactivate\\powershell;F:\\Python\\.venv/Scripts;C:\\Users\\<USER>\\miniconda3;C:\\Users\\<USER>\\miniconda3\\Library\\mingw-w64\\bin;C:\\Users\\<USER>\\miniconda3\\Library\\usr\\bin;C:\\Users\\<USER>\\miniconda3\\Library\\bin;C:\\Users\\<USER>\\miniconda3\\Scripts;C:\\Users\\<USER>\\miniconda3\\bin;C:\\Users\\<USER>\\miniconda3\\condabin;c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.python-2024.12.3-win32-x64\\python_files\\deactivate\\powershell;F:\\Python\\.venv\\Scripts;.;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files\\dotnet;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2024.3.2;C:\\Program Files\\Git\\cmd;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm", "PATHEXT": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL", "PSMODULEPATH": "C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules", "VIRTUAL_ENV": "F:\\Python\\.venv", "VIRTUAL_ENV_PROMPT": ".venv", "_CONDA_EXE": "C:\\Users\\<USER>\\miniconda3\\Scripts\\conda.exe", "_CONDA_ROOT": "C:\\Users\\<USER>\\miniconda3", "__CONDA_OPENSLL_CERT_FILE_SET": "1"}