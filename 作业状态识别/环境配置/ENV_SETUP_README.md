# 作业状态识别系统 - 环境配置说明

本文件夹包含以下环境配置文件，用于在其他计算机上快速部署相同的环境。

## 环境文件说明

- `requirements_full.txt`: 完整的依赖列表，包含所有已安装的Python包
- `requirements_minimal.txt`: 精简的依赖列表，过滤掉了Git仓库依赖等复杂包
- `environment_variables.json`: 相关环境变量的JSON导出
- `environment_info.json`: 详细的环境信息，包括Python版本、NumPy、BLAS等信息
- `setup_env.bat`: Windows一键配置脚本，创建虚拟环境并安装依赖

## 环境配置方法

### 方法一：使用一键配置脚本（仅Windows）

1. 双击运行 `setup_env.bat`
2. 脚本会自动创建虚拟环境并安装所有必要的依赖

### 方法二：手动配置（适用于所有平台）

1. 创建虚拟环境
   ```
   python -m venv .venv
   ```

2. 激活虚拟环境
   - Windows: `.venv\Scripts\activate`
   - Linux/Mac: `source .venv/bin/activate`

3. 安装依赖
   ```
   pip install -r requirements_minimal.txt
   ```

### 方法三：使用Conda（推荐用于复杂依赖）

如果提供了`environment.yml`文件，可以使用以下命令创建Conda环境：

```
conda env create -f environment.yml
conda activate 环境名称
```

## 常见问题

1. **NumPy导入错误**：如果遇到DLL加载失败，请确保安装了Visual C++ Redistributable，并尝试重新安装NumPy
2. **BLAS后端**：根据`environment_info.json`中的信息，当前环境使用的是{导出时的BLAS后端}后端

## 联系支持

如有任何问题，请联系[支持信息]
