{"python": {"python_version": "3.11.9", "python_implementation": "CPython", "python_compiler": "MSC v.1938 64 bit (AMD64)", "python_path": "F:\\Python\\.venv\\Scripts\\python.exe", "platform": "Windows-10-10.0.26100-SP0", "processor": "Intel64 Family 6 Model 183 Stepping 1, GenuineIntel", "architecture": ["64bit", "WindowsPE"], "system": "Windows"}, "is_conda": false, "env_prefix": "F:\\Python\\.venv", "timestamp": "2025-05-20T14:31:43.574695", "packages": [{"name": "affine", "version": "2.4.0"}, {"name": "aiofiles", "version": "24.1.0"}, {"name": "aiohappyeyeballs", "version": "2.6.1"}, {"name": "aiohttp", "version": "3.11.14"}, {"name": "aiosignal", "version": "1.3.2"}, {"name": "alphashape", "version": "1.3.1"}, {"name": "altair", "version": "5.5.0"}, {"name": "aniso8601", "version": "10.0.0"}, {"name": "annotated-types", "version": "0.7.0"}, {"name": "antlr4-python3-runtime", "version": "4.9.3"}, {"name": "anyio", "version": "4.9.0"}, {"name": "anywidget", "version": "0.9.16"}, {"name": "asttokens", "version": "3.0.0"}, {"name": "attrs", "version": "25.3.0"}, {"name": "beautifulsoup4", "version": "4.13.3"}, {"name": "blinker", "version": "1.9.0"}, {"name": "bqplot", "version": "0.12.44"}, {"name": "branca", "version": "0.8.0"}, {"name": "cachelib", "version": "0.13.0"}, {"name": "cachetools", "version": "5.5.2"}, {"name": "cdBoundary", "version": "0.8"}, {"name": "certifi", "version": "2024.8.30"}, {"name": "charset-normalizer", "version": "3.4.0"}, {"name": "click", "version": "8.1.8"}, {"name": "click-log", "version": "0.4.0"}, {"name": "click-plugins", "version": "1.1.1"}, {"name": "cligj", "version": "0.7.2"}, {"name": "clip", "version": "1.0"}, {"name": "color-operations", "version": "0.1.6"}, {"name": "colorama", "version": "0.4.6"}, {"name": "colour", "version": "0.1.5"}, {"name": "comm", "version": "0.2.2"}, {"name": "contextily", "version": "1.6.2"}, {"name": "contourpy", "version": "1.3.1"}, {"name": "cycler", "version": "0.12.1"}, {"name": "decorator", "version": "5.2.1"}, {"name": "duckdb", "version": "1.2.1"}, {"name": "efficient-sam", "version": "1.0"}, {"name": "executing", "version": "2.2.0"}, {"name": "ezdxf", "version": "1.4.1"}, {"name": "<PERSON><PERSON><PERSON>", "version": "0.115.11"}, {"name": "fastsam", "version": "0.1.1"}, {"name": "ffmpy", "version": "0.5.0"}, {"name": "filelock", "version": "3.13.1"}, {"name": "fiona", "version": "1.10.1"}, {"name": "Flask", "version": "3.1.0"}, {"name": "Flask-Caching", "version": "2.3.1"}, {"name": "flask-cors", "version": "5.0.1"}, {"name": "flask-restx", "version": "1.3.0"}, {"name": "folium", "version": "0.19.5"}, {"name": "fonttools", "version": "4.55.8"}, {"name": "frozenlist", "version": "1.5.0"}, {"name": "fsspec", "version": "2024.6.1"}, {"name": "ftfy", "version": "6.3.1"}, {"name": "GDAL", "version": "3.10.1"}, {"name": "gdown", "version": "5.2.0"}, {"name": "geographiclib", "version": "2.0"}, {"name": "g<PERSON><PERSON><PERSON>", "version": "3.2.0"}, {"name": "geopandas", "version": "1.0.1"}, {"name": "geopy", "version": "2.4.1"}, {"name": "gitdb", "version": "4.0.12"}, {"name": "GitPython", "version": "3.1.44"}, {"name": "gradio", "version": "3.35.2"}, {"name": "gradio_client", "version": "1.8.0"}, {"name": "h11", "version": "0.14.0"}, {"name": "httpcore", "version": "1.0.7"}, {"name": "httpx", "version": "0.28.1"}, {"name": "huggingface-hub", "version": "0.29.3"}, {"name": "hydra-core", "version": "1.3.2"}, {"name": "idna", "version": "3.10"}, {"name": "imageio", "version": "2.37.0"}, {"name": "importlib_resources", "version": "6.5.2"}, {"name": "intel-cmplr-lib-ur", "version": "2025.1.1"}, {"name": "intel-openmp", "version": "2025.1.1"}, {"name": "iopath", "version": "0.1.10"}, {"name": "ipyevents", "version": "2.0.2"}, {"name": "ipyfilechooser", "version": "0.6.0"}, {"name": "ipyleaflet", "version": "0.19.2"}, {"name": "ipympl", "version": "0.9.7"}, {"name": "ipython", "version": "9.0.2"}, {"name": "ipython_pygments_lexers", "version": "1.1.1"}, {"name": "i<PERSON><PERSON>", "version": "0.2.2"}, {"name": "ipyvue", "version": "1.11.2"}, {"name": "ipyvuetify", "version": "1.11.1"}, {"name": "ipywidgets", "version": "8.1.5"}, {"name": "itsdangerous", "version": "2.2.0"}, {"name": "jedi", "version": "0.19.2"}, {"name": "Jinja2", "version": "3.1.4"}, {"name": "joblib", "version": "1.4.2"}, {"name": "jsonschema", "version": "4.23.0"}, {"name": "jsonschema-specifications", "version": "2024.10.1"}, {"name": "jupyter-leaflet", "version": "0.19.2"}, {"name": "jupyterlab_widgets", "version": "3.0.13"}, {"name": "kiwisolver", "version": "1.4.8"}, {"name": "lazy_loader", "version": "0.4"}, {"name": "leafmap", "version": "0.42.13"}, {"name": "linkify-it-py", "version": "2.0.3"}, {"name": "localtileserver", "version": "0.10.6"}, {"name": "markdown-it-py", "version": "2.2.0"}, {"name": "MarkupSafe", "version": "3.0.2"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "3.10.0"}, {"name": "matplotlib-inline", "version": "0.1.7"}, {"name": "mdit-py-plugins", "version": "0.3.3"}, {"name": "mdurl", "version": "0.1.2"}, {"name": "mercantile", "version": "1.2.1"}, {"name": "mkl", "version": "2025.1.0"}, {"name": "mobile-sam", "version": "1.0"}, {"name": "morecantile", "version": "6.2.0"}, {"name": "mpmath", "version": "1.3.0"}, {"name": "multidict", "version": "6.2.0"}, {"name": "narwhals", "version": "1.31.0"}, {"name": "networkx", "version": "3.3"}, {"name": "numexpr", "version": "2.10.2"}, {"name": "numpy", "version": "2.1.1"}, {"name": "omegaconf", "version": "2.3.0"}, {"name": "onnx", "version": "1.17.0"}, {"name": "opencv-python", "version": "*********"}, {"name": "opencv-python-headless", "version": "*********"}, {"name": "<PERSON><PERSON><PERSON>", "version": "3.10.15"}, {"name": "packaging", "version": "24.2"}, {"name": "pandas", "version": "2.2.3"}, {"name": "parso", "version": "0.8.4"}, {"name": "patool", "version": "4.0.0"}, {"name": "pillow", "version": "11.0.0"}, {"name": "pip", "version": "25.0.1"}, {"name": "pip-autoremove", "version": "0.10.0"}, {"name": "plotly", "version": "6.0.1"}, {"name": "<PERSON><PERSON><PERSON>", "version": "3.1.1"}, {"name": "prompt_toolkit", "version": "3.0.50"}, {"name": "propcache", "version": "0.3.0"}, {"name": "protobuf", "version": "5.29.4"}, {"name": "psutil", "version": "7.0.0"}, {"name": "psygnal", "version": "0.12.0"}, {"name": "pure_eval", "version": "0.2.3"}, {"name": "py-cpuinfo", "version": "9.0.0"}, {"name": "p<PERSON><PERSON>", "version": "19.0.1"}, {"name": "pycocotools", "version": "2.0.8"}, {"name": "pydantic", "version": "2.10.6"}, {"name": "pydantic_core", "version": "2.27.2"}, {"name": "<PERSON><PERSON><PERSON>", "version": "0.9.1"}, {"name": "pydub", "version": "0.25.1"}, {"name": "Pygments", "version": "2.19.1"}, {"name": "pyogrio", "version": "0.11.0"}, {"name": "pyparsing", "version": "3.2.1"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "3.7.0"}, {"name": "pyshp", "version": "2.3.1"}, {"name": "PySocks", "version": "1.7.1"}, {"name": "pystac", "version": "1.12.2"}, {"name": "pystac-client", "version": "0.8.6"}, {"name": "pytesseract", "version": "0.3.13"}, {"name": "python-box", "version": "7.3.2"}, {"name": "python-dateutil", "version": "2.9.0.post0"}, {"name": "python-multipart", "version": "0.0.20"}, {"name": "pytz", "version": "2024.2"}, {"name": "pywin32", "version": "310"}, {"name": "PyYAML", "version": "6.0.2"}, {"name": "rasterio", "version": "1.4.3"}, {"name": "referencing", "version": "0.36.2"}, {"name": "regex", "version": "2024.11.6"}, {"name": "requests", "version": "2.32.3"}, {"name": "rio-cogeo", "version": "5.4.1"}, {"name": "rio-tiler", "version": "7.5.1"}, {"name": "rioxarray", "version": "0.18.2"}, {"name": "rpds-py", "version": "0.23.1"}, {"name": "rtree", "version": "1.4.0"}, {"name": "safetensors", "version": "0.5.3"}, {"name": "SAM-2", "version": "1.0", "editable_project_location": "F:\\Python\\Sam2farm\\segment-anything-2"}, {"name": "sam2", "version": "1.1.0"}, {"name": "scikit-image", "version": "0.25.2"}, {"name": "scikit-learn", "version": "1.6.0"}, {"name": "scipy", "version": "1.14.1"}, {"name": "scooby", "version": "0.10.0"}, {"name": "seaborn", "version": "0.13.2"}, {"name": "segment-anything", "version": "1.0"}, {"name": "segment-anything-hq", "version": "0.3"}, {"name": "segment-anything-py", "version": "1.0.1"}, {"name": "segment-geospatial", "version": "0.12.4"}, {"name": "semantic-version", "version": "2.10.0"}, {"name": "server-thread", "version": "0.3.0"}, {"name": "setuptools", "version": "68.2.0"}, {"name": "shapely", "version": "2.0.6"}, {"name": "six", "version": "1.17.0"}, {"name": "smmap", "version": "5.0.2"}, {"name": "sniffio", "version": "1.3.1"}, {"name": "soupsieve", "version": "2.6"}, {"name": "stack-data", "version": "0.6.3"}, {"name": "starlette", "version": "0.46.1"}, {"name": "streamlit", "version": "1.44.0"}, {"name": "sympy", "version": "1.13.3"}, {"name": "tbb", "version": "2022.1.0"}, {"name": "tcmlib", "version": "1.3.0"}, {"name": "tenacity", "version": "9.0.0"}, {"name": "threadpoolctl", "version": "3.5.0"}, {"name": "tifffile", "version": "2025.3.13"}, {"name": "timm", "version": "1.0.15"}, {"name": "toml", "version": "0.10.2"}, {"name": "torch", "version": "2.7.0+cu118"}, {"name": "<PERSON><PERSON><PERSON>", "version": "2.7.0+cu118"}, {"name": "torchvision", "version": "0.22.0+cu118"}, {"name": "tornado", "version": "6.4.2"}, {"name": "tqdm", "version": "4.67.1"}, {"name": "traitlets", "version": "5.14.3"}, {"name": "traittypes", "version": "0.2.1"}, {"name": "<PERSON><PERSON>", "version": "4.6.8"}, {"name": "typing_extensions", "version": "4.12.2"}, {"name": "tzdata", "version": "2024.2"}, {"name": "uc-micro-py", "version": "1.0.3"}, {"name": "ultralytics", "version": "8.3.94"}, {"name": "ultralytics-thop", "version": "2.0.14"}, {"name": "umf", "version": "0.10.0"}, {"name": "urllib3", "version": "2.3.0"}, {"name": "u<PERSON><PERSON>", "version": "0.34.0"}, {"name": "watchdog", "version": "6.0.0"}, {"name": "wcwidth", "version": "0.2.13"}, {"name": "websockets", "version": "15.0.1"}, {"name": "Werkzeug", "version": "3.1.3"}, {"name": "wheel", "version": "0.41.2"}, {"name": "whitebox", "version": "2.3.6"}, {"name": "whiteboxgui", "version": "2.3.0"}, {"name": "widgetsnbextension", "version": "4.0.13"}, {"name": "xarray", "version": "2025.3.0"}, {"name": "xlrd", "version": "1.2.0"}, {"name": "xyzservices", "version": "2024.9.0"}, {"name": "yarl", "version": "1.18.3"}], "numpy": {"version": "2.1.1", "file": "F:\\Python\\.venv\\Lib\\site-packages\\numpy\\__init__.py"}, "mkl": "不可用", "blas_backend": "OpenBLAS"}