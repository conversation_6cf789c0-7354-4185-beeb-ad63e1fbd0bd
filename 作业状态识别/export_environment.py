#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
环境配置导出工具
导出Python环境配置(包括依赖项和环境变量)，方便环境迁移和对比
"""

import os
import sys
import platform
import subprocess
import json
from datetime import datetime
import argparse

def get_python_info():
    """获取Python解释器信息"""
    return {
        "python_version": platform.python_version(),
        "python_path": sys.executable,
        "platform": platform.platform(),
        "processor": platform.processor(),
        "python_compiler": platform.python_compiler(),
        "system": platform.system(),
        "architecture": platform.architecture(),
    }

def get_packages_info():
    """获取已安装包信息"""
    try:
        result = subprocess.run(
            [sys.executable, "-m", "pip", "freeze"],
            capture_output=True,
            text=True,
            check=True
        )
        packages = result.stdout.strip().split("\n")
        
        # 将包信息转换为字典
        packages_dict = {}
        for pkg in packages:
            if "==" in pkg:
                name, version = pkg.split("==", 1)
                packages_dict[name] = version
        
        return packages_dict
    except subprocess.CalledProcessError:
        return {"error": "无法获取包信息"}

def get_environment_variables():
    """获取环境变量"""
    env_vars = {}
    
    # 筛选与数值计算相关的环境变量
    important_vars = [
        "PATH", "PYTHONPATH", "VIRTUAL_ENV", 
        "OMP_NUM_THREADS", "OPENBLAS_NUM_THREADS", "MKL_NUM_THREADS", 
        "NUMEXPR_NUM_THREADS", "VECLIB_MAXIMUM_THREADS", "NUMBA_NUM_THREADS"
    ]
    
    for var in important_vars:
        if var in os.environ:
            env_vars[var] = os.environ[var]
            
    return env_vars

def create_minimal_requirements(packages):
    """创建最小需求文件内容"""
    core_packages = [
        "numpy", "pandas", "scikit-learn", "scipy", 
        "matplotlib", "pyproj", "shapely", "folium"
    ]
    
    minimal_reqs = {}
    for pkg in core_packages:
        if pkg in packages:
            minimal_reqs[pkg] = packages[pkg]
    
    return "\n".join([f"{pkg}=={ver}" for pkg, ver in minimal_reqs.items()])

def format_export_data(format_type, data):
    """根据指定格式输出数据"""
    python_info = data["python_info"]
    packages = data["packages"]
    env_vars = data["environment_variables"]
    
    if format_type == "json":
        return json.dumps(data, indent=2)
    
    elif format_type == "requirements":
        return "\n".join([f"{pkg}=={ver}" for pkg, ver in packages.items()])
    
    elif format_type == "minimal_requirements":
        return create_minimal_requirements(packages)
    
    elif format_type == "env_vars":
        return "\n".join([f"{var}={value}" for var, value in env_vars.items()])
    
    elif format_type == "batch_script":
        lines = [
            "@echo off",
            "echo 设置优化环境变量...",
            "",
            ":: 设置数值计算优化环境变量",
        ]
        
        # 添加环境变量设置命令
        for var in ["OMP_NUM_THREADS", "OPENBLAS_NUM_THREADS", "MKL_NUM_THREADS", "NUMEXPR_NUM_THREADS"]:
            val = env_vars.get(var, "8")  # 默认值为8
            lines.append(f"set {var}={val}")
        
        lines.extend([
            "",
            ":: 激活Python环境 (如果使用虚拟环境)",
            "if exist .venv\\Scripts\\activate.bat (",
            "    call .venv\\Scripts\\activate.bat",
            ")",
            "",
            "echo 环境设置完成!",
            "echo 现在可以运行Python脚本了",
            "",
            ":: 示例: python track12_20250519.py",
            "",
            "pause"
        ])
        
        return "\n".join(lines)
    
    elif format_type == "shell_script":
        lines = [
            "#!/bin/bash",
            "echo \"设置优化环境变量...\"",
            "",
            "# 设置数值计算优化环境变量"
        ]
        
        # 添加环境变量设置命令
        for var in ["OMP_NUM_THREADS", "OPENBLAS_NUM_THREADS", "MKL_NUM_THREADS", "NUMEXPR_NUM_THREADS"]:
            val = env_vars.get(var, "8")  # 默认值为8
            lines.append(f"export {var}={val}")
        
        lines.extend([
            "",
            "# 激活Python环境 (如果使用虚拟环境)",
            "if [ -f .venv/bin/activate ]; then",
            "    source .venv/bin/activate",
            "fi",
            "",
            "echo \"环境设置完成!\"",
            "echo \"现在可以运行Python脚本了\"",
            "",
            "# 示例: python track12_20250519.py"
        ])
        
        return "\n".join(lines)
    
    else:  # text
        lines = [
            f"Python环境信息导出 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "-" * 50,
            f"Python版本: {python_info['python_version']}",
            f"Python路径: {python_info['python_path']}",
            f"平台: {python_info['platform']}",
            f"处理器: {python_info['processor']}",
            f"系统: {python_info['system']}",
            "-" * 50,
            "已安装包:",
        ]
        
        for pkg, ver in sorted(packages.items()):
            lines.append(f"  {pkg}=={ver}")
            
        lines.extend([
            "-" * 50,
            "环境变量:",
        ])
        
        for var, value in env_vars.items():
            lines.append(f"  {var}={value}")
            
        return "\n".join(lines)

def export_environment(format_type="text", output_file=None):
    """导出环境信息"""
    data = {
        "python_info": get_python_info(),
        "packages": get_packages_info(),
        "environment_variables": get_environment_variables(),
        "export_time": datetime.now().isoformat()
    }
    
    formatted_output = format_export_data(format_type, data)
    
    if output_file:
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(formatted_output)
        print(f"环境信息已保存到 {output_file}")
    else:
        print(formatted_output)
    
    return formatted_output

def main():
    parser = argparse.ArgumentParser(description="导出Python环境配置")
    parser.add_argument("--format", "-f", choices=["text", "json", "requirements", "minimal_requirements", "env_vars", "batch_script", "shell_script"], 
                        default="text", help="输出格式")
    parser.add_argument("--output", "-o", help="输出文件名，不指定则输出到控制台")
    parser.add_argument("--all", "-a", action="store_true", help="生成所有格式的输出文件")
    
    args = parser.parse_args()
    
    if args.all:
        # 生成所有格式的输出
        formats = {
            "text": "环境信息.txt",
            "json": "环境信息.json",
            "requirements": "requirements.txt",
            "minimal_requirements": "requirements_minimal.txt",
            "env_vars": "环境变量.txt",
            "batch_script": "setup_env.bat",
            "shell_script": "setup_env.sh"
        }
        
        for fmt, filename in formats.items():
            export_environment(fmt, filename)
        
        print(f"已生成全部{len(formats)}种格式的环境配置文件")
    else:
        export_environment(args.format, args.output)

if __name__ == "__main__":
    main() 