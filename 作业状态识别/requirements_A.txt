affine==2.4.0
aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.11.14
aiosignal==1.3.2
alphashape==1.3.1
altair==5.5.0
aniso8601==10.0.0
annotated-types==0.7.0
antlr4-python3-runtime==4.9.3
anyio==4.9.0
anywidget==0.9.16
asttokens==3.0.0
attrs==25.3.0
beautifulsoup4==4.13.3
blinker==1.9.0
bqplot==0.12.44
branca==0.8.0
cachelib==0.13.0
cachetools==5.5.2
cdBoundary==0.8
certifi==2024.8.30
charset-normalizer==3.4.0
click==8.1.8
click-log==0.4.0
click-plugins==1.1.1
cligj==0.7.2
clip @ git+https://github.com/openai/CLIP.git@a1d071733d7111c9c014f024669f959182114e33
color-operations==0.1.6
colorama==0.4.6
colour==0.1.5
comm==0.2.2
contextily==1.6.2
contourpy==1.3.1
cycler==0.12.1
decorator==5.2.1
duckdb==1.2.1
efficient-sam @ git+https://github.com/yformer/EfficientSAM.git@d525f622e6f640acf5a0fc37c7ca1f243da5bde0
executing==2.2.0
ezdxf==1.4.1
fastapi==0.115.11
fastsam @ git+https://github.com/CASIA-IVA-Lab/FastSAM.git@b4ed20c2fed75eadc5aa7d8b09fedd137b873b52
ffmpy==0.5.0
filelock==3.13.1
fiona==1.10.1
Flask==3.1.0
Flask-Caching==2.3.1
flask-cors==5.0.1
flask-restx==1.3.0
folium==0.19.5
fonttools==4.55.8
frozenlist==1.5.0
fsspec==2024.6.1
ftfy==6.3.1
GDAL @ file:///F:/Python/GDAL-3.10.1-cp311-cp311-win_amd64.whl#sha256=5def57b3c5512a5f6441497fc45b47d9aa83b1f1a56eb25a9c79dc1056ae1978
gdown==5.2.0
geographiclib==2.0
geojson==3.2.0
geopandas==1.0.1
geopy==2.4.1
gitdb==4.0.12
GitPython==3.1.44
gradio==3.35.2
gradio_client==1.8.0
h11==0.14.0
httpcore==1.0.7
httpx==0.28.1
huggingface-hub==0.29.3
hydra-core==1.3.2
idna==3.10
imageio==2.37.0
importlib_resources==6.5.2
iopath==0.1.10
ipyevents==2.0.2
ipyfilechooser==0.6.0
ipyleaflet==0.19.2
ipympl==0.9.7
ipython==9.0.2
ipython_pygments_lexers==1.1.1
ipytree==0.2.2
ipyvue==1.11.2
ipyvuetify==1.11.1
ipywidgets==8.1.5
itsdangerous==2.2.0
jedi==0.19.2
Jinja2==3.1.4
joblib==1.4.2
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
jupyter-leaflet==0.19.2
jupyterlab_widgets==3.0.13
kiwisolver==1.4.8
lazy_loader==0.4
leafmap==0.42.13
linkify-it-py==2.0.3
localtileserver==0.10.6
markdown-it-py==2.2.0
MarkupSafe==3.0.2
matplotlib==3.10.0
matplotlib-inline==0.1.7
mdit-py-plugins==0.3.3
mdurl==0.1.2
mercantile==1.2.1
mobile-sam @ git+https://github.com/ChaoningZhang/MobileSAM.git@34bbbfdface3c18e5221aa7de6032d7220c6c6a1
morecantile==6.2.0
mpmath==1.3.0
multidict==6.2.0
narwhals==1.31.0
networkx==3.3
numexpr==2.10.2
numpy==2.1.1
omegaconf==2.3.0
onnx==1.17.0
opencv-python==*********
opencv-python-headless==*********
orjson==3.10.15
packaging==24.2
pandas==2.2.3
parso==0.8.4
patool==4.0.0
pillow==11.0.0
pip-autoremove==0.10.0
plotly==6.0.1
portalocker==3.1.1
prompt_toolkit==3.0.50
propcache==0.3.0
protobuf==5.29.4
psutil==7.0.0
psygnal==0.12.0
pure_eval==0.2.3
py-cpuinfo==9.0.0
pyarrow==19.0.1
pycocotools==2.0.8
pydantic==2.10.6
pydantic_core==2.27.2
pydeck==0.9.1
pydub==0.25.1
Pygments==2.19.1
pyogrio==0.11.0
pyparsing==3.2.1
pyproj==3.7.0
pyshp==2.3.1
PySocks==1.7.1
pystac==1.12.2
pystac-client==0.8.6
pytesseract==0.3.13
python-box==7.3.2
python-dateutil==2.9.0.post0
python-multipart==0.0.20
pytz==2024.2
pywin32==310
PyYAML==6.0.2
rasterio==1.4.3
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
rio-cogeo==5.4.1
rio-tiler==7.5.1
rioxarray==0.18.2
rpds-py==0.23.1
rtree==1.4.0
safetensors==0.5.3
# Editable Git install with no remote (SAM-2==1.0)
-e f:\python\sam2farm\segment-anything-2
sam2==1.1.0
scikit-image==0.25.2
scikit-learn==1.6.0
scipy==1.14.1
scooby==0.10.0
seaborn==0.13.2
segment-anything @ git+https://github.com/facebookresearch/segment-anything.git@dca509fe793f601edb92606367a655c15ac00fdf
segment-anything-hq==0.3
segment-anything-py==1.0.1
segment-geospatial @ git+https://github.com/opengeos/segment-geospatial@42a5f92abf9fa8616487228f8a54d3fbf240fa5f
semantic-version==2.10.0
server-thread==0.3.0
shapely==2.0.6
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
soupsieve==2.6
stack-data==0.6.3
starlette==0.46.1
streamlit==1.44.0
sympy==1.13.3
tenacity==9.0.0
threadpoolctl==3.5.0
tifffile==2025.3.13
timm==1.0.15
toml==0.10.2
torch==2.7.0+cu118
torchaudio==2.7.0+cu118
torchvision==0.22.0+cu118
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
traittypes==0.2.1
trimesh==4.6.8
typing_extensions==4.12.2
tzdata==2024.2
uc-micro-py==1.0.3
ultralytics==8.3.94
ultralytics-thop==2.0.14
urllib3==2.3.0
uvicorn==0.34.0
watchdog==6.0.0
wcwidth==0.2.13
websockets==15.0.1
Werkzeug==3.1.3
whitebox==2.3.6
whiteboxgui==2.3.0
widgetsnbextension==4.0.13
xarray==2025.3.0
xlrd==1.2.0
xyzservices==2024.9.0
yarl==1.18.3
