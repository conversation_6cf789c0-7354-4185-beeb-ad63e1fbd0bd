import json
import math
import pandas as pd


# import os # 如果不需要检查文件存在或创建目录，可以不导入

# 坐标转换相关函数 (这些函数保持不变)
# _transformlat, _transformlng, gcj02_to_wgs84 省略，与您原代码相同...
def _transformlat(lng, lat):
    ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * math.sqrt(abs(lng))
    ret += (20.0 * math.sin(6.0 * lng * math.pi) + 20.0 * math.sin(2.0 * lng * math.pi)) * 2.0 / 3.0
    ret += (20.0 * math.sin(lat * math.pi) + 40.0 * math.sin(lat / 3.0 * math.pi)) * 2.0 / 3.0
    ret += (160.0 * math.sin(lat / 12.0 * math.pi) + 320.0 * math.sin(lat * math.pi / 30.0)) * 2.0 / 3.0
    return ret


def _transformlng(lng, lat):
    ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * math.sqrt(abs(lng))
    ret += (20.0 * math.sin(6.0 * lng * math.pi) + 20.0 * math.sin(2.0 * lng * math.pi)) * 2.0 / 3.0
    ret += (20.0 * math.sin(lng * math.pi) + 40.0 * math.sin(lng / 3.0 * math.pi)) * 2.0 / 3.0
    ret += (150.0 * math.sin(lng / 12.0 * math.pi) + 300.0 * math.sin(lng / 30.0 * math.pi)) * 2.0 / 3.0
    return ret


def gcj02_to_wgs84(lng, lat):
    a = 6378245.0;
    ee = 0.00669342162296594323;
    pi = math.pi
    if not (-180 <= lng <= 180 and -90 <= lat <= 90): return lng, lat  # 保持不变
    dlat = _transformlat(lng - 105.0, lat - 35.0)
    dlng = _transformlng(lng - 105.0, lat - 35.0)
    radlat = lat / 180.0 * pi
    magic = math.sin(radlat)
    magic = 1 - ee * magic * magic
    if magic < 0: magic = 0
    sqrtmagic = math.sqrt(magic)
    if sqrtmagic == 0: return lng, lat  # 保持不变
    dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * pi)
    dlng = (dlng * 180.0) / (a / sqrtmagic * math.cos(radlat) * pi)
    mglat = lat + dlat
    mglng = lng + dlng
    return lng * 2 - mglng, lat * 2 - mglat


# 主程序 (保持函数名和内部逻辑基本不变，只在调用处循环)
def convert_json_gcj02_to_wgs84(input_file):  # 函数本身仍然处理单个文件
    print(f"\n🔄 开始处理文件: {input_file}")  # 加个换行和提示
    try:
        # 读取json
        df = pd.read_json(input_file)
        if not {'latitude', 'longitude'}.issubset(df.columns):
            print(f"❌ 文件 {input_file} 缺少必要列: latitude / longitude")
            return

        print(f"✅ 成功加载 {len(df)} 条记录，开始坐标转换...")

        # 转换每一行
        # (可选) 确保经纬度为数值型，对于非常干净的数据可能不需要
        # df['longitude'] = pd.to_numeric(df['longitude'], errors='coerce')
        # df['latitude'] = pd.to_numeric(df['latitude'], errors='coerce')
        # df.dropna(subset=['longitude', 'latitude'], inplace=True) # 如果做了上面的转换，可能需要dropna

        new_coords = df[['longitude', 'latitude']].apply(lambda row: gcj02_to_wgs84(row['longitude'], row['latitude']),
                                                         axis=1)
        # 检查new_coords是否为空或长度是否匹配，以防万一
        if new_coords.empty and not df.empty:
            print(f"⚠️ 坐标转换后没有有效数据，原文件 {input_file} 有 {len(df)} 条记录。")
            # return # 或者继续尝试保存空结果，取决于期望行为
        elif not new_coords.empty:
            df['longitude'], df['latitude'] = zip(*new_coords)
        # 如果 df 本来就是空的，new_coords也会是空的，zip会出错，但 to_json 空df是OK的

        # 保存
        output_file = input_file.replace('.json', '_wgs84.json')
        # 如果输入文件名不包含.json，上面的replace会无效，可以加个判断
        if not output_file.endswith("_wgs84.json"):
            output_file = f"{input_file}_wgs84.json"

        df.to_json(output_file, orient='records', force_ascii=False, indent=4)  # 增加indent
        print(f"✅ 坐标转换完成，保存为: {output_file}")

    except FileNotFoundError:
        print(f"❌ 文件未找到: {input_file}")
    except Exception as e:
        print(f"❌ 处理文件 {input_file} 时出错: {e}")


# 调用示例
if __name__ == "__main__":
    # 🛠️ 这里改成你的输入文件名列表
    input_filenames = [
        r"F:\Python\作业状态识别\Json\程总\861564060862953_2024-10-01.json",
        r"F:\Python\作业状态识别\Json\程总\861564060862953_2024-10-06.json",
        r"F:\Python\作业状态识别\Json\程总\861564060862953_2024-10-07.json",
        r"F:\Python\作业状态识别\Json\程总\861564060862953_2024-10-08.json",
        r"F:\Python\作业状态识别\Json\程总\861564060866384_2024-10-13.json",
        r"F:\Python\作业状态识别\Json\程总\861564060867770_2024-09-20.json",
        r"F:\Python\作业状态识别\Json\程总\861564060867770_2024-09-23.json",
        r"F:\Python\作业状态识别\Json\程总\861564060867770_2024-09-26.json",
        r"F:\Python\作业状态识别\Json\程总\861564060883512_2023-10-27.json",
        r"F:\Python\作业状态识别\Json\程总\861564060883850_2024-08-14.json",
        r"F:\Python\作业状态识别\Json\程总\861564060883850_2024-10-20.json",
        r"F:\Python\作业状态识别\Json\程总\861564060884031_2024-09-23.json",
        r"F:\Python\作业状态识别\Json\程总\861564060884031_2024-09-26.json",
        r"F:\Python\作业状态识别\Json\程总\861564060884031_2024-09-29.json",
        r"F:\Python\作业状态识别\Json\程总\861564060884031_2024-10-04.json",
        r"F:\Python\作业状态识别\Json\程总\861564060884031_2024-10-07.json",
        r"F:\Python\作业状态识别\Json\程总\861564060809087_2025-01-06.json",
        r"F:\Python\作业状态识别\Json\程总\861564060809327_2024-10-25.json",
        r"F:\Python\作业状态识别\Json\程总\861564060815639_2024-06-12.json",
        r"F:\Python\作业状态识别\Json\程总\861564060847525_2024-10-12.json",
        r"F:\Python\作业状态识别\Json\程总\861564060847525_2025-01-06.json",
        r"F:\Python\作业状态识别\Json\程总\861564060847525_2025-01-09.json",
        r"F:\Python\作业状态识别\Json\程总\861564060853002_2024-09-23.json",
        r"F:\Python\作业状态识别\Json\程总\861564060862037_2024-10-10.json",
        r"F:\Python\作业状态识别\Json\程总\861564060862037_2024-10-11.json",
        r"F:\Python\作业状态识别\Json\程总\861564060862839_2023-10-27.json",
        r"F:\Python\作业状态识别\Json\程总\861564060862953_2024-09-20.json",
        r"F:\Python\作业状态识别\Json\程总\861564060862953_2024-09-24.json",
        r"F:\Python\作业状态识别\Json\程总\861564060862953_2024-09-26.json",
        r"F:\Python\作业状态识别\Json\程总\861564060862953_2024-09-29.json",
    ]

    # 简单地循环处理列表中的每个文件
    for filename in input_filenames:
        convert_json_gcj02_to_wgs84(filename)

    print("\n✨ 所有指定文件处理完毕。")
