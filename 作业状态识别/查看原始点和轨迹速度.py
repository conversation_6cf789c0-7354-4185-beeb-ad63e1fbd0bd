import os
import json
from datetime import datetime
from math import radians, sin, cos, asin
import folium
from folium.plugins import MeasureControl
from branca.element import Template, MacroElement
from concurrent.futures import ProcessPoolExecutor
import multiprocessing

def extract_records(file_path):
    records = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for r in json.load(f):
            try:
                lat = float(r.get('latitude')); lon = float(r.get('longitude'))
            except:
                continue
            ts = r.get('time')
            if not ts: continue
            t = datetime.strptime(ts, '%Y-%m-%d %H:%M:%S')
            records.append((lat, lon, t))
    print(f"  → loaded {len(records)} records from {os.path.basename(file_path)}")
    return records

def haversine(lat1, lon1, lat2, lon2):
    lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])
    dlon = lon2 - lon1; dlat = lat2 - lat1
    a = sin(dlat/2)**2 + cos(lat1)*cos(lat2)*sin(dlon/2)**2
    return 6371 * 2 * asin(a**0.5)

def get_color(speed):
    if speed <= 5:    return 'green'
    if speed <= 10:   return 'blue'
    if speed <= 15:   return 'orange'
    if speed <= 20:   return 'red'
    return 'darkred'

def create_map(records, output):
    m = folium.Map(location=(records[0][0], records[0][1]), zoom_start=17)
    folium.TileLayer(
        tiles='http://{s}.google.com/vt/lyrs=s&x={x}&y={y}&z={z}',
        attr='Google', name='Google卫星影像',
        overlay=True, control=True,
        subdomains=['mt0','mt1','mt2','mt3'], max_zoom=20
    ).add_to(m)
    traj_fg = folium.FeatureGroup(name='轨迹', show=True).add_to(m)
    pts_fg  = folium.FeatureGroup(name='坐标点', show=False).add_to(m)
    for (lat1, lon1, t1), (lat2, lon2, t2) in zip(records, records[1:]):
        dist = haversine(lat1, lon1, lat2, lon2)
        hrs  = (t2 - t1).total_seconds() / 3600
        speed = dist/hrs if hrs>0 else 0
        folium.PolyLine(
            locations=[(lat1, lon1), (lat2, lon2)],
            color=get_color(speed), weight=3
        ).add_to(traj_fg)
    for lat, lon, _ in records:
        folium.CircleMarker(
            location=(lat, lon), radius=3,
            color='orange', fill=True, fill_color='orange'
        ).add_to(pts_fg)
    m.add_child(MeasureControl(primary_length_unit='kilometers', primary_area_unit='sqmeters'))
    legend_html = """
    {% macro html(this, kwargs) %}
    <div style="position:fixed;bottom:50px;left:50px;width:160px;background:white;
                border:2px solid grey;z-index:9999;font-size:14px;padding:10px;">
      <b>速度图例</b><br>
      <i style="background:green;width:12px;height:12px;display:inline-block;margin-right:5px;"></i>&lt;=5 km/h<br>
      <i style="background:blue;width:12px;height:12px;display:inline-block;margin-right:5px;"></i>&lt;=10 km/h<br>
      <i style="background:orange;width:12px;height:12px;display:inline-block;margin-right:5px;"></i>&lt;=15 km/h<br>
      <i style="background:red;width:12px;height:12px;display:inline-block;margin-right:5px;"></i>&lt;=20 km/h<br>
      <i style="background:darkred;width:12px;height:12px;display:inline-block;margin-right:5px;"></i>&gt;20 km/h
    </div>
    {% endmacro %}
    """
    legend = MacroElement(); legend._template = Template(legend_html)
    m.get_root().add_child(legend)
    folium.LayerControl().add_to(m)
    m.save(output)
    print(f"  ✔ saved {os.path.basename(output)}")

def process_file(args):
    fname, idx, total, input_dir, output_dir = args
    print(f"[{idx}/{total}] {fname}")
    in_path = os.path.join(input_dir, fname)
    base = os.path.splitext(fname)[0]
    out_path = os.path.join(output_dir, f'{base}_speed_layers.html')
    recs = extract_records(in_path)
    if recs:
        create_map(recs, out_path)

if __name__ == '__main__':
    input_dir = r'F:\Python\作业状态识别\Json'
    output_dir = r'F:\Python\作业状态识别\原始轨迹和速度'
    os.makedirs(output_dir, exist_ok=True)
    files = [f for f in os.listdir(input_dir) if f.lower().endswith('.json')]
    print(f"Processing {len(files)} files from {input_dir}")
    cpu_cnt = multiprocessing.cpu_count()
    args_list = [(f, idx, len(files), input_dir, output_dir) for idx, f in enumerate(files, 1)]
    with ProcessPoolExecutor(max_workers=cpu_cnt) as executor:
        executor.map(process_file, args_list)
    print("All done.")
