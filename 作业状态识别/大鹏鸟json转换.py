import json
import os
from datetime import datetime

def convert_time_format(time_str_a): # 时间格式转换 YYYYMMDDHHMMSS -> YYYY-MM-DD HH:MM:SS
    try: return datetime.strptime(time_str_a, "%Y%m%d%H%M%S").strftime("%Y-%m-%d %H:%M:%S")
    except ValueError: raise ValueError(f"时间格式无效: {time_str_a}")

def transform_item(item_a): # 单条数据转换: 经纬度转浮点数, 时间转格式
    return {'latitude': float(item_a['latitude']), 'longitude': float(item_a['longitude']), 'time': convert_time_format(item_a['time'])}

def convert_json_content(data_a): # JSON内容整体转换
    if not isinstance(data_a, list): raise TypeError(f"输入应为列表,得到{type(data_a)}")
    data_b = []
    for item_a in data_a: # 逐条处理, 跳过非字典或转换失败的条目
        if isinstance(item_a, dict):
            try: data_b.append(transform_item(item_a))
            except (KeyError, ValueError): pass # 转换失败则静默跳过该条
    return data_b

def process_json_file(input_filepath, output_filepath): # 处理单个JSON文件: 读取,转换,写入
    try:
        with open(input_filepath, 'r', encoding='utf-8') as f_in: data_a = json.load(f_in)
        data_b = convert_json_content(data_a)
        os.makedirs(os.path.dirname(output_filepath), exist_ok=True)
        with open(output_filepath, 'w', encoding='utf-8') as f_out: json.dump(data_b, f_out, indent=4, ensure_ascii=False)
        return True
    except (FileNotFoundError, json.JSONDecodeError, TypeError, IOError, ValueError) as e:
        print(f"错误: {os.path.basename(input_filepath)} 处理失败 - {e}")
        return False
    except Exception as e: # 捕获其他所有未预料到的错误
        print(f"未知错误: {os.path.basename(input_filepath)} 处理失败 - {e}")
        return False

def batch_convert_json_files(input_dir, output_base_dir): # 批量转换指定目录内所有JSON文件
    input_dir, output_base_dir = os.path.normpath(input_dir), os.path.normpath(output_base_dir)
    if not os.path.isdir(input_dir): print(f"错误: 输入目录 '{input_dir}' 无效."); return
    output_dir = os.path.join(output_base_dir, f"{os.path.basename(input_dir)}_converted_output")
    if not os.path.exists(output_dir): os.makedirs(output_dir)
    processed_count, success_count = 0, 0
    print(f"开始批量转换: '{input_dir}' -> '{output_dir}'")
    for filename in os.listdir(input_dir):
        if filename.lower().endswith('.json'):
            input_fp = os.path.join(input_dir, filename)
            base_name, ext = os.path.splitext(filename)
            output_fn = f"{base_name}_converted{ext}"
            output_fp = os.path.join(output_dir, output_fn)
            processed_count += 1
            if process_json_file(input_fp, output_fp): success_count += 1
    print(f"转换完成. 总计: {processed_count}, 成功: {success_count}, 失败: {processed_count - success_count}. 输出至: '{output_dir}'")

if __name__ == "__main__":
    input_folder = r"F:\Python\作业状态识别\Json\程总" # 【配置】输入文件夹路径
    # 【配置】输出文件存放的父目录。脚本会在此外创建名为 "输入文件夹名_converted_output" 的子目录
    output_base_folder = os.path.dirname(input_folder) # 示例：输出在输入文件夹的同级目录下
    # output_base_folder = r"D:\Converted_Json_Outputs" # 示例：指定一个自定义的输出父目录
    if os.path.exists(input_folder): batch_convert_json_files(input_folder, output_base_folder)
    else: print(f"错误: 输入文件夹 '{input_folder}' 未找到。请检查路径。")
