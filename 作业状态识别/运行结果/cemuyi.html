<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="viewport" content="width=device-width, initial-scale=1.0">
    <meta charset="UTF-8">
    <title>测亩易APP介绍</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f4f4f9;
            margin: 0;
            padding: 0;
        }
        header {
            background-color: #333;
            color: #fff;
            padding: 20px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .logo {
            width: 100px;
            height: auto;
            margin-right: 15px;
        }
        .container {
            width: 95%;
            max-width: 1400px;
            margin: 20px auto;
            display: grid;
            grid-template-columns: 61.5% 36.5%;
            grid-template-rows: auto auto;
            gap: 20px;
            padding: 0;
        }
        .card {
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border-radius: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin: 0;
            width: 100%;
            overflow: hidden;
            transition: all 0.3s ease;
            text-align: center;
            border: none;
        }
        .card:hover {
            transform: translateY(-10px);
            box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15);
        }
        .icon, .method-icon {
            font-size: 24px;
            width: 45px;
            height: 45px;
            margin: 5px 0;
            padding: 8px;
            background: linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0.3) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;
            min-width: 45px;
            min-height: 45px;
            flex-shrink: 0;
        }
        .card:nth-child(1) { background: linear-gradient(135deg, #E8F5E9 0%, #C8E6C9 100%); }
        .card:nth-child(2) { background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%); }
        .card:nth-child(3) { background: linear-gradient(135deg, #FFF3E0 0%, #FFE0B2 100%); }
        .card:nth-child(4) { background: linear-gradient(135deg, #F3E5F5 0%, #E1BEE7 100%); }
        .card:nth-child(5) { background: linear-gradient(135deg, #E0F7FA 0%, #B2EBF2 100%); }
        .card:nth-child(6) { background: linear-gradient(135deg, #FBE9E7 0%, #FFCCBC 100%); }
        .card:nth-child(7) { background: linear-gradient(135deg, #F1F8E9 0%, #DCEDC8 100%); }
        .card:nth-child(8) { background: linear-gradient(135deg, #E8EAF6 0%, #C5CAE9 100%); }
        .card:nth-child(9) { background: linear-gradient(135deg, #FCE4EC 0%, #F8BBD0 100%); }
        .card:nth-child(10) { background: linear-gradient(135deg, #EFEBE9 0%, #D7CCC8 100%); }
        .card:nth-child(11) { background: linear-gradient(135deg, #E0F2F1 0%, #B2DFDB 100%); }
        .card:not(.card-large) {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px;
            height: 143px;
            justify-content: center;
            width: 100%;
            box-sizing: border-box;
        }
        .card:not(.card-large) .icon {
            font-size: 24px;
            margin: 5px 0;
            padding: 10px;
            background: linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0.3) 100%);
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;
            aspect-ratio: 1;
            min-width: 45px;
            min-height: 45px;
        }
        .card:not(.card-large) .card-content {
            padding: 5px;
            background: transparent;
            width: 100%;
            margin-top: 10px;
        }
        .card:not(.card-large) .card-content h3 {
            margin: 5px 0;
            font-size: 1.1em;
            white-space: nowrap;
        }
        .card:not(.card-large) .card-content p {
            font-size: 0.8em;
            line-height: 1.3;
            margin: 5px 0;
            max-width: 90%;
            margin: 5px auto;
        }
        .card-large:first-child {
            grid-column: 1;
            grid-row: span 1;
            height: fit-content;
            padding: 15px;
            width: 100%;
            box-sizing: border-box;
            margin: 0;
        }
        .card-large:nth-child(2) {
            grid-column: 2;
            grid-row: span 1;
            height: fit-content;
            padding: 15px;
            width: 100%;
            box-sizing: border-box;
            margin: 0;
        }
        .measure-methods {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 20px;
            padding: 20px;
            height: auto;
        }
        .method-card, .manage-card {
            padding: 8px;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            height: 165px;
            display: flex;
            flex-direction: column;
            justify-content: space-evenly;
            align-items: center;
            margin: 0;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        .measure-methods .method-card:nth-child(1) { background: linear-gradient(135deg, #E3F2FD 0%, rgba(255, 255, 255, 0.9) 100%); }
        .measure-methods .method-card:nth-child(2) { background: linear-gradient(135deg, #F3E5F5 0%, rgba(255, 255, 255, 0.9) 100%); }
        .measure-methods .method-card:nth-child(3) { background: linear-gradient(135deg, #E8F5E9 0%, rgba(255, 255, 255, 0.9) 100%); }
        .measure-methods .method-card:nth-child(4) { background: linear-gradient(135deg, #FFF3E0 0%, rgba(255, 255, 255, 0.9) 100%); }
        .measure-methods .method-card:nth-child(5) { background: linear-gradient(135deg, #E0F7FA 0%, rgba(255, 255, 255, 0.9) 100%); }
        .measure-methods .method-card:nth-child(6) { background: linear-gradient(135deg, #F1F8E9 0%, rgba(255, 255, 255, 0.9) 100%); }
        .method-card:hover, .manage-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
            border: 1px solid rgba(255, 255, 255, 0.5);
        }
        .method-icon, .manage-card .method-icon {
            font-size: 24px;
            width: 40px;
            height: 40px;
            margin: 5px 0;
            padding: 8px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            backdrop-filter: blur(3px);
            border: 1px solid rgba(255, 255, 255, 0.5);
        }
        .manage-items {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 20px;
            padding: 20px;
            height: auto;
        }
        .manage-items .manage-card:nth-child(1) { background: linear-gradient(135deg, #E8EAF6 0%, rgba(255, 255, 255, 0.9) 100%); }
        .manage-items .manage-card:nth-child(2) { background: linear-gradient(135deg, #FCE4EC 0%, rgba(255, 255, 255, 0.9) 100%); }
        .manage-items .manage-card:nth-child(3) { background: linear-gradient(135deg, #F3E5F5 0%, rgba(255, 255, 255, 0.9) 100%); }
        .manage-items .manage-card:nth-child(4) { background: linear-gradient(135deg, #E0F2F1 0%, rgba(255, 255, 255, 0.9) 100%); }
        .small-cards-container {
            grid-column: 1 / 3;
            display: grid;
            grid-template-columns: repeat(4, 23.5%);
            grid-template-rows: repeat(2, 1fr);
            gap: 20px;
            margin-top: 20px;
            justify-content: space-between;
            width: 100%;
            margin: 20px 0 0 0;
        }
        .card-large .card-content {
            text-align: center;
            padding: 0 0 10px 0;
            margin-bottom: 0;
        }
        .card-large .card-content h3 {
            font-size: 1.2em;
            margin-bottom: 10px;
        }
        .card-large .card-content p {
            color: #666;
            font-size: 0.9em;
        }
        .icon, .method-icon, .manage-card .method-icon {
            font-size: 24px;
            width: 40px;
            height: 40px;
            margin: 5px 0;
            padding: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            box-sizing: border-box;
        }
        .method-card .card-content, .manage-card .card-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .method-card h4, .manage-card h4 {
            margin: 8px 0;
            font-size: 1.1em;
            color: #2c3e50;
        }
        .method-card p, .manage-card p {
            margin: 0;
            font-size: 0.9em;
            color: #666;
            line-height: 1.4;
        }
        .card-large:first-child {
            background: linear-gradient(135deg, #E8F5E9 0%, #C8E6C9 60%, #B3E5FC 100%);
        }
        .card-large:nth-child(2) {
            background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 60%, #E1BEE7 100%);
        }
        .small-cards-container .card:nth-child(1) {
            background: linear-gradient(135deg, #E8F5E9 0%, #C8E6C9 50%, #B2DFDB 100%);
        }
        .small-cards-container .card:nth-child(2) {
            background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 50%, #90CAF9 100%);
        }
        .small-cards-container .card:nth-child(3) {
            background: linear-gradient(135deg, #FFF3E0 0%, #FFE0B2 50%, #FFCC80 100%);
        }
        .small-cards-container .card:nth-child(4) {
            background: linear-gradient(135deg, #F3E5F5 0%, #E1BEE7 50%, #CE93D8 100%);
        }
        .small-cards-container .card:nth-child(5) {
            background: linear-gradient(135deg, #E1F5FE 0%, #B3E5FC 50%, #81D4FA 100%);
        }
        .small-cards-container .card:nth-child(6) {
            background: linear-gradient(135deg, #FCE4EC 0%, #F8BBD0 50%, #F48FB1 100%);
        }
        .small-cards-container .card:nth-child(7) {
            background: linear-gradient(135deg, #F1F8E9 0%, #DCEDC8 50%, #C5E1A5 100%);
        }
        .small-cards-container .card:nth-child(8) {
            background: linear-gradient(135deg, #E8EAF6 0%, #C5CAE9 50%, #9FA8DA 100%);
        }
        .small-cards-container .card {
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }
        .small-cards-container .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
            border: 1px solid rgba(255, 255, 255, 0.5);
        }
    </style>
</head>
<body>
    <header>
        <img src="测亩易图标.png" alt="测亩易图标" class="logo">
        <h1>测亩易APP介绍</h1>
    </header>
    <div class="container">
        <div class="card card-large">
            <div class="card-content">
                <h3>多种方式测亩量地</h3>
                <p>提供多种专业测量方式，满足不同场景需求</p>
            </div>
            <div class="measure-methods">
                <div class="method-card">
                    <div class="method-icon">✏️</div>
                    <h4>画地块</h4>
                    <p>直接在地图上打点圈地测量</p>
                </div>
                <div class="method-card">
                    <div class="method-icon">👣</div>
                    <h4>走一圈</h4>
                    <p>手持手机绕地块走一圈测量</p>
                </div>
                <div class="method-card">
                    <div class="method-icon">🤖</div>
                    <h4>智能画地</h4>
                    <p>智能识别地块轮廓</p>
                </div>
                <div class="method-card">
                    <div class="method-icon">📏</div>
                    <h4>垄测</h4>
                    <p>选定地边输入垄宽计算面积</p>
                </div>
                <div class="method-card">
                    <div class="method-icon">🎯</div>
                    <h4>精准画地</h4>
                    <p>挪动地图中心点精确打点</p>
                </div>
                <div class="method-card">
                    <div class="method-icon">🚗</div>
                    <h4>车载测量</h4>
                    <p>记录实时作业轨迹计算面积</p>
                </div>
            </div>
        </div>
        <div class="card card-large">
            <div class="card-content">
                <h3>作业队管理</h3>
                <p>一站式农业生产组织管理平台</p>
            </div>
            <div class="manage-items">
                <div class="manage-card">
                    <div class="method-icon">👥</div>
                    <h4>人员管理</h4>
                    <p>管理作业队成员</p>
                </div>
                <div class="manage-card">
                    <div class="method-icon">🚜</div>
                    <h4>机器管理</h4>
                    <p>农机实时监控</p>
                </div>
                <div class="manage-card">
                    <div class="method-icon">🌾</div>
                    <h4>地块管理</h4>
                    <p>农田分配管理</p>
                </div>
                <div class="manage-card">
                    <div class="method-icon">📋</div>
                    <h4>作业管理</h4>
                    <p>农事任务分配</p>
                </div>
            </div>
        </div>
        <div class="small-cards-container">
            <div class="card">
                <div class="icon">🌍</div>
                <div class="card-content">
                    <h3>地块管理</h3>
                    <p>自动保存测量地块，支持分组管理和查看天气预报。</p>
                </div>
            </div>
            <div class="card">
                <div class="icon">📢</div>
                <div class="card-content">
                    <h3>农事信息发布</h3>
                    <p>发布农活信息、招聘、土地转让等。</p>
                </div>
            </div>
            <div class="card">
                <div class="icon">📸</div>
                <div class="card-content">
                    <h3>水印相机</h3>
                    <p>拍照记录地块信息，增加真实性。</p>
                </div>
            </div>
            <div class="card">
                <div class="icon">📝</div>
                <div class="card-content">
                    <h3>农活记录</h3>
                    <p>记录作业信息，方便跨区机手管理。</p>
                </div>
            </div>
            <div class="card">
                <div class="icon">💰</div>
                <div class="card-content">
                    <h3>农事记账</h3>
                    <p>记录农事活动的收支。</p>
                </div>
            </div>
            <div class="card">
                <div class="icon">🛒</div>
                <div class="card-content">
                    <h3>农资商城</h3>
                    <p>购买农药和化肥。</p>
                </div>
            </div>
            <div class="card">
                <div class="icon">🚜</div>
                <div class="card-content">
                    <h3>农机世界</h3>
                    <p>了解农机信息和经销商。</p>
                </div>
            </div>
            <div class="card">
                <div class="icon">🔗</div>
                <div class="card-content">
                    <h3>农服信息分享</h3>
                    <p>分享地块信息给他人。</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 