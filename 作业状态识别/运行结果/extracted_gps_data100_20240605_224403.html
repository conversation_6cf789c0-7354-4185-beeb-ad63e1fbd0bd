<!DOCTYPE html>
<html>
<head>
    
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    
        <script>
            L_NO_TOUCH = false;
            L_DISABLE_3D = false;
        </script>
    
    <style>html, body {width: 100%;height: 100%;margin: 0;padding: 0;}</style>
    <style>#map {position:absolute;top:0;bottom:0;right:0;left:0;}</style>
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.0/css/all.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/python-visualization/folium/folium/templates/leaflet.awesome.rotate.min.css"/>
    
            <meta name="viewport" content="width=device-width,
                initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
            <style>
                #map_eaf4447406028a81b8a932170a76a66a {
                    position: relative;
                    width: 100.0%;
                    height: 100.0%;
                    left: 0.0%;
                    top: 0.0%;
                }
                .leaflet-container { font-size: 1rem; }
            </style>
        
    <script src="https://cdn.jsdelivr.net/gh/ljagis/leaflet-measure@2.1.7/dist/leaflet-measure.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/ljagis/leaflet-measure@2.1.7/dist/leaflet-measure.min.css"/>
</head>
<body>
    
    
    <div id="info-box" style="position: fixed; top: 50px; left: 10px; z-index: 1000; background-color: white; padding: 10px; border: 2px solid black; max-height: 1000px; overflow-y: auto;">
        <div style="font-size: 14px;"><h4>地块编号和面积信息</h4><p><strong>地块 1</strong></p><p>去重面积: 3316 m²<br>包围面积: 3314 m²<br>扩展包围面积: 4052 m²</p></div>
    </div>
    <button id="toggle-button" style="position: fixed; top: 10px; left: 10px; z-index: 1001; background-color: white; border: 2px solid black;">收起/展开</button>
    <script>
        document.getElementById('toggle-button').onclick = function() {
            var infoBox = document.getElementById('info-box');
            if (infoBox.style.display === 'none') {
                infoBox.style.display = 'block';
            } else {
                infoBox.style.display = 'none';
            }
        };
    </script>
    
    
            <div class="folium-map" id="map_eaf4447406028a81b8a932170a76a66a" ></div>
        
</body>
<script>
    
    
            var map_eaf4447406028a81b8a932170a76a66a = L.map(
                "map_eaf4447406028a81b8a932170a76a66a",
                {
                    center: [36.609643232679886, 120.04887026174988],
                    crs: L.CRS.EPSG3857,
                    zoom: 12,
                    zoomControl: true,
                    preferCanvas: false,
                }
            );

            

        
    
            var tile_layer_a55e2ca8e4a0f583985314a790668e4c = L.tileLayer(
                "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
                {"attribution": "\u0026copy; \u003ca href=\"https://www.openstreetmap.org/copyright\"\u003eOpenStreetMap\u003c/a\u003e contributors", "detectRetina": false, "maxNativeZoom": 19, "maxZoom": 19, "minZoom": 0, "noWrap": false, "opacity": 1, "subdomains": "abc", "tms": false}
            );
        
    
            tile_layer_a55e2ca8e4a0f583985314a790668e4c.addTo(map_eaf4447406028a81b8a932170a76a66a);
        
    
            var tile_layer_f3f43cc60b132cf2b9bf51c3e2a186f2 = L.tileLayer(
                "http://t0.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS\u0026REQUEST=GetTile\u0026VERSION=1.0.0\u0026LAYER=img\u0026STYLE=default\u0026TILEMATRIXSET=w\u0026FORMAT=tiles\u0026TILEMATRIX={z}\u0026TILEROW={y}\u0026TILECOL={x}\u0026tk=e7f645439e09d5fdbc5158bacab6d024",
                {"attribution": "\u5929\u5730\u56fe", "detectRetina": false, "maxNativeZoom": 18, "maxZoom": 18, "minZoom": 0, "noWrap": false, "opacity": 1, "subdomains": "abc", "tms": false}
            );
        
    
            tile_layer_f3f43cc60b132cf2b9bf51c3e2a186f2.addTo(map_eaf4447406028a81b8a932170a76a66a);
        
    
            var measure_control_da2c3d51b96d311b9b3bdb6712a55400 = new L.Control.Measure(
                {"activeColor": "red", "completedColor": "red", "popupOptions": {"className": "measure_popup"}, "position": "topright", "primaryAreaUnit": "sqmeters", "primaryLengthUnit": "meters", "secondaryAreaUnit": "acres", "secondaryLengthUnit": "miles"});
            map_eaf4447406028a81b8a932170a76a66a.addControl(measure_control_da2c3d51b96d311b9b3bdb6712a55400);

            // Workaround for using this plugin with Leaflet>=1.8.0
            // https://github.com/ljagis/leaflet-measure/issues/171
            L.Control.Measure.include({
                _setCaptureMarkerIcon: function () {
                    // disable autopan
                    this._captureMarker.options.autoPanOnFocus = false;
                    // default function
                    this._captureMarker.setIcon(
                        L.divIcon({
                            iconSize: this._map.getSize().multiplyBy(2)
                        })
                    );
                },
            });

        
    
            var layer_control_c6c9f4950e922cc1a13768b5345784be_layers = {
                base_layers : {
                    "openstreetmap" : tile_layer_a55e2ca8e4a0f583985314a790668e4c,
                },
                overlays :  {
                    "\u5929\u5730\u56fe\u536b\u661f\u5f71\u50cf" : tile_layer_f3f43cc60b132cf2b9bf51c3e2a186f2,
                },
            };
            let layer_control_c6c9f4950e922cc1a13768b5345784be = L.control.layers(
                layer_control_c6c9f4950e922cc1a13768b5345784be_layers.base_layers,
                layer_control_c6c9f4950e922cc1a13768b5345784be_layers.overlays,
                {"autoZIndex": true, "collapsed": true, "position": "topright"}
            ).addTo(map_eaf4447406028a81b8a932170a76a66a);

        
</script>
</html>