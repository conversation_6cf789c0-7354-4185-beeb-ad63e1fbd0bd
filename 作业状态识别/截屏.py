import os
import time
from multiprocessing import Pool
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait

# 输入输出路径
html_dir = r"F:\Python\作业状态识别\result\html_maps"
output_dir = r"F:\Python\作业状态识别\result\screenshots"
os.makedirs(output_dir, exist_ok=True)

# 获取所有HTML文件
html_files = [os.path.join(html_dir, f) for f in os.listdir(html_dir) if f.endswith(".html")]

def take_screenshot(html_path):
    try:
        basename = os.path.basename(html_path).replace(".html", ".png")
        out_path = os.path.join(output_dir, basename)

        if os.path.exists(out_path):
            print(f"⏭️ 已存在，跳过: {basename}")
            return

        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1200")

        driver = webdriver.Chrome(options=chrome_options)
        url = "file:///" + html_path.replace("\\", "/")
        driver.get(url)

        # 智能等待页面加载，最多等 10 秒
        WebDriverWait(driver, 10).until(
            lambda d: d.execute_script("return document.readyState") == "complete"
        )

        time.sleep(1)  # 保守冗余等一下地图加载（可调成0.5）

        driver.save_screenshot(out_path)
        driver.quit()

        print(f"✅ 成功: {basename}")
    except Exception as e:
        print(f"❌ 失败: {html_path}")
        print(e)

if __name__ == '__main__':
    # 推荐保守并发数：3（你机器可选 4~6，建议从3试）
    with Pool(processes=3) as pool:
        pool.map(take_screenshot, html_files)
