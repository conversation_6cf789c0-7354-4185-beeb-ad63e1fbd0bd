import pandas as pd
import json
from datetime import datetime


def haversine(lat1, lon1, lat2, lon2):
    # 计算两个经纬度之间的距离
    from math import radians, sin, cos, sqrt, atan2
    R = 6371000  # 地球半径，单位为米
    dlat = radians(lat2 - lat1)
    dlon = radians(lon2 - lon1)
    a = sin(dlat / 2) ** 2 + cos(radians(lat1)) * cos(radians(lat2)) * sin(dlon / 2) ** 2
    c = 2 * atan2(sqrt(a), sqrt(1 - a))
    return R * c


# 读取JSON文件
json_file_path = r'F:\Python\作业状态识别\Json\867065078382749_20250624.json'  # 你的文件路径
with open(json_file_path, 'r') as file:
    data = json.load(file)

# 确保数据点数量
total_points = len(data)

# 解析时间和坐标数据
for point in data:
    point['time'] = pd.to_datetime(point['time'])
    point['latitude'] = float(point['latitude'])    # 转换为数字
    point['longitude'] = float(point['longitude'])  # 转换为数字

# 初始化变量
total_distance = 0
total_time = 0
distances = []
times = []
speeds = []

max_speed = 0
max_speed_point = None
max_speed_index = 0
max_time_diff = 0
max_time_diff_index = 0

# 计算距离、时间间隔和速度
for i in range(1, total_points):
    lat1, lon1 = data[i - 1]['latitude'], data[i - 1]['longitude']
    lat2, lon2 = data[i]['latitude'], data[i]['longitude']
    time1 = data[i - 1]['time']
    time2 = data[i]['time']

    distance = haversine(lat1, lon1, lat2, lon2)
    time_diff = (time2 - time1).total_seconds()

    if time_diff > 0:
        speed = (distance / time_diff) * 3.6  # 转换为公里每小时
        speeds.append(speed)
        if speed > max_speed:
            max_speed = speed
            max_speed_point = data[i]
            max_speed_index = i

    if time_diff > max_time_diff:
        max_time_diff = time_diff
        max_time_diff_index = i - 1

    total_distance += distance
    total_time += time_diff
    distances.append(distance)
    times.append(time_diff)

average_distance = total_distance / len(distances)
average_speed_overall = (total_distance / total_time) * 3.6  # 转换为公里每小时
average_speed_individual = sum(speeds) / len(speeds)

# 分段统计速度
speed_segments = [2, 5, 8, 12, 15, 20, 30]
speed_segment_counts = [0] * (len(speed_segments) + 1)

for speed in speeds:
    for i, segment in enumerate(speed_segments):
        if speed < segment:
            speed_segment_counts[i] += 1
            break
    else:
        speed_segment_counts[-1] += 1

# 计算每个速度段占总点数的百分比
speed_segment_percentages = [(count / total_points) * 100 for count in speed_segment_counts]

# 将百分比添加到结果中
speed_segment_info = []
for i, count in enumerate(speed_segment_counts):
    segment_info = {
        "range": f"{speed_segments[i - 1] if i > 0 else 0} ~ {speed_segments[i] if i < len(speed_segments) else '以上'} 公里/小时",
        "count": count,
        "percentage": speed_segment_percentages[i]
    }
    speed_segment_info.append(segment_info)

# 格式化总时间
total_time_hours = total_time // 3600
total_time_minutes = (total_time % 3600) // 60
total_time_seconds = total_time % 60
total_time_formatted = f"{total_time_hours}小时 {total_time_minutes}分钟 {total_time_seconds}秒"

# 找到最长时间间隔的起始点和结束点
max_time_start_point = data[max_time_diff_index]
max_time_end_point = data[max_time_diff_index + 1]

# 计算距离间隔最长的两个点及其距离
max_distance = max(distances)
max_distance_index = distances.index(max_distance)

# 找到最长距离间隔的起始点和结束点
max_distance_start_point = data[max_distance_index]
max_distance_end_point = data[max_distance_index + 1]

# 计算距离间隔最长的两个点之间的时间间隔
max_distance_time_start = max_distance_start_point['time']
max_distance_time_end = max_distance_end_point['time']
max_distance_time_diff = (max_distance_time_end - max_distance_time_start).total_seconds()

# 计算时间间隔最长的两个点之间的距离
max_time_start_lat = max_time_start_point['latitude']
max_time_start_lon = max_time_start_point['longitude']
max_time_end_lat = max_time_end_point['latitude']
max_time_end_lon = max_time_end_point['longitude']
max_time_distance = haversine(max_time_start_lat, max_time_start_lon, max_time_end_lat, max_time_end_lon)

# 统计各时间间隔段的点数
time_intervals = [2, 5, 10, 20, 30, 40, 50, 60, 100]
time_interval_counts = [0] * (len(time_intervals))

for time_diff in times:
    for i, interval in enumerate(time_intervals):
        if time_diff < interval:
            time_interval_counts[i] += 1
            break
    else:
        time_interval_counts[-1] += 1

# 将结果与时间段对应
time_interval_info = []
for i, count in enumerate(time_interval_counts):
    interval_info = {
        "range": f"{time_intervals[i - 1] if i > 0 else 0} ~ {time_intervals[i] if i < len(time_intervals) else '以上'} 秒",
        "count": count
    }
    time_interval_info.append(interval_info)

# 输出结果
result = {
    "total_time": total_time_formatted,
    "total_distance_km": total_distance / 1000,
    "average_distance_m": average_distance,
    "average_time_interval_seconds": total_time / (total_points - 1),
    "average_speed_overall_kmph": average_speed_overall,
    "average_speed_individual_kmph": average_speed_individual,
    "speed_segment_info": speed_segment_info,
    "max_speed_kmph": max_speed,
    "max_speed_point": max_speed_point,
    "max_speed_index": max_speed_index,
    "total_points": total_points,
    "max_time_diff_seconds": max_time_diff,
    "max_time_start_point": max_time_start_point,
    "max_time_end_point": max_time_end_point,
    "max_time_distance_m": max_time_distance,
    "max_distance_m": max_distance,
    "max_distance_time_diff_seconds": max_distance_time_diff,
    "max_distance_start_point": max_distance_start_point,
    "max_distance_end_point": max_distance_end_point,
    "time_interval_info": time_interval_info
}

# 打印结果
print(f"总时间: {result['total_time']}")
print(f"总距离: {result['total_distance_km']} 公里")
print(f"平均距离: {result['average_distance_m']} 米")
print(f"平均点间隔时间: {result['average_time_interval_seconds']} 秒")
print(f"总体平均速度: {result['average_speed_overall_kmph']} 公里/小时")
print(f"逐点平均速度: {result['average_speed_individual_kmph']} 公里/小时")
print("速度分段统计及百分比:")
for segment in result['speed_segment_info']:
    print(f" - {segment['range']}: {segment['count']} 点，占 {segment['percentage']:.2f}%")
print(f"速度最高点: {result['max_speed_kmph']} 公里/小时，位置在 (纬度: {result['max_speed_point']['latitude']}, 经度: {result['max_speed_point']['longitude']}), 时间: {result['max_speed_point']['time']}")
print(f"速度最高点所在位置: 第 {result['max_speed_index']} 个点")
print(f"总坐标点数量: {result['total_points']} 点")
print(f"时间间隔最长: {result['max_time_diff_seconds']} 秒")
print(f" - 起始点: 第 {max_time_diff_index} 个点, (纬度: {result['max_time_start_point']['latitude']}, 经度: {result['max_time_start_point']['longitude']}), 时间: {result['max_time_start_point']['time']}")
print(f" - 结束点: 第 {max_time_diff_index + 1} 个点, (纬度: {result['max_time_end_point']['latitude']}, 经度: {result['max_time_end_point']['longitude']}), 时间: {result['max_time_end_point']['time']}")
print(f" - 距离: {result['max_time_distance_m']} 米")
print(f"距离间隔最长: {result['max_distance_m']} 米")
print(f" - 起始点: 第 {max_distance_index} 个点, (纬度: {result['max_distance_start_point']['latitude']}, 经度: {result['max_distance_start_point']['longitude']}), 时间: {result['max_distance_start_point']['time']}")
print(f" - 结束点: 第 {max_distance_index + 1} 个点, (纬度: {result['max_distance_end_point']['latitude']}, 经度: {result['max_distance_end_point']['longitude']}), 时间: {result['max_distance_end_point']['time']}")
print(f" - 时间间隔: {result['max_distance_time_diff_seconds']} 秒")
print("点间隔时间统计:")
for interval in result['time_interval_info']:
    print(f" - {interval['range']}: {interval['count']} 点")


