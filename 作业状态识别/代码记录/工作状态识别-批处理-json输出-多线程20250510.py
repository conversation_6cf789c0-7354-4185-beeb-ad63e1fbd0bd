      
import json, pandas as pd, numpy as np, folium, pyproj, time, datetime, gc, os
from folium import plugins
from math import radians, cos, sin, asin, sqrt
from sklearn.cluster import DBSCAN
from scipy.spatial import <PERSON>aunay, QhullError
from shapely.ops import unary_union, polygonize
from shapely.geometry import Polygon, LineString, Point
import multiprocessing
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
from functools import partial
import traceback

EARTH_RADIUS_KM = 6371 

def load_data_implementation(filename): 
    try:
        with open(filename, 'r', encoding='utf-8') as f: data = pd.read_json(f) # 读取JSON
        print(f"✅ 成功从 {filename} 加载 {len(data)} 条数据。")
        if not all(col in data.columns for col in ['latitude', 'longitude', 'time']): print("❌ 错误：JSON文件缺少必需列"); return None
        data['latitude'] = pd.to_numeric(data['latitude'], errors='coerce') # 转为数字类型
        data['longitude'] = pd.to_numeric(data['longitude'], errors='coerce')
        data['time'] = pd.to_datetime(data['time'], errors='coerce') # 转为时间戳
        initial_count = len(data)
        data.dropna(subset=['latitude', 'longitude', 'time'], inplace=True) # 去除空值
        if len(data) < initial_count: print(f"ℹ️ 清理无效数据：移除 {initial_count - len(data)} 条")
        if data.empty: print("❌ 错误：有效数据为空。"); return None
        return data
    except FileNotFoundError: print(f"❌ 错误: 文件 '{filename}' 未找到。"); return None
    except ValueError as e: print(f"❌ 错误: 解析或类型转换失败 - {e}"); return None
    except Exception as e: print(f"❌ 未知错误: {e}"); return None

def load_data(filename): return load_data_implementation(filename) # 封装一层，便于调用

def vectorized_haversine_implementation(lat1, lon1, lat2, lon2): # 批量计算两组经纬度点之间的距离（单位：公里）
    lat1, lon1, lat2, lon2 = map(np.radians, [lat1, lon1, lat2, lon2])
    dlat = lat2 - lat1; dlon = lon2 - lon1
    a = np.sin(dlat / 2.0) ** 2 + np.cos(lat1) * np.cos(lat2) * np.sin(dlon / 2.0) ** 2
    a = np.clip(a, 0, 1); c = 2 * np.arcsin(np.sqrt(a)); km = EARTH_RADIUS_KM * c
    return km

def vectorized_haversine(lat1, lon1, lat2, lon2): return vectorized_haversine_implementation(lat1, lon1, lat2, lon2) # 封装调用

def calculate_speed_distance_implementation(data): # 根据时间和位置计算每段速度与距离
    df = data.sort_values(by='time').copy()
    if len(df) < 2: df['distance_km'] = 0.0; df['speed_kmh'] = 0.0; return df # 小于两点直接返回0
    distances_km = vectorized_haversine(df['latitude'].values[:-1], df['longitude'].values[:-1], df['latitude'].values[1:], df['longitude'].values[1:]) # 计算相邻点距离
    distance_col = np.concatenate(([0.0], distances_km)) # 第一行距离为0
    df['distance_km'] = distance_col
    time_deltas_hours = df['time'].diff().dt.total_seconds().div(3600) # 相邻点时间差（小时）
    time_deltas_hours = time_deltas_hours.replace(0, np.nan); time_deltas_hours[time_deltas_hours < 1e-9] = np.nan # 避免除零
    df['speed_kmh'] = distance_col / time_deltas_hours; df['speed_kmh'] = df['speed_kmh'].fillna(0) # 计算速度
    df = df.replace([np.inf, -np.inf], np.nan); df['speed_kmh'] = df['speed_kmh'].fillna(0) # 清理无穷大
    print("✅ 速度和距离计算完成。")
    df['original_index'] = df.index # 记录原索引
    return df

def calculate_speed_distance(data): return calculate_speed_distance_implementation(data) # 封装调用

def haversine(lon1, lat1, lon2, lat2): # 单对单计算两点间距离（单位：米）
    lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])
    dlon = lon2 - lon1; dlat = lat2 - lat1
    a = sin(dlat / 2) ** 2 + cos(lat1) * cos(lat2) * sin(dlon / 2) ** 2
    c = 2 * asin(sqrt(a)); meters = EARTH_RADIUS_KM * 1000 * c
    return meters

def generate_uniformly_spaced_points_on_path(input_df, sampling_interval_m): # 在原始轨迹上每隔一定距离插值生成新点
    if input_df.empty: print("ℹ️ 输入数据为空，无法生成轨迹点。"); return pd.DataFrame(columns=['latitude', 'longitude']), 0.0
    if len(input_df) < 2: # 少于2个点，直接返回
        print("ℹ️ 输入点少于2个，返回原始单点。")
        if 'latitude' in input_df.columns and 'longitude' in input_df.columns: return input_df[['latitude', 'longitude']].copy(), 0.0
        return pd.DataFrame(columns=['latitude', 'longitude']), 0.0
    print(f"🛠️ 开始轨迹插值，每隔 {sampling_interval_m} 米采样...")
    path_points_list = [{'latitude': input_df.iloc[0]['latitude'], 'longitude': input_df.iloc[0]['longitude']}]
    total_trajectory_length_m = 0.0; accumulated_distance_along_path = 0.0; next_sample_distance_target = sampling_interval_m
    for i in range(len(input_df) - 1):
        p1_lat, p1_lon = input_df.iloc[i]['latitude'], input_df.iloc[i]['longitude']
        p2_lat, p2_lon = input_df.iloc[i + 1]['latitude'], input_df.iloc[i + 1]['longitude']
        segment_distance_m = haversine(p1_lon, p1_lat, p2_lon, p2_lat)
        total_trajectory_length_m += segment_distance_m
        if segment_distance_m == 0: continue
        while next_sample_distance_target <= accumulated_distance_along_path + segment_distance_m:
            distance_into_segment = next_sample_distance_target - accumulated_distance_along_path
            fraction = max(0.0, min(1.0, distance_into_segment / segment_distance_m))
            inter_lat = p1_lat + fraction * (p2_lat - p1_lat); inter_lon = p1_lon + fraction * (p2_lon - p1_lon)
            if not path_points_list or haversine(inter_lon, inter_lat, path_points_list[-1]['longitude'], path_points_list[-1]['latitude']) > 1e-3:
                path_points_list.append({'latitude': inter_lat, 'longitude': inter_lon})
            next_sample_distance_target += sampling_interval_m
        accumulated_distance_along_path += segment_distance_m
    last_orig_point = input_df.iloc[-1]
    if not path_points_list: path_points_list.append({'latitude': last_orig_point['latitude'], 'longitude': last_orig_point['longitude']})
    else:
        last_gen_point = path_points_list[-1]
        dist_to_last_orig = haversine(last_gen_point['longitude'], last_gen_point['latitude'], last_orig_point['longitude'], last_orig_point['latitude'])
        if dist_to_last_orig > 1e-3 and accumulated_distance_along_path >= next_sample_distance_target - sampling_interval_m:
            path_points_list.append({'latitude': last_orig_point['latitude'], 'longitude': last_orig_point['longitude']})
    output_df = pd.DataFrame(path_points_list); output_df.reset_index(drop=True, inplace=True)
    print(f"✅ 轨迹插值完成，总生成 {len(output_df)} 个点")
    return output_df, total_trajectory_length_m

def filter_by_speed_implementation(data, speed_min, speed_max): # 根据速度范围筛选有效轨迹点
    initial_count = len(data)
    if 'speed_kmh' not in data.columns: print("❌ 错误: 'speed_kmh' 列不存在，无法筛选。"); return pd.DataFrame(columns=data.columns)
    data['speed_kmh'] = pd.to_numeric(data['speed_kmh'], errors='coerce') # 转为数值
    data.dropna(subset=['speed_kmh'], inplace=True) # 删除无效速度
    filtered_data = data[(data['speed_kmh'] >= speed_min) & (data['speed_kmh'] <= speed_max)].copy() # 只保留合理速度区间
    print(f"ℹ️ 速度筛选：保留 {len(filtered_data)} 个点 (速度在 {speed_min}-{speed_max} km/h)，移除 {initial_count - len(filtered_data)} 个点。")
    return filtered_data

def filter_by_speed(data, speed_min, speed_max): return filter_by_speed_implementation(data, speed_min, speed_max) # 封装调用

def run_dbscan_implementation(data, eps_meters, min_samples): # 使用DBSCAN聚类分析轨迹点
    if data.empty or len(data) < min_samples: print(f"ℹ️ 输入点过少 (<{min_samples})，跳过聚类。"); data['cluster'] = -1; return data
    print(f"🚀 开始DBSCAN聚类 (eps={eps_meters}m, min_samples={min_samples})...")
    start_dbscan = time.time()
    eps_radians = eps_meters / (EARTH_RADIUS_KM * 1000) # eps换算为弧度单位
    coords_rad = np.radians(data[['latitude', 'longitude']].values) # 坐标转弧度
    if np.isnan(coords_rad).any(): print("❌ 坐标数据含NaN，无法DBSCAN。"); data['cluster'] = -1; return data
    # 使用更优化的参数设置
    db = DBSCAN(eps=eps_radians, min_samples=min_samples, metric='haversine', algorithm='ball_tree', 
                n_jobs=-1, leaf_size=50) # 增加leaf_size以提高性能
    try:
        db.fit(coords_rad); cluster_labels = db.labels_
        data_copy = data.copy(); data_copy['cluster'] = cluster_labels # 将聚类结果赋值
    except ValueError as e:
        print(f"❌ DBSCAN出错: {e}，所有点标记为噪声。"); data_copy = data.copy(); data_copy['cluster'] = -1
        cluster_labels = data_copy['cluster'].values
    end_dbscan = time.time()
    num_clusters = len(set(cluster_labels)) - (1 if -1 in cluster_labels else 0) # 排除噪声
    num_noise = list(cluster_labels).count(-1)
    print(f"🌌 DBSCAN完成 (耗时: {end_dbscan - start_dbscan:.2f}秒)：发现 {num_clusters} 个簇，{num_noise} 个噪声点。")
    return data_copy

def run_dbscan(data, eps_meters, min_samples): return run_dbscan_implementation(data, eps_meters, min_samples) # 封装调用

def perform_delaunay_triangulation_implementation(points): # 对点集合进行Delaunay三角剖分
    if points is None or not hasattr(points, '__len__') or len(points) < 3: return None
    try:
        points_array = np.array(points, dtype=float)
        if np.isnan(points_array).any(): return None
        noise_magnitude_degrees = 5e-6 # 加微小扰动防止共线退化
        points_perturbed = np.copy(points_array)
        points_perturbed[:, 0] += np.random.uniform(-noise_magnitude_degrees, noise_magnitude_degrees, size=points_array.shape[0])
        points_perturbed[:, 1] += np.random.uniform(-noise_magnitude_degrees, noise_magnitude_degrees, size=points_array.shape[0])
        tri = Delaunay(points_perturbed)
        return tri
    except QhullError as e: print(f"❌ Delaunay三角剖分失败 (QhullError)，点数: {len(points)}"); return None
    except Exception as e: print(f"❌ Delaunay剖分未知错误: {e}"); return None

def perform_delaunay_triangulation(points): return perform_delaunay_triangulation_implementation(points) # 封装调用

def get_utm_projection_implementation(centroid_lat, centroid_lon): # 根据质心经纬度推算UTM投影区域
    if pd.isna(centroid_lat) or pd.isna(centroid_lon): print("⚠️ 无法获取质心，无法确定UTM区域。"); return None, None, None
    try:
        utm_zone_number = int((centroid_lon + 180) / 6) + 1 # 经度计算所属UTM Zone
        hemisphere = 'north' if centroid_lat >= 0 else 'south' # 纬度判断南北半球
        utm_crs_str = f"+proj=utm +zone={utm_zone_number} +{hemisphere} +ellps=WGS84 +datum=WGS84 +units=m +no_defs"
        proj_utm = pyproj.CRS(utm_crs_str); proj_wgs84 = pyproj.CRS('EPSG:4326')
        transformer_to_utm = pyproj.Transformer.from_crs(proj_wgs84, proj_utm, always_xy=True) # 经纬度转UTM
        transformer_to_lonlat = pyproj.Transformer.from_crs(proj_utm, proj_wgs84, always_xy=True) # UTM转经纬度
        print(f"💡 使用UTM Zone: {utm_zone_number} ({hemisphere}) 进行精确计算。")
        return proj_utm, transformer_to_utm, transformer_to_lonlat
    except Exception as e: print(f"❌ 无法创建UTM投影: {e}"); return None, None, None

def get_utm_projection(centroid_lat, centroid_lon): return get_utm_projection_implementation(centroid_lat, centroid_lon) # 封装

def get_cluster_polygon_implementation(points_lonlat, max_edge_len_m): # 获取簇的多边形边界
    if points_lonlat is None or not hasattr(points_lonlat, '__len__') or len(points_lonlat) < 3: return None
    points_array = np.array(points_lonlat, dtype=float)
    if len(points_array) > 30: # 移除开头15个点和末尾15个点，只保留中间部分 (必须总数 > 30)
        points_array = points_array[15:-15]
    else: print(f"⚠️ 点数不足30个（实际点数: {len(points_array)}），跳过头尾点扣除。")
    if len(points_array) < 3: print(f"⚠️ 扣除头尾点后剩余点数不足3个，跳过本簇。"); return None
    tri = perform_delaunay_triangulation(points_array)
    if tri is None: return None
    valid_triangles_indices = [simplex for simplex in tri.simplices if valid_triangle(simplex, tri.points, max_edge_len_m)]
    if not valid_triangles_indices: return None
    outer_edges_indices = extract_outer_edges(valid_triangles_indices, tri.points)
    if not outer_edges_indices: return None
    edge_lines = []
    for edge_idx_pair in outer_edges_indices:
        p1_coords = tri.points[edge_idx_pair[0]]; p2_coords = tri.points[edge_idx_pair[1]]
        if not (np.isnan(p1_coords).any() or np.isnan(p2_coords).any()): edge_lines.append(LineString([p1_coords, p2_coords]))
    if not edge_lines: return None
    try:
        merged_lines = unary_union(edge_lines); polygons = list(polygonize(merged_lines))
        if polygons: return max(polygons, key=lambda p: p.area)
        else: return None
    except Exception as e: print(f"❌ 合并边线或生成多边形时出错: {e}"); return None

def valid_triangle(triangle_indices, points_array, max_length): # 校验三角形边长是否符合要求
    for i in range(3):
        p1_idx, p2_idx = triangle_indices[i], triangle_indices[(i + 1) % 3]
        if p1_idx >= len(points_array) or p2_idx >= len(points_array): return False
        point1, point2 = points_array[p1_idx], points_array[p2_idx]
        if pd.isna(point1[0]) or pd.isna(point1[1]) or pd.isna(point2[0]) or pd.isna(point2[1]): return False
        if haversine(point1[0], point1[1], point2[0], point2[1]) > max_length: return False
    return True

def extract_outer_edges(triangles_simplices, points_array): # 提取三角剖分后外部边
    if not triangles_simplices: return []
    edge_count = {}
    for tri_indices in triangles_simplices:
        for i in range(3):
            p1_idx, p2_idx = tri_indices[i], tri_indices[(i + 1) % 3]
            if p1_idx >= len(points_array) or p2_idx >= len(points_array): continue
            edge = tuple(sorted((p1_idx, p2_idx)))
            edge_count[edge] = edge_count.get(edge, 0) + 1
    return [edge_indices for edge_indices, count in edge_count.items() if count == 1]

def calculate_centroid_implementation(points_lonlat): # 计算点集合的中心点
    if points_lonlat is None or not hasattr(points_lonlat, '__len__') or len(points_lonlat) == 0: return [0,0]
    points_array = np.array(points_lonlat, dtype=float)
    if np.isnan(points_array).any():
        points_array = points_array[~np.isnan(points_array).any(axis=1)]
        if len(points_array) == 0: print("❌ 过滤NaN后无有效点计算质心。"); return [0,0]
    try:
        lon_mean = np.mean(points_array[:, 0]); lat_mean = np.mean(points_array[:, 1]); return [lat_mean, lon_mean]
    except IndexError: print(f"❌ 计算质心时IndexError: {points_array.shape}"); return [0,0]
    except Exception as e: print(f"❌ 计算质心出错: {e}"); return [0,0]

def calculate_centroid(points_lonlat): return calculate_centroid_implementation(points_lonlat) # 封装

def calculate_trajectory_coverage_area(traj_points, grid_size_meters, transformer_to_utm): # traj_points是(lat, lon)列表
    if not traj_points or len(traj_points) < 2 or transformer_to_utm is None: print("❌ 轨迹点不足或无有效UTM转换器，无法计算覆盖面积。"); return 0, 0
    traj_utm = [transformer_to_utm.transform(lon, lat) for lat, lon in traj_points] # 把轨迹点转成 UTM 坐标（米单位）
    grid_set = set() # 建立小格子的索引集合
    for i in range(len(traj_utm) - 1):
        x1, y1 = traj_utm[i]; x2, y2 = traj_utm[i + 1]
        segment_length = sqrt((x2 - x1)**2 + (y2 - y1)**2) # 线段上取样（步长为格子的一半）
        if segment_length == 0: continue
        num_samples = max(int(segment_length / (grid_size_meters / 2)), 1)
        for j in range(num_samples + 1):
            t = j / num_samples
            x = x1 + (x2 - x1) * t; y = y1 + (y2 - y1) * t
            grid_x = int(x // grid_size_meters); grid_y = int(y // grid_size_meters)
            grid_set.add((grid_x, grid_y))
    grid_mark_count = len(grid_set)
    coverage_area_m2 = grid_mark_count * (grid_size_meters ** 2)
    return coverage_area_m2, grid_set

def add_grid_cells_to_map(map_obj, grid_set, grid_size_meters, transformer_to_lonlat): # 在地图上添加栅格图层
    grid_layer = folium.FeatureGroup(name='轨迹覆盖栅格', show=False)
    for grid_x, grid_y in grid_set:
        x_min = grid_x * grid_size_meters; y_min = grid_y * grid_size_meters # 计算格子左下角的米坐标
        x_max = x_min + grid_size_meters; y_max = y_min + grid_size_meters
        lon_lat_min = transformer_to_lonlat.transform(x_min, y_min) # 把米坐标转回经纬度
        lon_lat_max = transformer_to_lonlat.transform(x_max, y_max)
        lat_min, lon_min = lon_lat_min[1], lon_lat_min[0]; lat_max, lon_max = lon_lat_max[1], lon_lat_max[0]
        folium.Polygon( # 画一个矩形Polygon
            locations=[(lat_min, lon_min), (lat_min, lon_max), (lat_max, lon_max), (lat_max, lon_min)],
            color='green', weight=0.5, fill=True, fill_color='green', fill_opacity=0.2
        ).add_to(grid_layer)
    grid_layer.add_to(map_obj)
    print(f"✅ 成功添加 {len(grid_set)} 个轨迹覆盖格子图层。")
    return map_obj

def create_base_map_implementation(center_lat, center_lon, zoom=15): # 创建基础地图对象
    map_vis = folium.Map(location=[center_lat, center_lon], zoom_start=zoom, tiles=None)
    folium.TileLayer( # 添加Google卫星
        tiles='http://{s}.google.com/vt/lyrs=s&x={x}&y={y}&z={z}', attr='Google Satellite', name='Google Satellite',
        overlay=True, control=True, subdomains=['mt0', 'mt1', 'mt2', 'mt3'], max_zoom=20).add_to(map_vis)
    your_tianditu_key = "e7f645439e09d5fdbc5158bacab6d024" # 添加天地图卫星图
    tian_di_tu_satellite_url = f'http://t0.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={{z}}&TILEROW={{y}}&TILECOL={{x}}&tk={your_tianditu_key}'
    folium.TileLayer(tiles=tian_di_tu_satellite_url, attr='天地图卫星影像', name='天地图卫星影像', overlay=False, control=True, max_zoom=20).add_to(map_vis)
    folium.TileLayer('openstreetmap', name='OpenStreetMap').add_to(map_vis) # 添加OpenStreetMap
    plugins.MeasureControl(position='topright', primary_length_unit='meters', primary_area_unit='sqmeters', active_color='red', completed_color='red').add_to(map_vis) # 添加测量工具
    print("✅ 基础地图创建完成。")
    return map_vis

def create_base_map(center_lat, center_lon, zoom=15): return create_base_map_implementation(center_lat, center_lon) # 封装

def add_points_to_map(map_obj, data_df, valid_clusters):
    work_layer = folium.FeatureGroup(name='作业点 (按簇)', show=True)
    noise_layer = folium.FeatureGroup(name='行驶/噪声点', show=True)
    colors = ['#FF4500', '#32CD32', '#1E90FF', '#FFD700', '#FF69B4', '#8A2BE2']
    if 'cluster' not in data_df.columns: print("❌ 错误：缺少'cluster'列"); return map_obj, work_layer, noise_layer
    work_count = noise_count = 0
    for idx, row in data_df.iterrows():
        cluster_id, lat, lon = row['cluster'], row['latitude'], row['longitude']
        if pd.isna(lat) or pd.isna(lon): continue
        coord = [lat, lon]
        popup = f"索引(筛选后): {idx}<br>原始索引: {row.get('original_index', 'N/A')}<br>时间: {row.get('time', 'N/A')}<br>速度: {row.get('speed_kmh', np.nan):.2f} km/h<br>簇: {cluster_id}"
        if cluster_id != -1 and cluster_id in valid_clusters:
            try: color = colors[valid_clusters.index(cluster_id) % len(colors)]
            except ValueError: color = 'black'
            folium.CircleMarker(location=coord, radius=2, color=color, fill=True, fill_color=color, fill_opacity=0.7, popup=popup).add_to(work_layer); work_count += 1
        else:
            folium.CircleMarker(location=coord, radius=1.5, color='gray', fill=True, fill_color='gray', fill_opacity=0.56, popup=popup).add_to(noise_layer); noise_count += 1
    work_layer.add_to(map_obj); noise_layer.add_to(map_obj)
    print(f"✅ 作业点 (按簇) ({work_count}) 和 行驶/噪声点 ({noise_count}) 已添加到地图。")
    return map_obj, work_layer, noise_layer

def add_polygons_and_labels(map_obj, data_df, valid_clusters, proj_utm, transformer_to_utm, transformer_to_lonlat, max_edge_length_m, points_threshold_for_polygon, border_expansion_m, grid_size_meters): # 添加多边形和标签
    polygon_layer = folium.FeatureGroup(name='作业区域边界', show=True); label_layer = folium.FeatureGroup(name='作业区域编号', show=True)
    all_grids_union = set(); html_content_parts = ['<div style="font-size: 14px; font-family: sans-serif;">']
    total_area_mu = 0; total_trajectory_area_mu = 0; area_counter = 1
    colors = ['#FF4500', '#32CD32', '#1E90FF', '#FFD700', '#FF69B4', '#8A2BE2']
    print(f"📐 开始为 {len(valid_clusters)} 个有效作业簇计算几何边界...")
    
    # 获取原始GPS数据
    original_points = None
    if hasattr(data_df, 'original_points'):
        original_points = data_df.original_points
    elif 'original_gps_data' in globals():
        original_points = globals()['original_gps_data']
    
    if not valid_clusters:
        print("ℹ️ 无有效簇可绘制。"); polygon_layer.add_to(map_obj); label_layer.add_to(map_obj)
        html_content_parts.append('<p>无有效作业区域。</p></div>'); full_html_content = "".join(html_content_parts)
        info_box_html = create_info_box_html(full_html_content); map_obj.get_root().html.add_child(folium.Element(info_box_html))
        return map_obj, polygon_layer, label_layer, full_html_content, all_grids_union, {}
    
    # 创建一个字典存储每个地块的信息，包括工作时间
    plot_info = {}
    
    # 使用多线程并行处理每个簇
    def process_cluster(cluster_id):
        try:
            cluster_data = data_df[data_df['cluster'] == cluster_id]; points_lonlat = cluster_data[['longitude', 'latitude']].values
            cluster_traj_lat = cluster_data['latitude'].values; cluster_traj_lon = cluster_data['longitude'].values # 重新基于每个簇，原始轨迹点计算轨迹长度（向量版）
            cluster_distances = vectorized_haversine(cluster_traj_lat[:-1], cluster_traj_lon[:-1], cluster_traj_lat[1:], cluster_traj_lon[1:])
            cluster_path_length = np.sum(cluster_distances) * 1000 # km ➔ m
            cluster_track_area_m2_est = cluster_path_length * grid_size_meters # 初步轨迹面积(估算)
            if len(points_lonlat) < points_threshold_for_polygon: 
                print(f"ℹ️ 簇 {cluster_id} 点数不足 ({len(points_lonlat)} < {points_threshold_for_polygon})，跳过地块生成。")
                return None
            
            print(f"⏳ 正在处理簇 {cluster_id}...")
            outer_polygon = get_cluster_polygon_implementation(points_lonlat, max_edge_length_m)
            if not (outer_polygon and outer_polygon.is_valid and not outer_polygon.is_empty):
                print(f"⚠️ 簇 {cluster_id} 无法生成有效外轮廓。")
                return None
                
            outer_polygon_coords_lonlat = list(outer_polygon.exterior.coords)
            utm_area_m2 = 0
            expanded_polygon_coords_latlon_folium = []
            if proj_utm and transformer_to_utm and transformer_to_lonlat:
                try:
                    utm_coords = [transformer_to_utm.transform(lon, lat) for lon, lat in outer_polygon_coords_lonlat]
                    if len(utm_coords) >= 3:
                        utm_polygon_shape = Polygon(utm_coords)
                        expanded_utm_polygon = utm_polygon_shape.buffer(border_expansion_m)
                        utm_area_m2 = expanded_utm_polygon.area
                        expanded_coords_lonlat = [transformer_to_lonlat.transform(x, y) for x, y in expanded_utm_polygon.exterior.coords]
                        expanded_polygon_coords_latlon_folium = [(lat, lon) for lon, lat in expanded_coords_lonlat]
                except Exception as e:
                    print(f"⚠️ 簇 {cluster_id} UTM处理出错: {e}")
            
            display_area_m2 = utm_area_m2 if utm_area_m2 > 0 else 0
            area_mu = round(display_area_m2 / 666.67, 1)
            traj_points_array = cluster_data[['longitude', 'latitude']].values
            if len(traj_points_array) > 30: traj_points_array = traj_points_array[15:-15]
            traj_points_list = traj_points_array[:, [1, 0]].tolist()
            coverage_area_m2_dedup, grid_set_cluster = calculate_trajectory_coverage_area(traj_points=traj_points_list,
                                                                                grid_size_meters=grid_size_meters, 
                                                                                transformer_to_utm=transformer_to_utm)
            coverage_area_mu_dedup = round(coverage_area_m2_dedup / 666.67, 1)
            per_mu_track_length = round(cluster_path_length / coverage_area_mu_dedup, 2) if coverage_area_mu_dedup > 0 else 0
            
            # 计算该地块的工作时间 - 使用原始GPS点
            work_hours = 0
            time_threshold_seconds = 60
            
            if hasattr(data_df, 'original_points') and data_df.original_points is not None:
                # 获取地块多边形
                if outer_polygon and outer_polygon.is_valid:
                    try:
                        # 使用原始点计算
                        original_points = data_df.original_points
                        if 'time' in original_points.columns and len(original_points) > 1:
                            # 向量化处理：一次性检查所有点是否在多边形内
                            points_in_polygon = []
                            # 通过批处理减少循环开销
                            batch_size = 1000
                            for i in range(0, len(original_points), batch_size):
                                batch = original_points.iloc[i:i+batch_size]
                                for idx, row in batch.iterrows():
                                    try:
                                        point = Point(row['longitude'], row['latitude'])
                                        if outer_polygon.contains(point):
                                            points_in_polygon.append(row)
                                    except:
                                        continue
                            
                            # 如果有足够的点，计算有效作业时间
                            if len(points_in_polygon) >= 2:
                                # 按时间排序
                                sorted_points = sorted(points_in_polygon, key=lambda x: x['time'])
                                
                                # 累加有效工作时间
                                total_work_seconds = 0
                                for i in range(1, len(sorted_points)):
                                    # 计算相邻点之间的时间差
                                    time_diff_seconds = (sorted_points[i]['time'] - sorted_points[i-1]['time']).total_seconds()
                                    
                                    # 如果时间差小于阈值，累加为有效工作时间
                                    if time_diff_seconds <= time_threshold_seconds:
                                        total_work_seconds += time_diff_seconds
                                    else:
                                        pass
                                
                                # 转换为小时
                                work_hours = total_work_seconds / 3600
                    except Exception as e:
                        print(f"⚠️ 使用原始点计算簇 {cluster_id} 工作时间时出错: {e}")
            
            # 如果使用原始点计算失败，退回到使用聚类点计算
            if work_hours == 0 and 'time' in cluster_data.columns and len(cluster_data) > 1:
                try:
                    # 按时间排序
                    sorted_times = cluster_data['time'].dropna().sort_values()
                    if len(sorted_times) >= 2:
                        # 使用累加法计算聚类点的工作时间
                        points_ts = sorted_times.to_frame().reset_index()
                        total_seconds = 0
                        for i in range(1, len(points_ts)):
                            time_diff = (points_ts.iloc[i]['time'] - points_ts.iloc[i-1]['time']).total_seconds()
                            if time_diff <= time_threshold_seconds:
                                total_seconds += time_diff
                        work_hours = total_seconds / 3600
                except Exception as e:
                    print(f"⚠️ 计算簇 {cluster_id} 工作时间时出错: {e}")
            
            # 存储地块信息，包括工作时间
            for i, valid_id in enumerate(valid_clusters):
                if valid_id == cluster_id:
                    plot_idx = i + 1
                    
                    # 无论是否有效，都先创建数据对象
                    plot_data = {
                        'cluster_id': cluster_id,
                        'area_m2': int(display_area_m2),
                        'area_mu': area_mu,
                        'trajectory_length': int(cluster_path_length),
                        'deduplicated_area_m2': int(coverage_area_m2_dedup),
                        'deduplicated_area_mu': coverage_area_mu_dedup,
                        'track_per_mu': per_mu_track_length,
                        'work_hours': work_hours,
                        'outer_polygon_coords': outer_polygon_coords_lonlat,
                        'expanded_polygon_coords': expanded_polygon_coords_latlon_folium,
                        'grid_set': grid_set_cluster,
                        'is_valid': True,  # 默认为有效
                        'point_count': len(points_lonlat)  # 保存点数
                    }
                    
                    # 检查是否应该排除此地块
                    if per_mu_track_length > 1000 or coverage_area_m2_dedup < 400:
                        print(f"🚫 簇 {cluster_id} 被排除（每亩轨迹长度={per_mu_track_length}米，轨迹去重面积={coverage_area_m2_dedup:.1f}平方米）")
                        plot_data['is_valid'] = False  # 标记为无效
                    
                    return (plot_idx, plot_data, cluster_id)
            
            return None
        except Exception as e:
            print(f"处理簇 {cluster_id} 时出错: {str(e)}")
            traceback.print_exc()
            return None
    
    # 使用线程池并行处理簇
    with ThreadPoolExecutor(max_workers=min(8, len(valid_clusters))) as executor:
        results = list(executor.map(process_cluster, valid_clusters))
    
    # 处理结果
    all_grid_sets = []
    for result in results:
        if result is None:
            continue
        
        plot_idx, plot_data, cluster_id = result
        plot_info[str(plot_idx)] = plot_data
        
        # 收集所有有效地块的栅格集合
        if plot_data['is_valid'] and 'grid_set' in plot_data:
            all_grid_sets.append(plot_data['grid_set'])
            all_grids_union.update(plot_data['grid_set'])
        
        # 有效地块才添加到HTML和地图
        if plot_data['is_valid']:
            area_mu = plot_data['area_mu']
            coverage_area_mu_dedup = plot_data['deduplicated_area_mu']
            cluster_path_length = plot_data['trajectory_length']
            work_hours = plot_data['work_hours']
            per_mu_track_length = plot_data['track_per_mu']
            cluster_track_area_m2_est = cluster_path_length * grid_size_meters
            display_area_m2 = plot_data['area_m2']
            outer_polygon_coords_lonlat = plot_data['outer_polygon_coords']
            expanded_polygon_coords_latlon_folium = plot_data['expanded_polygon_coords']
            
            total_area_mu += area_mu
            total_trajectory_area_mu += coverage_area_mu_dedup
            
            if len(expanded_polygon_coords_latlon_folium) >= 3:
                cluster_color_idx = valid_clusters.index(cluster_id) if cluster_id in valid_clusters else area_counter - 1
                cluster_color = colors[cluster_color_idx % len(colors)]
                folium.Polygon(locations=[(lat, lon) for lon, lat in outer_polygon_coords_lonlat], 
                            color=cluster_color, weight=2, fill=True, 
                            fill_color=cluster_color, fill_opacity=0.2, 
                            tooltip=f"作业区域 (簇 {cluster_id})").add_to(polygon_layer)
                folium.Polygon(locations=expanded_polygon_coords_latlon_folium, 
                            color='white', weight=2.5, fill=False, 
                            tooltip=f"扩展边界 (簇 {cluster_id})").add_to(polygon_layer)
                
                # 获取中心点，可以从外部多边形计算
                center_point_lonlat = (np.mean([lon for lon, lat in outer_polygon_coords_lonlat]), 
                                    np.mean([lat for lon, lat in outer_polygon_coords_lonlat]))
                center_point_latlon = (center_point_lonlat[1], center_point_lonlat[0])
                
                folium.Marker(location=center_point_latlon, 
                            icon=folium.DivIcon(html=f'<div style="font-size:16pt;color:{cluster_color};font-weight:bold;">{plot_idx}</div>'), 
                            tooltip=f"地块 {plot_idx}").add_to(label_layer)
                
                # 使用保存在plot_data中的点数而不是外部的points_lonlat
                point_count = plot_data.get('point_count', 0)
                html_content_parts.append(f'<p><strong>地块 {plot_idx} (簇 {cluster_id})</strong><br>地块面积: {int(display_area_m2)} m² (约 {area_mu} 亩)<br>轨迹长度: {int(cluster_path_length)} 米<br>轨迹面积(估): {int(cluster_track_area_m2_est)} m² (约 {cluster_track_area_m2_est / 666.67:.1f} 亩)<br>轨迹去重面积: {int(plot_data["deduplicated_area_m2"])} m² (约 {coverage_area_mu_dedup} 亩)<br>每亩轨迹长度: {int(round(per_mu_track_length))} 米<br>作业时间: {work_hours:.1f} 小时<br>点数: {point_count}</p>')
                area_counter += 1
    
    html_content_parts.insert(1, f'<p><strong>总地块面积: {round(total_area_mu, 1)} 亩</strong></p><p><strong>总去重轨迹面积: {round(total_trajectory_area_mu, 1)} 亩</strong></p>')
    html_content_parts.append('</div>'); full_html_content = "".join(html_content_parts)
    info_box_html = create_info_box_html(full_html_content); map_obj.get_root().html.add_child(folium.Element(info_box_html))
    polygon_layer.add_to(map_obj); label_layer.add_to(map_obj)
    print(f"✅ 作业区域边界和编号标签添加完成。")
    
    # 返回地块信息
    return map_obj, polygon_layer, label_layer, full_html_content, all_grids_union, plot_info

def create_info_box_html(content): # 此函数只负责生成HTML字符串
    return f'''
<div id="info-box" style="position: fixed;top: 80px;left: 20px;z-index: 1000;background: rgba(255, 255, 255, 0.92);backdrop-filter: blur(8px);-webkit-backdrop-filter: blur(8px);padding: 20px 24px;border-radius: 16px;box-shadow: 0 8px 24px rgba(0,0,0,0.15);max-height: 80vh;overflow-y: auto;font-family: 'Helvetica Neue', 'Segoe UI', 'Roboto', sans-serif;font-size: 14px;line-height: 1.6;color: #333;min-width: 320px;border: 1px solid rgba(0,0,0,0.08);">
    <h2 style="margin-top: 0; margin-bottom: 16px; font-size: 18px; font-weight: 700; display: flex; align-items: center;">
        <span style="font-size:20px; margin-right:8px;">🗂️</span>地块信息</h2>
    <div style="height:1px; background:#e0e0e0; margin:12px 0;"></div><div id="info-content">{content}</div></div>
<button id="toggle-button" style="position: fixed;top: 30px;left: 20px;z-index: 1001;background: #ffffff;border: 1px solid rgba(0,0,0,0.15);border-radius: 50%;width: 36px;height: 36px;font-size: 20px;font-weight: bold;color: #333;cursor: pointer;box-shadow: 0 4px 12px rgba(0,0,0,0.2);display: flex;align-items: center;justify-content: center;transition: all 0.3s ease;">–</button>
<script>var infoBox = document.getElementById("info-box"); var toggleButton = document.getElementById("toggle-button"); var expanded = true; toggleButton.onclick = function() {{ if (expanded) {{ infoBox.style.display = "none"; toggleButton.textContent = "+"; }} else {{ infoBox.style.display = "block"; toggleButton.textContent = "–"; }} expanded = !expanded; }};</script>'''

# 创建地块信息JSON文件（面积、轨迹长度等）
def create_plot_info_json(data_df, valid_clusters, polygon_info_dict):
    area_values = []
    traj_area_values = []
    distance_values = []
    track_per_mu_values = []
    hour_values = []
    
    # 按地块编号顺序处理
    for plot_idx in range(1, len(polygon_info_dict) + 1):
        if str(plot_idx) in polygon_info_dict:
            info = polygon_info_dict[str(plot_idx)]
            # 只添加有效地块数据
            if info.get('is_valid', True):  # 默认为True以处理旧数据
                # 使用平方米作为面积单位
                area_values.append(info['area_m2'])
                traj_area_values.append(info['deduplicated_area_m2'])
                # 转换轨迹长度从米到千米
                distance_values.append(info['trajectory_length'] / 1000)
                # 添加工作时间（小时）
                hour_values.append(info['work_hours'])
                # 每亩轨迹长度
                track_per_mu_values.append(info['track_per_mu'])
    
    # 创建JSON结构
    info_json = {
        "area": area_values,
        "traj_area": traj_area_values,
        "distance": distance_values,
        "hour": hour_values,
        "track_per_mu": track_per_mu_values
    }
    
    return info_json

# 创建地块边界JSON文件
def create_plot_boundary_json(plot_info):
    plots_json = {}
    
    # 按地块编号创建边界数据
    for plot_id, info in plot_info.items():
        # 只处理有效地块
        if info.get('is_valid', True) and 'outer_polygon_coords' in info:  # 默认为True以处理旧数据
            # 格式化为指定的结构
            out_coords = []
            coord_list = []
            for lon, lat in info['outer_polygon_coords']:
                coord_list.append({"longitude": lon, "latitude": lat})
            if coord_list:
                out_coords.append(coord_list)
            
            # 对于每个地块创建一个输出文件
            plots_json[plot_id] = {
                "out": out_coords,
                "in": []  # 当前无内部空洞，设为空数组
            }
    
    return plots_json

def process_file(full_path):
    try:
        print(f"\n--- 开始处理文件: {os.path.basename(full_path)} ---")
        main(full_path)
        return True
    except Exception as e:
        print(f"处理文件 {os.path.basename(full_path)} 时出错: {str(e)}")
        traceback.print_exc()
        return False

def main(input_filename):
    gc.collect() # 每次main进来都清空上一次内存垃圾
    print("--- 开始处理农机轨迹数据 (仅使用人造轨迹点模式) ---"); start_total_time = time.time()
    # 从完整路径中提取文件名，并获取不含扩展名的部分作为输出前缀
    file_basename = os.path.basename(input_filename)
    output_map_prefix = os.path.splitext(file_basename)[0]
    speed_filter_min = 1; speed_filter_max = 20 # 速度筛选区间
    trajectory_sampling_interval_m = 10.0 # 插值采样间隔（米）
    grid_size_meters = 2.3 # ✅ 作业幅宽（米）
    dbscan_eps_meters = 15; dbscan_min_samples = 9 # DBSCAN参数
    max_edge_length_m = 20; border_expansion_m = grid_size_meters/2 # 三角剖分最大边长, 边界扩展距离
    effective_min_cluster_size = 100; effective_points_threshold_for_polygon = 100 # 有效簇最小点数, 绘制多边形所需最少点数
    raw_data = load_data(input_filename); # 加载原始数据
    if raw_data is None: return
    data_with_speed = calculate_speed_distance(raw_data) # 计算速度和距离
    cleaned_data_for_path = filter_by_speed(data_with_speed, speed_filter_min, speed_filter_max) # 速度清洗
    if cleaned_data_for_path.empty: print("❌ 速度清洗后无数据"); return
    print("🚀 生成人造轨迹点进行DBSCAN分析。")
    raw_traj_lat = cleaned_data_for_path['latitude'].values; raw_traj_lon = cleaned_data_for_path['longitude'].values # 用原始清洗后的点计算轨迹长度
    raw_traj_distances = vectorized_haversine(raw_traj_lat[:-1], raw_traj_lon[:-1], raw_traj_lat[1:], raw_traj_lon[1:])
    raw_total_path_length_m = np.sum(raw_traj_distances) * 1000 # km ➔ m
    print(f"📏 原始轨迹总长度: {raw_total_path_length_m:.2f} 米")
    points_for_interpolation_df = cleaned_data_for_path[['latitude', 'longitude']].copy(); points_for_interpolation_df.reset_index(drop=True, inplace=True) # 插值生成轨迹点
    interpolated_df, total_path_length_m = generate_uniformly_spaced_points_on_path(points_for_interpolation_df, trajectory_sampling_interval_m)
    print(f"📏 人造轨迹总长度: {total_path_length_m:.2f} 米")
    if interpolated_df.empty: print("❌ 人造轨迹点生成失败"); return
    points_for_dbscan = interpolated_df
    print(f"ℹ️ 最小簇大小: {effective_min_cluster_size}，多边形最少点数: {effective_points_threshold_for_polygon}")
    data_clustered = run_dbscan(points_for_dbscan, dbscan_eps_meters, dbscan_min_samples) # 轨迹聚类
    print("ℹ️ 聚类完成，开始筛选有效簇..."); valid_cluster_ids = []; final_data = data_clustered.copy()
    if 'cluster' in final_data.columns and not final_data[final_data['cluster'] != -1].empty:
        cluster_counts = final_data[final_data['cluster'] != -1]['cluster'].value_counts()
        valid_ids_temp = cluster_counts[cluster_counts >= effective_min_cluster_size].index.tolist()
        invalid_ids_to_mark_noise = cluster_counts[cluster_counts < effective_min_cluster_size].index.tolist()
        if invalid_ids_to_mark_noise: final_data.loc[final_data['cluster'].isin(invalid_ids_to_mark_noise), 'cluster'] = -1
        current_valid_clusters_in_data = sorted(list(final_data[final_data['cluster'] != -1]['cluster'].unique()))
        valid_cluster_ids = [cid for cid in valid_ids_temp if cid in current_valid_clusters_in_data]
    print(f"👍 保留 {len(valid_cluster_ids)} 个有效簇: {valid_cluster_ids}")
    print("🗺️ 开始创建地图..."); center_lat, center_lon = 39.9, 116.4 # 确定地图中心点
    if valid_cluster_ids and not final_data[final_data['cluster'].isin(valid_cluster_ids)].empty:
        center_lat = final_data.loc[final_data['cluster'].isin(valid_cluster_ids), 'latitude'].mean()
        center_lon = final_data.loc[final_data['cluster'].isin(valid_cluster_ids), 'longitude'].mean()
    elif not final_data.empty: center_lat = final_data['latitude'].mean(); center_lon = final_data['longitude'].mean()
    elif not cleaned_data_for_path.empty: center_lat = cleaned_data_for_path['latitude'].mean(); center_lon = cleaned_data_for_path['longitude'].mean()
    elif not raw_data.empty: center_lat = raw_data['latitude'].mean(); center_lon = raw_data['longitude'].mean()
    map_vis = create_base_map(center_lat, center_lon) # 创建基础地图
    track_layer = folium.FeatureGroup(name='原始轨迹动画 (全部)', show=True) # ✅ 图层3：原始轨迹动画
    if not raw_data.empty:
        locations = raw_data[['latitude', 'longitude']].dropna().values.tolist()
        if locations and len(locations) >= 2: plugins.AntPath(locations, color='#00a9e2', weight=3, opacity=0.8, delay=40000, pulse_color='00a9e2').add_to(track_layer)
    track_layer.add_to(map_vis)
    proj_utm, transformer_to_utm, transformer_to_lonlat = None, None, None # 创建作业区边界+标签
    if valid_cluster_ids:
        all_valid_clustered_points = final_data[final_data['cluster'].isin(valid_cluster_ids)]
        if not all_valid_clustered_points.empty:
            centroid_lat_poly = all_valid_clustered_points['latitude'].mean(); centroid_lon_poly = all_valid_clustered_points['longitude'].mean()
            proj_utm, transformer_to_utm, transformer_to_lonlat = get_utm_projection(centroid_lat_poly, centroid_lon_poly)
        else: print(f"⚠️ 无法计算质心确定UTM区。")
    
    # 将原始GPS数据附加到final_data
    if hasattr(final_data, '__dict__'):
        final_data.original_points = raw_data
    else:
        # 如果final_data不支持附加属性，使用全局变量
        global original_gps_data
        original_gps_data = raw_data
    
    map_vis, polygon_layer, label_layer, html_info, all_grids_union, plot_info = add_polygons_and_labels(
        map_obj=map_vis, data_df=final_data, valid_clusters=valid_cluster_ids,
        proj_utm=proj_utm, transformer_to_utm=transformer_to_utm, transformer_to_lonlat=transformer_to_lonlat,
        max_edge_length_m=max_edge_length_m, points_threshold_for_polygon=effective_points_threshold_for_polygon,
        border_expansion_m=border_expansion_m, grid_size_meters=grid_size_meters)
        
    if transformer_to_lonlat and all_grids_union : map_vis = add_grid_cells_to_map(map_vis, all_grids_union, grid_size_meters, transformer_to_lonlat) # 添加轨迹覆盖栅格
    else: print("ℹ️ 未添加轨迹覆盖栅格图层，因缺少UTM转换器或栅格数据。")
    folium.LayerControl().add_to(map_vis) # 添加图层开关控件
    current_time_str = datetime.datetime.now().strftime('%Y%m%d_%H%M%S') # 保存地图为HTML
    
    # 确保result目录存在
    script_dir = os.path.dirname(os.path.abspath(__file__))
    result_dir = os.path.join(script_dir, "result")
    if not os.path.exists(result_dir):
        os.makedirs(result_dir)
    
    # 保存HTML地图文件
    output_filename = os.path.join(result_dir, f"{output_map_prefix}_result_{current_time_str}.html")
    map_vis.save(output_filename)
    print(f"✅ 地图已保存为: {output_filename}")
    
    # 生成并保存地块信息JSON文件
    info_json_data = create_plot_info_json(final_data, valid_cluster_ids, plot_info)
    info_json_filename = os.path.join(result_dir, f"{output_map_prefix}_info_{current_time_str}.json")
    with open(info_json_filename, 'w', encoding='utf-8') as f:
        json.dump(info_json_data, f)
    print(f"✅ 地块信息已保存为: {info_json_filename}")
    
    # 生成并保存地块边界JSON文件
    boundary_json_data = create_plot_boundary_json(plot_info)
    boundary_json_filename = os.path.join(result_dir, f"{output_map_prefix}_boundary_{current_time_str}.json")
    with open(boundary_json_filename, 'w', encoding='utf-8') as f:
        json.dump(boundary_json_data, f)
    print(f"✅ 地块边界已保存为: {boundary_json_filename}")
    
    end_total_time = time.time() # 总结统计
    print(f"--- 全部处理完成 ---\n总耗时: {end_total_time - start_total_time:.2f} 秒\n轨迹总长度: {total_path_length_m:.2f} 米")
    total_points_in_clustering = len(points_for_dbscan)
    work_points_count_final = len(final_data[final_data['cluster'] != -1]) if 'cluster' in final_data else 0
    noise_points_final = len(final_data[final_data['cluster'] == -1]) if 'cluster' in final_data else 0
    print(f"聚类点总数 (人造轨迹点): {total_points_in_clustering}\n最终识别作业点数量: {work_points_count_final}\n最终识别噪声点数量: {noise_points_final}\n有效作业区域数量 (初步筛选后): {len(valid_cluster_ids)}")
    if 'time' in raw_data.columns and len(raw_data) > 1:
        try:
            sorted_raw_data_time = raw_data['time'].dropna().sort_values()
            if len(sorted_raw_data_time) >= 2: print(f"原始数据作业总时长: {(sorted_raw_data_time.iloc[-1] - sorted_raw_data_time.iloc[0]).total_seconds() / 3600:.3f} 小时")
            else: print("原始数据有效时间点不足2个，无法计算作业时长。")
        except Exception as e: print(f"计算时长出错: {e}")

if __name__ == '__main__':
    overall_start_time = time.time()  # 开始时间
    # 新的JSON文件路径列表
    input_filenames = [
        # "Json/41.31transformed_data.json",
        # "Json/64.15transformed_data.json",
        # "Json/20220928_converted_wgs84.json",
        # "Json/20221014_converted_wgs84.json",
        "Json/20221016_converted_wgs84.json",
        # "Json/20240511_8004_data_wgs84.json",
        # "Json/20240511_wgs84.json",
        # "Json/20240706_191_wgs84.json",
        # "Json/20240706_195_wgs84.json",
        # "Json/20240706_196_wgs84.json",
        # "Json/20240706_197_wgs84.json",
        # "Json/20240706_198_wgs84.json",
        # "Json/866214077609600_20240809_wgs84.json",
        # "Json/866214077609600_20240810_wgs84.json",
        # "Json/866214077609600_20240812_wgs84.json",
        # "Json/866214077609600_20240813_wgs84.json",
        # "Json/866214077609600_20240814_wgs84.json",
        # "Json/866214077609600_20240815_wgs84.json",
        # "Json/866214077609600_20240816_wgs84.json",
        # "Json/866214077609600_20540819_wgs84.json",
        # "Json/866214077611416_20240621_wgs84.json",
        # "Json/866214077647923_20240909.json",
        # "Json/866214077647923_20240911.json",
        # "Json/866214077647923_20240917.json",
        # "Json/866214077647949_20250429.json",
        # "Json/866214077648004_20240507_wgs84.json",
        # "Json/866214077648004_20240512_wgs84.json",
        # "Json/866214077653095_20250118原地跳点.json",
        # "Json/866214077653095_20250125原地跳点.json",
        # "Json/extracted_gps_data100_wgs84.json",
        # "Json/39.7transformed_data.json"
    ]
    
    # 获取脚本所在目录路径
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 构建完整路径列表
    full_paths = [os.path.join(script_dir, filename) for filename in input_filenames]
    
    # 确定CPU核心数，并设置并行处理的进程数
    cpu_count = multiprocessing.cpu_count()
    process_count = min(cpu_count, len(full_paths))
    print(f"系统CPU核心数: {cpu_count}，将使用 {process_count} 个进程并行处理")
    
    # 使用进程池并行处理文件
    with ProcessPoolExecutor(max_workers=process_count) as executor:
        results = list(executor.map(process_file, full_paths))
    
    # 输出处理结果统计
    success_count = results.count(True)
    fail_count = results.count(False)
    overall_end_time = time.time()
    
    print(f"\n🏁 所有文件处理完成，总耗时：{overall_end_time - overall_start_time:.2f} 秒")
    print(f"成功: {success_count} 个文件，失败: {fail_count} 个文件")

    