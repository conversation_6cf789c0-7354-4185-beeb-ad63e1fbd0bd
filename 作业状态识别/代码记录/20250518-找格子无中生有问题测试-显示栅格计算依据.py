import json, pandas as pd, numpy as np, pyproj, time, datetime, gc, os
from math import radians, cos, sin, asin, sqrt
from sklearn.cluster import DBSCAN
from scipy.spatial import Delaunay, QhullError
from shapely.ops import unary_union, polygonize
from shapely.geometry import Polygon, LineString, Point
import traceback

EARTH_RADIUS_KM = 6371
SPEED_FILTER_MIN_KMH = 1
SPEED_FILTER_MAX_KMH = 30
TRAJECTORY_BASE_SAMPLING_INTERVAL_M = 10.0
SEGMENT_LENGTH_FOR_AVG_SPEED_M = 200.0
SPEED_THRESHOLD_KMH_FOR_DYNAMIC_SAMPLING = 12.0
HIGH_SPEED_SAMPLING_INTERVAL_M = 100.0
DBSCAN_EPS_METERS = 15
DBSCAN_MIN_SAMPLES = 9
MAX_EDGE_LENGTH_M_FOR_POLYGON = 23
BORDER_EXPANSION_M_FACTOR = 0.5
GRID_SIZE_METERS = 2.3
EFFECTIVE_MIN_CLUSTER_SIZE_POINTS = 30
EFFECTIVE_POINTS_THRESHOLD_FOR_POLYGON = 30
CLUSTER_TRIMMING_POINTS_COUNT = 15
MIN_COVERAGE_AREA_M2_THRESHOLD = 300
MIN_WORK_EFFICIENCY_MU_HR_THRESHOLD = 1.5
MAX_TRAVEL_SPEED_THRESHOLD_KMH = 15
MIN_ORIGINAL_POINTS_IN_PLOT_THRESHOLD = 50
WORK_TIME_CALC_TIME_THRESHOLD_SECONDS = 60
GENERATE_HTML_MAPS = True
_SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
OUTPUT_DIR_BASE = r"/作业状态识别/result"
TEST_OUTPUT_SUBDIR_MAPS = "html_maps"
FINAL_OUTPUT_SUBDIR_JSON = "json_data"

def load_trajectory_data(filename: str):
    try:
        data = pd.read_json(filename, encoding='utf-8')
        if not all(col in data.columns for col in ['latitude', 'longitude', 'time']):
            return None, f"❌ 错误：JSON文件 {filename} 缺少必需列 (latitude, longitude, time)"
        data['latitude'] = pd.to_numeric(data['latitude'], errors='coerce')
        data['longitude'] = pd.to_numeric(data['longitude'], errors='coerce')
        data['time'] = pd.to_datetime(data['time'], errors='coerce')
        initial_count = len(data)
        data.dropna(subset=['latitude', 'longitude', 'time'], inplace=True)
        cleaned_count = initial_count - len(data)
        if data.empty: return None, f"❌ 错误：文件 {filename} 有效数据为空。"
        return data, f"✅ 从 {filename} 加载 {len(data)} 条数据 (清理了 {cleaned_count} 条无效数据)。"
    except FileNotFoundError: return None, f"❌ 错误: 文件 '{filename}' 未找到。"
    except ValueError as e: return None, f"❌ 错误: 解析或类型转换失败 - {e}"
    except Exception as e: return None, f"❌ 未知错误: {e}"
def calculate_haversine_distances_vectorized(lat1, lon1, lat2, lon2, earth_radius_km_param):
    lat1, lon1, lat2, lon2 = map(np.radians, [lat1, lon1, lat2, lon2])
    dlat = lat2 - lat1; dlon = lon2 - lon1
    a = np.sin(dlat / 2.0)**2 + np.cos(lat1) * np.cos(lat2) * np.sin(dlon / 2.0)**2
    a = np.clip(a, 0, 1); c = 2 * np.arcsin(np.sqrt(a)); km = earth_radius_km_param * c
    return km
def haversine_single_point(lon1, lat1, lon2, lat2, earth_radius_km_param):
    lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])
    dlon = lon2 - lon1; dlat = lat2 - lat1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * asin(sqrt(a)); meters = earth_radius_km_param * 1000 * c
    return meters
def calculate_trajectory_metrics(data: pd.DataFrame):
    df = data.sort_values(by='time').copy()
    if len(df) < 2:
        df['distance_km'] = 0.0; df['speed_kmh'] = 0.0
        return df, "ℹ️ 点数少于2，速度与距离设为0。"
    distances_km = calculate_haversine_distances_vectorized(
        df['latitude'].values[:-1], df['longitude'].values[:-1],
        df['latitude'].values[1:], df['longitude'].values[1:],
        EARTH_RADIUS_KM)
    df['distance_km'] = np.concatenate(([0.0], distances_km))
    time_deltas_hours = df['time'].diff().dt.total_seconds().div(3600)
    time_deltas_hours = time_deltas_hours.replace(0, np.nan)
    time_deltas_hours[time_deltas_hours < 1e-9] = np.nan
    df['speed_kmh'] = df['distance_km'] / time_deltas_hours
    df['speed_kmh'] = df['speed_kmh'].fillna(0)
    df = df.replace([np.inf, -np.inf], np.nan)
    df['speed_kmh'] = df['speed_kmh'].fillna(0)
    df['original_index'] = df.index
    return df, "✅ 速度和距离计算完成。"
def filter_trajectory_by_speed(data_input: pd.DataFrame, speed_min: float, speed_max: float):
    data = data_input.copy()
    if 'speed_kmh' not in data.columns:
        return pd.DataFrame(columns=data.columns), "❌ 错误: 'speed_kmh' 列不存在，无法筛选。"
    data['speed_kmh'] = pd.to_numeric(data['speed_kmh'], errors='coerce')
    data = data.dropna(subset=['speed_kmh'])
    filtered_data = data[(data['speed_kmh'] >= speed_min) & (data['speed_kmh'] <= speed_max)].copy()
    removed_count = len(data_input) - len(filtered_data)
    return filtered_data, f"ℹ️ 速度筛选：保留 {len(filtered_data)} 个点，移除 {removed_count} 个点。"
def interpolate_trajectory_dynamically(input_df: pd.DataFrame):
    if input_df.empty or not all(c in input_df.columns for c in ['latitude', 'longitude', 'distance_km', 'time']):
        return pd.DataFrame(columns=['latitude', 'longitude']), 0.0, "ℹ️ 输入数据为空或缺列，无法插值。"
    if len(input_df) < 2:
        if all(c in input_df.columns for c in ['latitude', 'longitude']):
            return input_df[['latitude', 'longitude']].copy(), 0.0, "ℹ️ 点数少于2，返回原始单点。"
        return pd.DataFrame(columns=['latitude', 'longitude']), 0.0, "ℹ️ 点数少于2，无法插值。"
    path_points_list = [{'latitude': input_df.iloc[0]['latitude'], 'longitude': input_df.iloc[0]['longitude']}]
    overall_total_trajectory_length_m = 0.0
    current_segment_points_indices = [0]
    accumulated_dist_in_segment_m = 0.0
    df_sorted = input_df.sort_values(by='time').reset_index(drop=True)
    df_sorted['time_diff_seconds'] = df_sorted['time'].diff().dt.total_seconds().fillna(0)
    for i in range(1, len(df_sorted)):
        segment_dist_m = df_sorted.loc[i, 'distance_km'] * 1000
        overall_total_trajectory_length_m += segment_dist_m
        accumulated_dist_in_segment_m += segment_dist_m
        current_segment_points_indices.append(i)
        if accumulated_dist_in_segment_m >= SEGMENT_LENGTH_FOR_AVG_SPEED_M or i == len(df_sorted) - 1:
            start_idx = current_segment_points_indices[0]; end_idx = current_segment_points_indices[-1]
            current_processing_segment_df = df_sorted.iloc[start_idx : end_idx + 1]
            avg_speed_kmh_in_segment = 0
            if 'speed_kmh' in current_processing_segment_df.columns and not current_processing_segment_df['speed_kmh'].empty:
                avg_speed_kmh_in_segment = current_processing_segment_df['speed_kmh'].mean()
            else:
                total_dist_km_proc_seg = current_processing_segment_df['distance_km'].sum()
                total_time_sec_proc_seg = current_processing_segment_df['time_diff_seconds'].sum()
                if total_time_sec_proc_seg > 1e-9: avg_speed_kmh_in_segment = (total_dist_km_proc_seg / (total_time_sec_proc_seg / 3600.0))
            current_sampling_interval_m = TRAJECTORY_BASE_SAMPLING_INTERVAL_M
            if avg_speed_kmh_in_segment > SPEED_THRESHOLD_KMH_FOR_DYNAMIC_SAMPLING: current_sampling_interval_m = HIGH_SPEED_SAMPLING_INTERVAL_M
            accumulated_distance_within_processing_segment = 0.0
            last_gen_pt_coords = (path_points_list[-1]['longitude'], path_points_list[-1]['latitude'])
            dist_from_last_gen_to_seg_start_m = 0
            if len(path_points_list) > 1 :
                dist_from_last_gen_to_seg_start_m = haversine_single_point(
                    last_gen_pt_coords[0], last_gen_pt_coords[1],
                    current_processing_segment_df.iloc[0]['longitude'], current_processing_segment_df.iloc[0]['latitude'], EARTH_RADIUS_KM)
            next_sample_target_dist_overall = current_sampling_interval_m - dist_from_last_gen_to_seg_start_m
            if next_sample_target_dist_overall < 0: next_sample_target_dist_overall = current_sampling_interval_m
            for k in range(len(current_processing_segment_df) -1):
                p1_lat,p1_lon = current_processing_segment_df.iloc[k][['latitude','longitude']]
                p2_lat,p2_lon = current_processing_segment_df.iloc[k+1][['latitude','longitude']]
                micro_segment_dist_m = haversine_single_point(p1_lon,p1_lat,p2_lon,p2_lat,EARTH_RADIUS_KM)
                if micro_segment_dist_m == 0: continue
                while next_sample_target_dist_overall <= accumulated_distance_within_processing_segment + micro_segment_dist_m:
                    dist_into_micro_seg = next_sample_target_dist_overall - accumulated_distance_within_processing_segment
                    fraction = max(0.0,min(1.0,dist_into_micro_seg/micro_segment_dist_m if micro_segment_dist_m > 1e-9 else 0.0))
                    inter_lat = p1_lat + fraction * (p2_lat - p1_lat); inter_lon = p1_lon + fraction * (p2_lon - p1_lon)
                    if not path_points_list or haversine_single_point(inter_lon,inter_lat,path_points_list[-1]['longitude'],path_points_list[-1]['latitude'],EARTH_RADIUS_KM) > 1e-3:
                        path_points_list.append({'latitude':inter_lat,'longitude':inter_lon})
                    next_sample_target_dist_overall += current_sampling_interval_m
                accumulated_distance_within_processing_segment += micro_segment_dist_m
            accumulated_dist_in_segment_m = 0.0; current_segment_points_indices = [i]
    last_orig_point_df = df_sorted.iloc[-1]
    if path_points_list:
        last_gen_pt = path_points_list[-1]
        dist_to_last_orig_m = haversine_single_point(last_gen_pt['longitude'],last_gen_pt['latitude'],last_orig_point_df['longitude'],last_orig_point_df['latitude'],EARTH_RADIUS_KM)
        if dist_to_last_orig_m > 1e-3 : path_points_list.append({'latitude':last_orig_point_df['latitude'],'longitude':last_orig_point_df['longitude']})
    elif not df_sorted.empty: path_points_list.append({'latitude':last_orig_point_df['latitude'],'longitude':last_orig_point_df['longitude']})
    output_df = pd.DataFrame(path_points_list)
    if not output_df.empty: output_df.reset_index(drop=True, inplace=True)
    return output_df, overall_total_trajectory_length_m, f"✅ 动态间隔轨迹插值完成，生成 {len(output_df)} 点。"
def cluster_trajectory_points_dbscan(data: pd.DataFrame, eps_meters: float, min_samples: int):
    if data.empty or len(data) < min_samples:
        data_copy = data.copy(); data_copy['cluster'] = -1
        return data_copy, f"ℹ️ 输入点过少(<{min_samples})，跳过DBSCAN聚类。"
    eps_radians = eps_meters / (EARTH_RADIUS_KM * 1000)
    coords_rad = np.radians(data[['latitude', 'longitude']].values)
    if np.isnan(coords_rad).any():
        data_copy = data.copy(); data_copy['cluster'] = -1
        return data_copy, "❌ 坐标数据含NaN，无法DBSCAN。"
    db = DBSCAN(eps=eps_radians, min_samples=min_samples, metric='haversine', algorithm='ball_tree', n_jobs=1, leaf_size=50)
    try:
        db.fit(coords_rad); cluster_labels = db.labels_
        data_copy = data.copy(); data_copy['cluster'] = cluster_labels
    except ValueError as e:
        data_copy = data.copy(); data_copy['cluster'] = -1; cluster_labels = data_copy['cluster'].values
        return data_copy, f"❌ DBSCAN出错: {e}，所有点标记为噪声。"
    num_clusters = len(set(cluster_labels)) - (1 if -1 in cluster_labels else 0)
    num_noise = list(cluster_labels).count(-1)
    return data_copy, f"🌌 DBSCAN完成：发现 {num_clusters} 个簇，{num_noise} 个噪声点。"
def triangulate_points_delaunay(points_lonlat):
    if points_lonlat is None or not hasattr(points_lonlat,'__len__') or len(points_lonlat) < 3: return None, "点数不足3"
    try:
        points_array = np.array(points_lonlat,dtype=float)
        if np.isnan(points_array).any(): return None, "含NaN"
        noise = 5e-6; points_perturbed = np.copy(points_array)
        points_perturbed[:,0] += np.random.uniform(-noise,noise,size=points_array.shape[0])
        points_perturbed[:,1] += np.random.uniform(-noise,noise,size=points_array.shape[0])
        return Delaunay(points_perturbed), "成功"
    except QhullError: return None, f"QhullError (点数: {len(points_lonlat)})"
    except Exception as e: return None, f"未知错误: {e}"
def _is_valid_triangle(triangle_indices, points_array, max_length_m):
    for i in range(3):
        p1_idx, p2_idx = triangle_indices[i], triangle_indices[(i+1)%3]
        if p1_idx >= len(points_array) or p2_idx >= len(points_array): return False
        pt1, pt2 = points_array[p1_idx], points_array[p2_idx]
        if pd.isna(pt1[0])or pd.isna(pt1[1])or pd.isna(pt2[0])or pd.isna(pt2[1]): return False
        if haversine_single_point(pt1[0],pt1[1],pt2[0],pt2[1],EARTH_RADIUS_KM) > max_length_m: return False
    return True
def _extract_outer_edges(triangles_simplices, points_array):
    if not triangles_simplices: return []
    edge_count = {}
    for tri_indices in triangles_simplices:
        for i in range(3):
            p1_idx,p2_idx = tri_indices[i],tri_indices[(i+1)%3]
            if p1_idx >= len(points_array)or p2_idx >= len(points_array): continue
            edge = tuple(sorted((p1_idx,p2_idx)))
            edge_count[edge] = edge_count.get(edge,0)+1
    return [edge for edge,count in edge_count.items() if count==1]
def extract_cluster_polygon_boundary(points_lonlat, max_edge_len_m: float):
    if points_lonlat is None or not hasattr(points_lonlat,'__len__') or len(points_lonlat) < 3: return None, "点数不足3"
    points_array = np.array(points_lonlat, dtype=float)
    if len(points_array) < 3: return None, "数组点数不足3"
    tri, msg = triangulate_points_delaunay(points_array)
    if tri is None: return None, f"三角剖分失败: {msg}"
    valid_triangles_indices = [s for s in tri.simplices if _is_valid_triangle(s,tri.points,max_edge_len_m)]
    if not valid_triangles_indices: return None, "无有效三角形"
    outer_edges_indices = _extract_outer_edges(valid_triangles_indices, tri.points)
    if not outer_edges_indices: return None, "无外部边"
    edge_lines = []
    for edge_idx_pair in outer_edges_indices:
        p1_coords,p2_coords = tri.points[edge_idx_pair[0]],tri.points[edge_idx_pair[1]]
        if not (np.isnan(p1_coords).any()or np.isnan(p2_coords).any()):
            edge_lines.append(LineString([p1_coords,p2_coords]))
    if not edge_lines: return None, "无线段"
    try:
        merged_lines = unary_union(edge_lines); polygons = list(polygonize(merged_lines))
        if polygons: return max(polygons,key=lambda p:p.area), "成功"
        else: return None, "未形成多边形"
    except Exception as e: return None, f"合并边线或多边形化失败: {e}"
def determine_utm_projection(centroid_lat: float, centroid_lon: float):
    if pd.isna(centroid_lat) or pd.isna(centroid_lon):
        return None, None, None, "⚠️ 无法获取质心，无法确定UTM区域。"
    try:
        utm_zone = int((centroid_lon + 180) / 6) + 1
        hemisphere = 'north' if centroid_lat >= 0 else 'south'
        utm_crs_str = f"+proj=utm +zone={utm_zone} +{hemisphere} +ellps=WGS84 +datum=WGS84 +units=m +no_defs"
        proj_utm = pyproj.CRS(utm_crs_str); proj_wgs84 = pyproj.CRS('EPSG:4326')
        to_utm = pyproj.Transformer.from_crs(proj_wgs84, proj_utm, always_xy=True)
        to_lonlat = pyproj.Transformer.from_crs(proj_utm, proj_wgs84, always_xy=True)
        return proj_utm, to_utm, to_lonlat, f"💡 使用UTM Zone: {utm_zone} ({hemisphere})。"
    except Exception as e: return None, None, None, f"❌ 无法创建UTM投影: {e}"
def calculate_trajectory_coverage_grid(traj_points_latlon, grid_size_m: float, transformer_to_utm):
    if not traj_points_latlon or len(traj_points_latlon) < 2 or transformer_to_utm is None:
        return 0, set(), [], "❌ 轨迹点不足或无UTM转换器，无法计算覆盖面积。"
    traj_utm = [transformer_to_utm.transform(lon, lat) for lat, lon in traj_points_latlon]
    grid_set = set(); sampling_points_utm = []
    for i in range(len(traj_utm) - 1):
        x1,y1=traj_utm[i]; x2,y2=traj_utm[i+1]
        seg_len = sqrt((x2-x1)**2+(y2-y1)**2)
        if seg_len == 0: continue
        num_samples = max(int(seg_len / (grid_size_m / 2)), 1)
        for j in range(num_samples + 1):
            t = j / num_samples; x = x1 + (x2-x1)*t; y = y1 + (y2-y1)*t
            grid_set.add((int(x//grid_size_m), int(y//grid_size_m)))
            sampling_points_utm.append((x,y))
    area_m2 = len(grid_set) * (grid_size_m**2)
    return area_m2, grid_set, sampling_points_utm, "✅ 栅格覆盖计算完成。"
def _process_single_cluster_analysis(cluster_id, cluster_data_df, original_gps_data_df):
    try:
        points_lonlat = cluster_data_df[['longitude','latitude']].values
        if len(points_lonlat) < EFFECTIVE_POINTS_THRESHOLD_FOR_POLYGON:
            return cluster_id, None, f"ℹ️ 簇 {cluster_id} 点数 ({len(points_lonlat)}) 不足，跳过。"
        outer_polygon_shape, msg_poly = extract_cluster_polygon_boundary(points_lonlat, MAX_EDGE_LENGTH_M_FOR_POLYGON)
        if not (outer_polygon_shape and outer_polygon_shape.is_valid and not outer_polygon_shape.is_empty):
            return cluster_id, None, f"⚠️ 簇 {cluster_id} 无法生成有效外轮廓: {msg_poly}"
        outer_polygon_coords_lonlat = list(outer_polygon_shape.exterior.coords)
        plot_centroid_lat = np.mean([c[1] for c in outer_polygon_coords_lonlat])
        plot_centroid_lon = np.mean([c[0] for c in outer_polygon_coords_lonlat])
        _, to_utm, to_lonlat, _ = determine_utm_projection(plot_centroid_lat, plot_centroid_lon)
        utm_area_m2 = 0; expanded_polygon_coords_lonlat_shapely = []
        if to_utm and to_lonlat:
            try:
                utm_coords = [to_utm.transform(lon,lat) for lon,lat in outer_polygon_coords_lonlat]
                if len(utm_coords) >= 3:
                    utm_poly_shape = Polygon(utm_coords)
                    expanded_utm_poly = utm_poly_shape.buffer(GRID_SIZE_METERS * BORDER_EXPANSION_M_FACTOR)
                    utm_area_m2 = expanded_utm_poly.area
                    expanded_coords_utm = list(expanded_utm_poly.exterior.coords)
                    expanded_polygon_coords_lonlat_shapely = [to_lonlat.transform(x,y) for x,y in expanded_coords_utm]
            except Exception as e_utm: print(f"UTM处理异常 {cluster_id}: {e_utm}")
        work_hours = 0.0; avg_speed_original_points_kmh = 0.0; cluster_path_length_m = 0.0
        relevant_original_points_df = pd.DataFrame()
        if original_gps_data_df is not None and 'time' in original_gps_data_df.columns and not original_gps_data_df.empty:
            try:
                from shapely.prepared import prep
                prepared_polygon = prep(outer_polygon_shape)
                lon_idx = original_gps_data_df.columns.get_loc('longitude') + 1
                lat_idx = original_gps_data_df.columns.get_loc('latitude') + 1
                relevant_indices = [row_tuple[0] for row_tuple in original_gps_data_df.itertuples(index=True) if prepared_polygon.contains(Point(row_tuple[lon_idx], row_tuple[lat_idx]))]
                if relevant_indices:
                    relevant_original_points_df = original_gps_data_df.loc[relevant_indices].copy().sort_values(by='time')
                if not relevant_original_points_df.empty and len(relevant_original_points_df) >=2:
                    lats = relevant_original_points_df['latitude'].values; lons = relevant_original_points_df['longitude'].values
                    dists_km = calculate_haversine_distances_vectorized(lats[:-1],lons[:-1],lats[1:],lons[1:],EARTH_RADIUS_KM)
                    total_dist_km = np.sum(dists_km); cluster_path_length_m = total_dist_km * 1000
                    time_diffs_s = relevant_original_points_df['time'].diff().dt.total_seconds()
                    work_hours = time_diffs_s[time_diffs_s <= WORK_TIME_CALC_TIME_THRESHOLD_SECONDS].sum()/3600.0
                    if work_hours > 1e-6: avg_speed_original_points_kmh=round(total_dist_km/work_hours,1)
            except Exception as e_orig: print(f"原始点相关计算失败 {cluster_id}: {e_orig}")
        traj_points_for_grid_calc_latlon = relevant_original_points_df[['latitude','longitude']].values.tolist() if not relevant_original_points_df.empty else []
        internal_sampling_points_utm = []
        if len(traj_points_for_grid_calc_latlon) < 2:
            coverage_area_m2_grid, grid_set_cluster = 0, set()
        else:
            coverage_area_m2_grid, grid_set_cluster, internal_sampling_points_utm, _ = calculate_trajectory_coverage_grid(traj_points_for_grid_calc_latlon, GRID_SIZE_METERS, to_utm)
        area_mu = round(utm_area_m2 / 666.67, 2) if utm_area_m2 > 0 else 0
        coverage_area_mu_grid = round(coverage_area_m2_grid / 666.67, 2)
        track_per_mu_m = int(round(cluster_path_length_m / coverage_area_mu_grid)) if coverage_area_mu_grid > 0 else 0
        work_efficiency_mu_hr = round(coverage_area_mu_grid / work_hours, 2) if work_hours > 1e-6 else 0
        is_valid_plot = True; reasons_invalid = []
        if work_hours > 1e-6 and work_efficiency_mu_hr < MIN_WORK_EFFICIENCY_MU_HR_THRESHOLD:
            is_valid_plot = False; reasons_invalid.append(f"工作效率过低({work_efficiency_mu_hr:.2f}亩/时)")
        elif work_hours <= 1e-6 and coverage_area_mu_grid > 0.01 :
            is_valid_plot = False; reasons_invalid.append(f"无法计算有效工作效率(时长为0但有面积)")
        if utm_area_m2 < MIN_COVERAGE_AREA_M2_THRESHOLD:
             is_valid_plot = False; reasons_invalid.append(f"地块面积过小({int(utm_area_m2)}m²)")
        if work_hours > 1e-6 and avg_speed_original_points_kmh > MAX_TRAVEL_SPEED_THRESHOLD_KMH:
             is_valid_plot = False; reasons_invalid.append(f"原始点行进速度过快({avg_speed_original_points_kmh}km/h)")
        elif work_hours <= 1e-6 and not relevant_original_points_df.empty and len(relevant_original_points_df) >=2 and coverage_area_mu_grid > 0.01:
             is_valid_plot = False; reasons_invalid.append(f"无法计算原始点有效行进速度(时长为0但有面积)")
        original_points_in_plot_count = len(relevant_original_points_df)
        if original_points_in_plot_count < MIN_ORIGINAL_POINTS_IN_PLOT_THRESHOLD:
            is_valid_plot = False; reasons_invalid.append(f"地块内原始点过少({original_points_in_plot_count})")
        raw_points_per_mu = int(
            round(original_points_in_plot_count / coverage_area_mu_grid)) if coverage_area_mu_grid > 0 else 0
        plot_data = {
            'cluster_id': cluster_id, 'point_count': len(points_lonlat), 'polygon_area_m2': int(utm_area_m2), 'polygon_area_mu': area_mu,
            'trajectory_length_m': int(cluster_path_length_m), 'grid_coverage_area_m2': int(coverage_area_m2_grid), 'grid_coverage_area_mu': coverage_area_mu_grid,
            'track_per_mu_m': track_per_mu_m, 'work_hours': round(work_hours,2), 'work_efficiency_mu_hr': work_efficiency_mu_hr, 'avg_speed_original_points_kmh': avg_speed_original_points_kmh,
            'original_points_in_plot': original_points_in_plot_count, 'raw_points_per_mu': raw_points_per_mu, 'outer_polygon_coords_lonlat': outer_polygon_coords_lonlat,
            'expanded_polygon_coords_lonlat': expanded_polygon_coords_lonlat_shapely, 'grid_set_utm_indices': list(grid_set_cluster),
            'internal_sampling_points_utm': internal_sampling_points_utm,
            'is_valid': is_valid_plot, 'reasons_invalid': ", ".join(reasons_invalid) if reasons_invalid else "有效",
            'utm_transformers': (to_utm, to_lonlat) if to_utm and to_lonlat else (None, None)}
        return cluster_id, plot_data, f"✅ 簇 {cluster_id} 分析完成. 有效性: {is_valid_plot}."
    except Exception as e_main_process: return cluster_id, None, f"❌ 处理簇 {cluster_id} 时发生严重错误: {e_main_process}"
def analyze_plot_candidates_from_clusters(clustered_data_df: pd.DataFrame, original_gps_data_df_cleaned: pd.DataFrame, valid_cluster_ids: list):
    all_plot_info = {}
    results_log = []; plot_idx_counter = 1
    for cluster_id in valid_cluster_ids:
        cluster_data_subset = clustered_data_df[clustered_data_df['cluster'] == cluster_id].copy()
        try:
            _, plot_data, msg = _process_single_cluster_analysis(cluster_id, cluster_data_subset, original_gps_data_df_cleaned)
            results_log.append(msg)
            if plot_data and plot_data.get('is_valid', False):
                plot_idx_str = str(plot_idx_counter); all_plot_info[plot_idx_str] = plot_data
                plot_idx_counter += 1
        except Exception as exc: results_log.append(f"❌ 簇 {cluster_id} 分析执行失败: {exc}")
    status_message = f"🗺️ 地块分析完成. 处理了 {len(valid_cluster_ids)} 个候选簇. 有效地块数: {len(all_plot_info)}."
    return all_plot_info, status_message, results_log
def generate_plot_summary_json_structure(plot_info_dict: dict):
    summary = {"plot_id": [], "area_m2": [], "area_mu": [], "trajectory_length_m": [], "traj_coverage_area_m2": [], "traj_coverage_area_mu": [], "track_per_mu_m": [], "work_hours": [], "work_efficiency_mu_hr": [], "avg_speed_original_points_kmh": [], "original_points_in_plot": [], "raw_points_per_mu": []}
    sorted_plot_ids = sorted(plot_info_dict.keys(), key=lambda x: int(x))
    for plot_id_str in sorted_plot_ids:
        info = plot_info_dict[plot_id_str]
        if not info.get('is_valid', True): continue
        summary["plot_id"].append(int(plot_id_str)); summary["area_m2"].append(info['polygon_area_m2']); summary["area_mu"].append(info['polygon_area_mu'])
        summary["trajectory_length_m"].append(info['trajectory_length_m']); summary["traj_coverage_area_m2"].append(info['grid_coverage_area_m2']); summary["traj_coverage_area_mu"].append(info['grid_coverage_area_mu'])
        summary["track_per_mu_m"].append(info['track_per_mu_m']); summary["work_hours"].append(info['work_hours']); summary["work_efficiency_mu_hr"].append(info['work_efficiency_mu_hr'])
        summary["avg_speed_original_points_kmh"].append(info['avg_speed_original_points_kmh']); summary["original_points_in_plot"].append(info['original_points_in_plot']); summary["raw_points_per_mu"].append(info['raw_points_per_mu'])
    return summary
def generate_plot_boundaries_json_structure(plot_info_dict: dict):
    final_boundaries_payload = {"out": [], "in": []}
    sorted_plot_ids = sorted(plot_info_dict.keys(), key=lambda x: int(x))
    for plot_id_str in sorted_plot_ids:
        info = plot_info_dict[plot_id_str]
        if not info.get('is_valid', True): continue
        if 'outer_polygon_coords_lonlat' in info and info['outer_polygon_coords_lonlat']:
            one_plot_outer_ring = [{"longitude": lon, "latitude": lat} for lon, lat in info['outer_polygon_coords_lonlat']]
            if one_plot_outer_ring:
                final_boundaries_payload["out"].append(one_plot_outer_ring)
    return final_boundaries_payload
def calculate_dataframe_centroid(df: pd.DataFrame, lat_col='latitude', lon_col='longitude'):
    if df is None or df.empty or lat_col not in df.columns or lon_col not in df.columns: return None, None, "数据为空或缺少经纬度列"
    valid_points = df.dropna(subset=[lat_col, lon_col])
    if valid_points.empty: return None, None, "无有效经纬度点计算质心"
    return valid_points[lat_col].mean(), valid_points[lon_col].mean(), "质心计算成功"
def filter_and_trim_clusters(clustered_data: pd.DataFrame, min_cluster_size: int, trim_points_count: int):
    final_data = clustered_data.copy(); valid_cluster_ids = []; log_messages = []
    if 'cluster' not in final_data.columns or final_data[final_data['cluster'] != -1].empty:
        log_messages.append("ℹ️ 无有效聚类结果进行筛选和修剪。")
        return final_data, valid_cluster_ids, log_messages
    cluster_counts = final_data[final_data['cluster'] != -1]['cluster'].value_counts()
    valid_ids_by_size = cluster_counts[cluster_counts >= min_cluster_size].index.tolist()
    invalid_ids_by_size = cluster_counts[cluster_counts < min_cluster_size].index.tolist()
    if invalid_ids_by_size:
        final_data.loc[final_data['cluster'].isin(invalid_ids_by_size), 'cluster'] = -1
        log_messages.append(f"ℹ️ 移除了 {len(invalid_ids_by_size)} 个点数小于 {min_cluster_size} 的小簇。")
    current_valid_clusters_in_data = sorted(list(final_data[final_data['cluster'].isin(valid_ids_by_size)]['cluster'].unique()))
    for cluster_id in current_valid_clusters_in_data:
        cluster_points = final_data[final_data['cluster'] == cluster_id]
        if len(cluster_points) > trim_points_count * 2 and len(cluster_points) > min_cluster_size :
            cluster_points_sorted = cluster_points.sort_index()
            head_indices = cluster_points_sorted.iloc[:trim_points_count].index
            tail_indices = cluster_points_sorted.iloc[-trim_points_count:].index
            final_data.loc[head_indices, 'cluster'] = -1; final_data.loc[tail_indices, 'cluster'] = -1
            log_messages.append(f"ℹ️ 簇 {cluster_id}: 掐头去尾共排除 {len(head_indices) + len(tail_indices)} 个点。")
        else: log_messages.append(f"ℹ️ 簇 {cluster_id}: 点数 ({len(cluster_points)}) 不足或不满足掐头去尾条件，跳过。")
    final_cluster_counts = final_data[final_data['cluster'] != -1]['cluster'].value_counts()
    valid_cluster_ids = sorted(list(final_cluster_counts[final_cluster_counts >= min_cluster_size].index.tolist()))
    clusters_too_small_after_trim = final_cluster_counts[final_cluster_counts < min_cluster_size].index.tolist()
    if clusters_too_small_after_trim:
        final_data.loc[final_data['cluster'].isin(clusters_too_small_after_trim), 'cluster'] = -1
        log_messages.append(f"ℹ️ {len(clusters_too_small_after_trim)} 个簇在掐头去尾后点数不足 {min_cluster_size}，已标记为噪声。")
        valid_cluster_ids = sorted(list(final_data[final_data['cluster']!=-1]['cluster'].value_counts().loc[lambda x: x >= min_cluster_size].index))
    log_messages.append(f"👍 最终保留 {len(valid_cluster_ids)} 个有效簇: {valid_cluster_ids if valid_cluster_ids else '无'}")
    return final_data, valid_cluster_ids, log_messages
if GENERATE_HTML_MAPS:
    import folium
    from folium import plugins
    TIANDITU_KEY = "e7f645439e09d5fdbc5158bacab6d024"
    DEFAULT_MAP_CENTER_LAT = 39.9
    DEFAULT_MAP_CENTER_LON = 116.4
    DEFAULT_MAP_ZOOM = 16
    PLOT_COLORS = ['#FF4500', '#32CD32', '#1E90FF', '#FFD700', '#FF69B4', '#8A2BE2']
    LAYER_DEFAULT_VISIBILITY = { "原始轨迹动画 (全部)": False, "速度清洗后原始点": False,"人造轨迹点 (插值)": False, "作业点 (按簇)": False, "行驶/噪声点 (插值)": False,"作业区域边界": True,"作业区域编号": True, "轨迹覆盖栅格 (所有有效)": True, "栅格计算依据轨迹": False, "栅格计算采样点": False }
    def create_folium_base_map(center_lat: float, center_lon: float, zoom: int):
        map_vis = folium.Map(location=[center_lat, center_lon], zoom_start=zoom, tiles=None)
        folium.TileLayer(tiles='http://{s}.google.com/vt/lyrs=s&x={x}&y={y}&z={z}', attr='Google Satellite', name='Google Satellite', overlay=True, control=True, subdomains=['mt0', 'mt1', 'mt2', 'mt3'], max_zoom=20).add_to(map_vis)
        if TIANDITU_KEY:
            tdt_url = f'http://t0.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={{z}}&TILEROW={{y}}&TILECOL={{x}}&tk={TIANDITU_KEY}'
            folium.TileLayer(tiles=tdt_url, attr='天地图卫星影像', name='天地图卫星影像', overlay=False, control=True, max_zoom=20).add_to(map_vis)
        folium.TileLayer('openstreetmap', name='OpenStreetMap').add_to(map_vis)
        plugins.MeasureControl(position='topright', primary_length_unit='meters', primary_area_unit='sqmeters', active_color='red', completed_color='red').add_to(map_vis)
        return map_vis, "✅ 基础地图创建完成。"
    def add_raw_trajectory_antpath_to_map(map_obj, raw_data_df: pd.DataFrame, layer_name: str, color: str = '#00a9e2'):
        if raw_data_df is None or raw_data_df.empty: return "ℹ️ 原始数据为空，不添加轨迹动画。"
        show_default = LAYER_DEFAULT_VISIBILITY.get(layer_name, True)
        layer = folium.FeatureGroup(name=layer_name, show=show_default)
        locations = raw_data_df[['latitude', 'longitude']].dropna().values.tolist()
        if locations and len(locations) >= 2:
            plugins.AntPath(locations, color=color, weight=3, opacity=0.8, delay=40000, pulse_color='white').add_to(layer)
        layer.add_to(map_obj)
        return f"✅ 添加图层 '{layer_name}' ({len(locations)}点, 默认显示: {show_default})."
    def add_points_layer_to_map(map_obj, points_df: pd.DataFrame, layer_name: str, color: str, radius: float, tooltip_prefix: str = "点", speed_col: str = None, original_index_col: str = None, cluster_col: str = None):
        if points_df is None or points_df.empty: return f"ℹ️ 数据为空，不添加图层 '{layer_name}'."
        show_default = LAYER_DEFAULT_VISIBILITY.get(layer_name, True)
        layer = folium.FeatureGroup(name=layer_name, show=show_default); count = 0
        for idx, row in points_df.iterrows():
            lat, lon = row.get('latitude'), row.get('longitude')
            if pd.isna(lat) or pd.isna(lon): continue
            popup_parts = [f"{tooltip_prefix} (DF索引: {idx})"]
            if original_index_col and original_index_col in row and pd.notna(row[original_index_col]): popup_parts.append(f"原始索引: {row[original_index_col]}")
            if 'time' in row and pd.notna(row['time']): popup_parts.append(f"时间: {row['time']}")
            if speed_col and speed_col in row and pd.notna(row[speed_col]): popup_parts.append(f"速度: {row[speed_col]:.2f} km/h")
            if cluster_col and cluster_col in row and pd.notna(row[cluster_col]): popup_parts.append(f"簇: {row[cluster_col]}")
            folium.CircleMarker(location=[lat, lon], radius=radius, color=color, fill=True, fill_color=color, fill_opacity=0.6, popup="<br>".join(popup_parts), tooltip=f"{tooltip_prefix} {idx}").add_to(layer)
            count +=1
        layer.add_to(map_obj)
        return f"✅ 添加图层 '{layer_name}' ({count}点, 默认显示: {show_default})."
    def add_clustered_points_layers_to_map(map_obj, clustered_data_df: pd.DataFrame, valid_cluster_ids: list, work_layer_name: str, noise_layer_name: str):
        if clustered_data_df is None or clustered_data_df.empty or 'cluster' not in clustered_data_df.columns: return "ℹ️ 聚类数据为空或无'cluster'列，不添加聚类点图层。"
        show_work = LAYER_DEFAULT_VISIBILITY.get(work_layer_name, True); show_noise = LAYER_DEFAULT_VISIBILITY.get(noise_layer_name, True)
        work_layer = folium.FeatureGroup(name=work_layer_name, show=show_work); noise_layer = folium.FeatureGroup(name=noise_layer_name, show=show_noise)
        work_count, noise_count = 0, 0
        for idx, row in clustered_data_df.iterrows():
            cluster_id, lat, lon = row['cluster'], row['latitude'], row['longitude']
            if pd.isna(lat) or pd.isna(lon): continue
            popup = f"点索引(插值后): {idx}<br>簇: {cluster_id}"
            if 'original_index' in row: popup += f"<br>基于原索: {row['original_index']}"
            if 'time' in row : popup += f"<br>时间: {row.get('time', 'N/A')}"
            if 'speed_kmh' in row: popup += f"<br>速度: {row.get('speed_kmh', np.nan):.2f} km/h"
            if cluster_id != -1 and cluster_id in valid_cluster_ids:
                try: color = PLOT_COLORS[valid_cluster_ids.index(cluster_id) % len(PLOT_COLORS)]
                except ValueError: color = 'black'
                folium.CircleMarker(location=[lat,lon],radius=2,color=color,fill=True,fill_color=color,fill_opacity=0.7,popup=popup).add_to(work_layer); work_count+=1
            else: folium.CircleMarker(location=[lat,lon],radius=1.5,color='gray',fill=True,fill_color='gray',fill_opacity=0.56,popup=popup).add_to(noise_layer); noise_count+=1
        work_layer.add_to(map_obj); noise_layer.add_to(map_obj)
        return f"✅ 添加作业点图层 ({work_count}点, 默认显示: {show_work}) 和噪声点图层 ({noise_count}点, 默认显示: {show_noise})."
    def _create_info_box_html_content(plot_info_dict: dict):
        html_parts = ['<div style="font-size: 14px; font-family: sans-serif;">']
        total_polygon_area_mu = 0; total_grid_coverage_area_mu = 0; valid_plot_count = 0
        sorted_plot_ids = sorted(plot_info_dict.keys(), key=lambda x: int(x))
        for plot_id_str in sorted_plot_ids:
            info = plot_info_dict[plot_id_str]
            if not info.get('is_valid', False): continue
            valid_plot_count +=1; total_polygon_area_mu += info['polygon_area_mu']; total_grid_coverage_area_mu += info['grid_coverage_area_mu']
            html_parts.append(f"<p><strong>地块 {plot_id_str} (簇 {info['cluster_id']})</strong><br>"
                              f"  多边形面积: {info['polygon_area_m2']} m² (约 {info['polygon_area_mu']} 亩)<br>"
                              f"  轨迹长度: {info['trajectory_length_m']} 米<br>"
                              f"  栅格覆盖面积: {info['grid_coverage_area_m2']} m² (约 {info['grid_coverage_area_mu']} 亩)<br>"
                              f"  每亩轨迹: {info['track_per_mu_m']} 米<br>"
                              f"  作业时间: {info['work_hours']:.1f} 小时<br>"
                              f"  <span style='color:#ff6600;font-weight:bold;'>工作效率: {info['work_efficiency_mu_hr']:.2f} 亩/小时</span><br>"
                              f"  <span style='color:#009688;font-weight:bold;'>行进速度(原始点): {info['avg_speed_original_points_kmh']:.1f} km/h</span><br>"
                              f"  插值点数: {info['point_count']}<br>"
                              f"  <span style='color:#9c27b0;font-weight:bold;'>地块内原始点: {info['original_points_in_plot']} 点</span><br>"
                              f"  <span style='color:#9c27b0;font-weight:bold;'>每亩原始点: {info['raw_points_per_mu']} 点/亩</span></p>")
        if valid_plot_count == 0: html_parts.append('<p>无有效作业区域。</p>')
        else:
            html_parts.insert(1, f"<p><strong>总有效多边形面积: {round(total_polygon_area_mu, 1)} 亩</strong></p>"
                                 f"<p><strong>总有效栅格覆盖面积: {round(total_grid_coverage_area_mu, 1)} 亩</strong></p>")
        html_parts.append('</div>')
        return "".join(html_parts)
    def add_plots_visualization_to_map(map_obj, plot_info_dict: dict):
        if not plot_info_dict: return "ℹ️ 无地块信息可供可视化。"
        show_polygon = LAYER_DEFAULT_VISIBILITY.get('作业区域边界', True); show_label = LAYER_DEFAULT_VISIBILITY.get('作业区域编号', True)
        polygon_layer = folium.FeatureGroup(name='作业区域边界', show=show_polygon); label_layer = folium.FeatureGroup(name='作业区域编号', show=show_label)
        sorted_plot_ids = sorted(plot_info_dict.keys(), key=lambda x: int(x))
        for plot_id_str in sorted_plot_ids:
            plot_data = plot_info_dict[plot_id_str]
            if not plot_data.get('is_valid', False): continue
            cluster_id_for_plot = plot_data['cluster_id']; plot_numeric_id = int(plot_id_str)
            color = PLOT_COLORS[(plot_numeric_id - 1) % len(PLOT_COLORS)]
            outer_poly_coords_folium = [(lat, lon) for lon, lat in plot_data['outer_polygon_coords_lonlat']]
            if len(outer_poly_coords_folium) >= 3:
                folium.Polygon(locations=outer_poly_coords_folium, color=color, weight=2, fill=True, fill_color=color, fill_opacity=0.2, tooltip=f"作业区域 {plot_id_str} (簇 {cluster_id_for_plot})").add_to(polygon_layer)
            if 'expanded_polygon_coords_lonlat' in plot_data and plot_data['expanded_polygon_coords_lonlat']:
                expanded_poly_coords_folium = [(lat, lon) for lon, lat in plot_data['expanded_polygon_coords_lonlat']]
                if len(expanded_poly_coords_folium) >= 3:
                    folium.Polygon(locations=expanded_poly_coords_folium, color='white', weight=2.5, fill=False, tooltip=f"扩展边界 {plot_id_str} (簇 {cluster_id_for_plot})").add_to(polygon_layer)
            if plot_data['outer_polygon_coords_lonlat']:
                center_lon = np.mean([c[0] for c in plot_data['outer_polygon_coords_lonlat']]); center_lat = np.mean([c[1] for c in plot_data['outer_polygon_coords_lonlat']])
                folium.Marker(location=[center_lat, center_lon], icon=folium.DivIcon(html=f'<div style="font-size:16pt;color:{color};font-weight:bold;">{plot_id_str}</div>'), tooltip=f"地块 {plot_id_str}").add_to(label_layer)
        polygon_layer.add_to(map_obj); label_layer.add_to(map_obj)
        info_box_content_html = _create_info_box_html_content(plot_info_dict)
        info_box_full_html = f'''
    <div id="info-box" style="position: fixed;top: 80px;left: 20px;z-index: 1000;background: rgba(255, 255, 255, 0.92);backdrop-filter: blur(8px);-webkit-backdrop-filter: blur(8px);padding: 20px 24px;border-radius: 16px;box-shadow: 0 8px 24px rgba(0,0,0,0.15);max-height: 80vh;overflow-y: auto;font-family: 'Helvetica Neue', 'Segoe UI', 'Roboto', sans-serif;font-size: 14px;line-height: 1.6;color: #333;min-width: 320px;border: 1px solid rgba(0,0,0,0.08);">
        <h2 style="margin-top: 0; margin-bottom: 16px; font-size: 18px; font-weight: 700; display: flex; align-items: center;">
            <span style="font-size:20px; margin-right:8px;">🗂️</span>地块信息</h2>
        <div style="height:1px; background:#e0e0e0; margin:12px 0;"></div><div id="info-content">{info_box_content_html}</div></div>
    <button id="toggle-button" style="position: fixed;top: 30px;left: 20px;z-index: 1001;background: #ffffff;border: 1px solid rgba(0,0,0,0.15);border-radius: 50%;width: 36px;height: 36px;font-size: 20px;font-weight: bold;color: #333;cursor: pointer;box-shadow: 0 4px 12px rgba(0,0,0,0.2);display: flex;align-items: center;justify-content: center;transition: all 0.3s ease;">–</button>
    <script>var infoBox = document.getElementById("info-box"); var toggleButton = document.getElementById("toggle-button"); var expanded = true; toggleButton.onclick = function() {{ if (expanded) {{ infoBox.style.display = "none"; toggleButton.textContent = "+"; }} else {{ infoBox.style.display = "block"; toggleButton.textContent = "–"; }} expanded = !expanded; }};</script>'''
        map_obj.get_root().html.add_child(folium.Element(info_box_full_html))
        return f"✅ 地块可视化信息添加完成 ({len(plot_info_dict)} 个地块数据传入, 边界默认显示: {show_polygon}, 编号默认显示: {show_label})."
    def add_grid_cells_layer_to_map(map_obj, all_plots_info_dict: dict, grid_size_m: float, layer_name: str):
        if not all_plots_info_dict: return "ℹ️ 无地块信息，不添加栅格图层。"
        show_default = LAYER_DEFAULT_VISIBILITY.get(layer_name, True)
        grid_layer = folium.FeatureGroup(name=layer_name, show=show_default)
        total_grid_cells_drawn = 0
        sorted_plot_ids = sorted(all_plots_info_dict.keys(), key=lambda x: int(x))
        for plot_id_str in sorted_plot_ids:
            plot_data = all_plots_info_dict[plot_id_str]
            if not plot_data.get('is_valid', False) or not plot_data.get('grid_set_utm_indices') or not plot_data.get('utm_transformers'): continue
            transformer_to_lonlat = plot_data['utm_transformers'][1]
            if not transformer_to_lonlat: continue
            grid_indices_for_plot = plot_data['grid_set_utm_indices']
            for grid_x, grid_y in grid_indices_for_plot:
                x_min_utm = grid_x * grid_size_m; y_min_utm = grid_y * grid_size_m
                x_max_utm = x_min_utm + grid_size_m; y_max_utm = y_min_utm + grid_size_m
                corners_utm = [(x_min_utm, y_min_utm), (x_max_utm, y_min_utm), (x_max_utm, y_max_utm), (x_min_utm, y_max_utm)]
                try:
                    corners_lonlat = [transformer_to_lonlat.transform(x,y) for x,y in corners_utm]
                    polygon_locs_latlon = [(lat, lon) for lon, lat in corners_lonlat]
                    folium.Polygon(locations=polygon_locs_latlon, color='green', weight=0.5, fill=True, fill_color='green', fill_opacity=0.15).add_to(grid_layer)
                    total_grid_cells_drawn +=1
                except Exception: continue
        grid_layer.add_to(map_obj)
        return f"✅ 添加图层 '{layer_name}' ({total_grid_cells_drawn} 个栅格单元, 默认显示: {show_default})."
    def add_grid_source_trajectory_layer_to_map(map_obj, all_plots_info_dict: dict, original_gps_data_df_cleaned: pd.DataFrame, layer_name: str):
        if not all_plots_info_dict or original_gps_data_df_cleaned.empty: return f"ℹ️ 无地块信息或原始GPS数据，不添加 '{layer_name}' 图层。"
        show_default = LAYER_DEFAULT_VISIBILITY.get(layer_name, True)
        source_traj_layer = folium.FeatureGroup(name=layer_name, show=show_default)
        points_added_to_layer = 0
        from shapely.geometry import Point as ShapelyPoint
        from shapely.prepared import prep
        sorted_plot_ids = sorted(all_plots_info_dict.keys(), key=lambda x: int(x))
        for plot_id_str in sorted_plot_ids:
            plot_data = all_plots_info_dict[plot_id_str]
            if not plot_data.get('is_valid', False) or not plot_data.get('outer_polygon_coords_lonlat'): continue
            outer_polygon_shape = Polygon(plot_data['outer_polygon_coords_lonlat'])
            if not outer_polygon_shape.is_valid: continue
            prepared_polygon = prep(outer_polygon_shape)
            lon_idx = original_gps_data_df_cleaned.columns.get_loc('longitude') + 1
            lat_idx = original_gps_data_df_cleaned.columns.get_loc('latitude') + 1
            relevant_indices = [row_tuple[0] for row_tuple in original_gps_data_df_cleaned.itertuples(index=True) if prepared_polygon.contains(ShapelyPoint(row_tuple[lon_idx], row_tuple[lat_idx]))]
            if not relevant_indices: continue
            relevant_original_points_for_plot_df = original_gps_data_df_cleaned.loc[relevant_indices].copy().sort_values(by='time')
            if len(relevant_original_points_for_plot_df) < 2: continue
            path_coords_latlon = relevant_original_points_for_plot_df[['latitude', 'longitude']].values.tolist()
            plot_color = PLOT_COLORS[(int(plot_id_str) - 1) % len(PLOT_COLORS)]
            folium.PolyLine(locations=path_coords_latlon, color=plot_color, weight=2, opacity=0.7, tooltip=f"栅格计算源轨迹 - 地块 {plot_id_str} (簇 {plot_data['cluster_id']})").add_to(source_traj_layer)
            points_added_to_layer += len(path_coords_latlon)
        source_traj_layer.add_to(map_obj)
        return f"✅ 添加图层 '{layer_name}' (基于 {points_added_to_layer} 个原始点构成的轨迹段, 默认显示: {show_default})."
    def add_internal_sampling_points_layer_to_map(map_obj, all_plots_info_dict: dict, layer_name: str):
        if not all_plots_info_dict: return f"ℹ️ 无地块信息，不添加 '{layer_name}' 图层。"
        show_default = LAYER_DEFAULT_VISIBILITY.get(layer_name, True)
        sampling_points_layer = folium.FeatureGroup(name=layer_name, show=show_default)
        total_sampling_points_drawn = 0
        sorted_plot_ids = sorted(all_plots_info_dict.keys(), key=lambda x: int(x))
        for plot_id_str in sorted_plot_ids:
            plot_data = all_plots_info_dict[plot_id_str]
            if not plot_data.get('is_valid', False) or not plot_data.get('internal_sampling_points_utm') or not plot_data.get('utm_transformers'): continue
            transformer_to_lonlat = plot_data['utm_transformers'][1]
            if not transformer_to_lonlat: continue
            sampling_points_utm_for_plot = plot_data['internal_sampling_points_utm']
            plot_color = PLOT_COLORS[(int(plot_id_str) - 1) % len(PLOT_COLORS)]
            for utm_x, utm_y in sampling_points_utm_for_plot:
                try:
                    lon, lat = transformer_to_lonlat.transform(utm_x, utm_y)
                    folium.CircleMarker(location=[lat, lon], radius=0.5, color=plot_color, fill=True, fill_color=plot_color, fill_opacity=0.8, tooltip=f"采样点 (地块 {plot_id_str})").add_to(sampling_points_layer)
                    total_sampling_points_drawn +=1
                except Exception: continue
        sampling_points_layer.add_to(map_obj)
        return f"✅ 添加图层 '{layer_name}' ({total_sampling_points_drawn} 个内部采样点, 默认显示: {show_default})."
def ensure_output_dirs():
    json_dir = os.path.join(OUTPUT_DIR_BASE, FINAL_OUTPUT_SUBDIR_JSON)
    maps_dir = os.path.join(OUTPUT_DIR_BASE, TEST_OUTPUT_SUBDIR_MAPS)
    os.makedirs(json_dir, exist_ok=True)
    if GENERATE_HTML_MAPS: os.makedirs(maps_dir, exist_ok=True)
    return json_dir, maps_dir
def process_single_file(input_filepath: str):
    file_basename = os.path.basename(input_filepath)
    output_prefix = os.path.splitext(file_basename)[0]
    current_timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    json_output_dir, map_output_dir = ensure_output_dirs()
    start_time_file = time.time(); gc.collect()
    raw_data_df, msg = load_trajectory_data(input_filepath); print(msg)
    if raw_data_df is None: return False, f"{file_basename}: 数据加载失败。"
    data_with_speed_df, msg = calculate_trajectory_metrics(raw_data_df); print(msg)
    cleaned_data_for_path_df, msg = filter_trajectory_by_speed(data_with_speed_df, SPEED_FILTER_MIN_KMH,SPEED_FILTER_MAX_KMH);print(msg)
    if cleaned_data_for_path_df.empty: print(f"❌ {file_basename}: 速度清洗后无数据，处理终止。"); return False, f"{file_basename}: 速度清洗后无数据。"
    raw_traj_lat = cleaned_data_for_path_df['latitude'].values; raw_traj_lon = cleaned_data_for_path_df['longitude'].values
    raw_traj_dists = calculate_haversine_distances_vectorized(raw_traj_lat[:-1], raw_traj_lon[:-1], raw_traj_lat[1:],raw_traj_lon[1:], EARTH_RADIUS_KM)
    original_cleaned_path_len_m = np.sum(raw_traj_dists) * 1000
    print(f"📏 清洗后原始轨迹总长度: {original_cleaned_path_len_m:.1f} 米")
    interpolated_df, _, msg_interp = interpolate_trajectory_dynamically(cleaned_data_for_path_df.copy())
    if interpolated_df.empty: print(f"❌ {file_basename}: 人造轨迹点生成失败，处理终止。"); return False, f"{file_basename}: 人造轨迹点生成失败。"
    print(msg_interp)
    clustered_interpolated_df, msg_dbscan = cluster_trajectory_points_dbscan(interpolated_df, DBSCAN_EPS_METERS,DBSCAN_MIN_SAMPLES)
    print(msg_dbscan)
    final_clustered_df, valid_cluster_ids, log_msgs_filter_trim = filter_and_trim_clusters(clustered_interpolated_df,EFFECTIVE_MIN_CLUSTER_SIZE_POINTS,CLUSTER_TRIMMING_POINTS_COUNT)
    for log_msg in log_msgs_filter_trim: print(log_msg)
    map_center_lat, map_center_lon = None, None
    if GENERATE_HTML_MAPS:
        map_center_lat, map_center_lon = DEFAULT_MAP_CENTER_LAT, DEFAULT_MAP_CENTER_LON
        df_for_map_center = None
        if valid_cluster_ids and not final_clustered_df[final_clustered_df['cluster'].isin(valid_cluster_ids)].empty: df_for_map_center = final_clustered_df[final_clustered_df['cluster'].isin(valid_cluster_ids)]
        elif not final_clustered_df.empty: df_for_map_center = final_clustered_df
        elif not cleaned_data_for_path_df.empty: df_for_map_center = cleaned_data_for_path_df
        elif not raw_data_df.empty: df_for_map_center = raw_data_df
        if df_for_map_center is not None and not df_for_map_center.empty :
            map_center_lat_calc, map_center_lon_calc, _ = calculate_dataframe_centroid(df_for_map_center)
            if map_center_lat_calc is not None and map_center_lon_calc is not None:
                map_center_lat, map_center_lon = map_center_lat_calc, map_center_lon_calc
    all_plots_info_dict, status_msg_analysis, log_msgs_analysis = {}, "ℹ️ 无有效簇进行地块分析。", []
    if valid_cluster_ids:
        all_plots_info_dict, status_msg_analysis, log_msgs_analysis = analyze_plot_candidates_from_clusters(final_clustered_df, cleaned_data_for_path_df.copy(), valid_cluster_ids)
    for log_msg in log_msgs_analysis: print(log_msg)
    print(status_msg_analysis)
    plot_summary_json = generate_plot_summary_json_structure(all_plots_info_dict)
    summary_json_filename = os.path.join(json_output_dir, f"{output_prefix}_summary_{current_timestamp}.json")
    with open(summary_json_filename, 'w', encoding='utf-8') as f: json.dump(plot_summary_json, f, separators=(',', ':'))
    plot_boundaries_json = generate_plot_boundaries_json_structure(all_plots_info_dict)
    boundary_json_filename = os.path.join(json_output_dir, f"{output_prefix}_boundaries_{current_timestamp}.json")
    with open(boundary_json_filename, 'w', encoding='utf-8') as f: json.dump(plot_boundaries_json, f, separators=(',', ':'))
    if GENERATE_HTML_MAPS:
        folium_map, _ = create_folium_base_map(map_center_lat, map_center_lon, DEFAULT_MAP_ZOOM)
        add_raw_trajectory_antpath_to_map(folium_map, raw_data_df, "原始轨迹动画 (全部)")
        add_points_layer_to_map(folium_map, interpolated_df, "人造轨迹点 (插值)", color='#FF6600', radius=1)
        add_clustered_points_layers_to_map(folium_map, final_clustered_df, valid_cluster_ids, "作业点 (按簇)", "行驶/噪声点 (插值)")
        add_plots_visualization_to_map(folium_map, all_plots_info_dict)
        add_grid_cells_layer_to_map(folium_map, all_plots_info_dict, GRID_SIZE_METERS, "轨迹覆盖栅格 (所有有效)")
        add_grid_source_trajectory_layer_to_map(folium_map, all_plots_info_dict, cleaned_data_for_path_df.copy(), "栅格计算依据轨迹")
        add_internal_sampling_points_layer_to_map(folium_map, all_plots_info_dict, "栅格计算采样点")
        folium.LayerControl().add_to(folium_map)
        map_filename = os.path.join(map_output_dir, f"{output_prefix}_map_{current_timestamp}.html")
        folium_map.save(map_filename)
    end_time_file = time.time()
    print(f"总耗时: {end_time_file - start_time_file:.2f} 秒")
    total_points_interpolated = len(interpolated_df)
    final_work_points_count = len(final_clustered_df[final_clustered_df['cluster'] != -1]) if 'cluster' in final_clustered_df else 0
    num_valid_plots = len([p for p_id, p in all_plots_info_dict.items() if p.get('is_valid', False)])
    print(f"插值点总数: {total_points_interpolated}")
    print(f"最终有效作业点数 (插值点): {final_work_points_count}")
    print(f"识别出的有效作业地块数量: {num_valid_plots}")
    if 'time' in raw_data_df.columns and len(raw_data_df) > 1:
        try:
            sorted_times = raw_data_df['time'].dropna().sort_values()
            if len(sorted_times) >= 2: total_duration_hours = (sorted_times.iloc[-1] - sorted_times.iloc[0]).total_seconds() / 3600; print(f"原始数据总时长: {total_duration_hours:.3f} 小时")
        except Exception as e_dur: print(f"计算原始数据时长出错: {e_dur}")
    print()
    return True, f"{file_basename}: 处理成功。"
if __name__ == '__main__':
    json_input_dir = r"/作业状态识别/Json"
    print(f"📂 将从目录 {json_input_dir} 读取JSON文件")
    file_paths_to_process = [os.path.join(json_input_dir, f) for f in os.listdir(json_input_dir) if f.endswith('.json')]
    if not file_paths_to_process: print("🚫 未找到待处理的JSON文件。"); exit()
    print(f"📋 计划处理 {len(file_paths_to_process)} 个文件:")
    for file_path in file_paths_to_process: print(f"  - {os.path.basename(file_path)}")
    overall_start_time = time.time(); results_summary = []
    print(f"⚙️ 将串行处理文件。地图生成状态: {'启用' if GENERATE_HTML_MAPS else '禁用'}")
    for fp in file_paths_to_process:
        try:
            success, message = process_single_file(fp)
            results_summary.append({'success': success, 'message': message})
        except Exception as e_single_file:
            results_summary.append({'success': False, 'message': f"处理文件 {os.path.basename(fp)} 时发生严重错误: {e_single_file}"})
            traceback.print_exc()
    successful_files = sum(1 for r in results_summary if r['success'])
    failed_files = len(results_summary) - successful_files
    overall_end_time = time.time()
    print(f"\n🏁 所有文件处理完成! 总耗时: {overall_end_time - overall_start_time:.2f} 秒")
    print(f"🟢 成功: {successful_files} 个文件\n🔴 失败: {failed_files} 个文件")
    if failed_files > 0:
        print("\n详细失败信息:")
        for r in results_summary:
            if not r['success']: print(f"  - {r['message']}")