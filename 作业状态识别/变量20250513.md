# 阈值参数整理

## 1. 速度筛选下限: `speed_filter_min`
*   位置: 在 `main` 函数中定义
*   源代码: `speed_filter_min = 1;`
*   作用: 低于此速度的点被过滤。

## 2. 速度筛选上限: `speed_filter_max`
*   位置: 在 `main` 函数中定义
*   源代码: `speed_filter_max = 20`
*   作用: 高于此速度的点被过滤。

## 3. 轨迹插值采样间隔: `trajectory_sampling_interval_m`
*   位置: 在 `main` 函数中定义
*   源代码: `trajectory_sampling_interval_m = 10.0`
*   作用: 生成人造轨迹点的密度。

## 4. DBSCAN聚类邻域半径: `dbscan_eps_meters`
*   位置: 在 `main` 函数中定义
*   源代码: `dbscan_eps_meters = 15`
*   作用: 聚类时点的搜索范围。

## 5. DBSCAN聚类最小样本数: `dbscan_min_samples`
*   位置: 在 `main` 函数中定义
*   源代码: `dbscan_min_samples = 9`
*   作用: 形成簇的最小点数。

## 6. 三角剖分最大边长: `max_edge_length_m`
*   位置 (定义): 在 `main` 函数中
*   源代码 (定义): `max_edge_length_m = 20`
*   位置 (使用): `valid_triangle` 函数中作为参数 `max_length`
*   源代码 (使用): `if haversine(point1[0], point1[1], point2[0], point2[1]) > max_length:`
*   作用: 构建地块多边形时允许的三角形最大边长。

## 7. 地块边界扩展距离: `border_expansion_m`
*   位置: 在 `main` 函数中定义
*   源代码: `border_expansion_m = grid_size_meters/2`
*   作用: 地块边界向外扩展的距离，为作业幅宽的一半。

## 8. 有效簇最小点数: `effective_min_cluster_size`
*   位置: 在 `main` 函数中定义
*   源代码: `effective_min_cluster_size = 100`
*   作用: 簇被视为有效的最小（插值后）点数。

## 9. 绘制多边形所需最少点数: `effective_points_threshold_for_polygon`
*   位置: 在 `main` 函数中定义
*   源代码: `effective_points_threshold_for_polygon = 100`
*   作用: 绘制地块多边形所需的最少（插值后）点数。

## 10. 作业幅宽/栅格大小: `grid_size_meters`
*   位置: 在 `main` 函数中定义
*   源代码: `grid_size_meters = 2.3`
*   作用: 计算轨迹覆盖面积的栅格大小。

## 11. 头尾点剔除判断点数: `30` (硬编码值)
*   位置 1: `get_cluster_polygon_implementation` 函数中
*   源代码 1: `if len(points_array) > 30:`
*   位置 2: `add_polygons_and_labels` 函数中 (处理 `traj_points_array` 时)
*   源代码 2: `if len(traj_points_array) > 30:`
*   作用: 点集总数大于此值才执行头尾点剔除。

## 12. 头尾剔除各端点数: `15` (硬编码值)
*   位置 1: `get_cluster_polygon_implementation` 函数中
*   源代码 1: `points_array = points_array[15:-15]`
*   位置 2: `add_polygons_and_labels` 函数中 (处理 `traj_points_array` 时)
*   源代码 2: `traj_points_array = traj_points_array[15:-15]`
*   作用: 从点集头部和尾部分别剔除的点数。

## 13. 每亩轨迹长度筛选上限: `1000` (硬编码值)
*   位置: `add_polygons_and_labels` 函数中
*   源代码: `if per_mu_track_length > 1000 or coverage_area_m2_dedup < 400:` (关注 `per_mu_track_length > 1000` 部分)
*   作用: 每亩地块轨迹长度超过此值则排除该地块。

## 14. 地块最小面积筛选值: `400` (硬编码值)
*   位置: `add_polygons_and_labels` 函数中
*   源代码: `if per_mu_track_length > 1000 or coverage_area_m2_dedup < 400:` (关注 `coverage_area_m2_dedup < 400` 部分)
*   作用: 轨迹去重面积(平方米)小于此值则排除该地块。

## 15. Delaunay三角剖分坐标扰动幅度: `noise_magnitude_degrees`
*   位置: 在 `perform_delaunay_triangulation_implementation` 函数中定义
*   源代码: `noise_magnitude_degrees = 5e-6`
*   作用: 防止三角剖分因共线点失败。

## 16. 插值点最小间距: `1e-3` (硬编码值)
*   位置: `generate_uniformly_spaced_points_on_path` 函数中
*   源代码 1: `if not path_points_list or haversine(inter_lon, inter_lat, path_points_list[-1]['longitude'], path_points_list[-1]['latitude']) > 1e-3:`
*   源代码 2: `if dist_to_last_orig > 1e-3 and accumulated_distance_along_path >= next_sample_distance_target - sampling_interval_m:`
*   作用: 生成插值点时避免与上一个点距离过小。

## 17. 速度计算最小时间差: `1e-9` (硬编码值)
*   位置: `calculate_speed_distance_implementation` 函数中
*   源代码: `time_deltas_hours[time_deltas_hours < 1e-9] = np.nan`
*   作用: 避免计算速度时因时间差过小导致除零错误。

## 18. 天地图API密钥: `your_tianditu_key`
*   位置: 在 `create_base_map_implementation` 函数中定义
*   源代码: `your_tianditu_key = "e7f645439e09d5fdbc5158bacab6d024"`
*   作用: 用于加载天地图的地图瓦片。

## 19. 工作时间计算阈值: `time_threshold_seconds` (新增)
*   位置 1: `add_polygons_and_labels` 函数中
*   源代码 1: `time_threshold_seconds = 60`
*   位置 2: `calculate_working_time` 函数中 (如果存在)
*   作用: 计算工作时间时，超过此阈值的时间间隔被视为非工作时间并排除。这种累加法计算可以更准确地反映实际工作时间，排除停机等待等情况。
