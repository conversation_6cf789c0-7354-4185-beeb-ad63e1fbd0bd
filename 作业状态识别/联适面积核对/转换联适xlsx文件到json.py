# 如果你还没有安装 openpyxl，请先运行以下命令安装：
# pip install openpyxl

import pandas as pd
import json

# 定义Excel文件的路径
excel_file_path = '12304073081.xlsx'

# 读取Excel文件中的所有工作表
data = pd.read_excel(excel_file_path, sheet_name=None)

# 选择需要处理的工作表
sheet_data = data['pos']

# 将列名转换为与JSON文件一致的格式
transformed_data = sheet_data.rename(columns={
    '时间': 'time',
    '经度': 'longitude',
    '纬度': 'latitude'
})

# 选择需要的列
transformed_data = transformed_data[['latitude', 'longitude', 'time']]

# 将DataFrame转换为字典列表
json_like_data = transformed_data.to_dict(orient='records')

# 定义输出JSON文件的路径
output_file_path = '12304073081transformed_data.json'

# 将数据保存为JSON文件
with open(output_file_path, 'w') as json_file:
    json.dump(json_like_data, json_file, ensure_ascii=False, indent=4)

# 输出文件路径
print(f"JSON文件已保存至: {output_file_path}")
