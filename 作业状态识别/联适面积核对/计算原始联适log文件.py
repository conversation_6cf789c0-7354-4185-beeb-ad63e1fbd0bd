import re
from datetime import datetime
import folium
from folium.plugins import AntPath, HeatMap, MarkerCluster, MeasureControl
from math import radians, cos, sin, sqrt, atan2


# 解析 NMEA 句子
def parse_nmea_sentence(sentence):
    match = re.match(r'\$(\w+),(.*)\*(\w+)', sentence)
    if not match:
        return None
    sentence_type = match.group(1)
    fields = match.group(2).split(',')
    return sentence_type, fields


# 将度分格式转换为十进制度
def convert_to_decimal_degrees(degree_minute, direction):
    if not degree_minute or len(degree_minute) < 4:  # 检查是否为空和长度
        return None
    try:
        if direction in ['N', 'S']:  # 纬度格式
            degrees = int(degree_minute[:2])
            minutes = float(degree_minute[2:])
        elif direction in ['E', 'W']:  # 经度格式
            degrees = int(degree_minute[:3])
            minutes = float(degree_minute[3:])
        else:
            return None
    except ValueError:
        return None
    decimal_degrees = degrees + minutes / 60
    if direction in ['S', 'W']:
        decimal_degrees *= -1
    return decimal_degrees


# 计算两点间的距离（哈弗辛公式）
def haversine(lat1, lon1, lat2, lon2):
    R = 6371.0  # 地球半径，单位千米
    dlat = radians(lat2 - lat1)
    dlon = radians(lon2 - lon1)
    a = sin(dlat / 2) ** 2 + cos(radians(lat1)) * cos(radians(lat2)) * sin(dlon / 2) ** 2
    c = 2 * atan2(sqrt(a), sqrt(1 - a))
    distance = R * c
    return distance


# 从日志文件中提取 GPS 数据
def extract_gps_data_from_log(file_path):
    gps_data = []
    with open(file_path, 'r', encoding='utf-8') as file:  # 添加编码参数
        for line in file:
            parsed = parse_nmea_sentence(line.strip())
            if not parsed:
                continue
            sentence_type, fields = parsed
            if sentence_type == 'GNRMC' and len(fields) >= 10:
                time_utc = fields[0]
                latitude = convert_to_decimal_degrees(fields[2], fields[3])
                longitude = convert_to_decimal_degrees(fields[4], fields[5])

                # 检查速度字段是否有效
                if fields[6] == '' or not re.match(r'^\d+(\.\d+)?$', fields[6]):
                    speed = 0.0
                else:
                    speed = float(fields[6]) * 1.852  # 将速度从节转换为公里/小时

                date_utc = fields[8]

                # 检查日期字段是否符合预期格式
                if not re.match(r'\d{6}', date_utc):
                    continue

                # 检查字段是否有效
                if not time_utc or latitude is None or longitude is None or not date_utc:
                    continue

                try:
                    datetime_obj = datetime.strptime(date_utc + time_utc, '%d%m%y%H%M%S.%f')
                    gps_data.append((latitude, longitude, speed, datetime_obj))
                except ValueError as e:
                    continue
    return gps_data


# 计算统计数据
def calculate_statistics(gps_data):
    total_distance = 0.0
    total_time = 0.0
    num_points = len(gps_data)
    max_speed_kmh = 0.0
    points_in_range = 0

    # 初始化速度区间计数器
    speed_intervals = {
        '0-1 km/h': 0,
        '1-2 km/h': 0,
        '2-3 km/h': 0,
        '3-4 km/h': 0,
        '4-5 km/h': 0,
        '5-10 km/h': 0,
        '10-15 km/h': 0,
        '15-20 km/h': 0,
        '20-25 km/h': 0,
        '25+ km/h': 0
    }

    for i in range(1, num_points):
        lat1, lon1, speed1, time1 = gps_data[i - 1]
        lat2, lon2, speed2, time2 = gps_data[i]
        distance = haversine(lat1, lon1, lat2, lon2)
        total_distance += distance
        time_diff = (time2 - time1).total_seconds() / 3600.0  # 转换为小时
        total_time += time_diff

        if speed2 > max_speed_kmh:
            max_speed_kmh = speed2

        # 统计速度区间
        if 0 <= speed2 < 1:
            speed_intervals['0-1 km/h'] += 1
        elif 1 <= speed2 < 2:
            speed_intervals['1-2 km/h'] += 1
        elif 2 <= speed2 < 3:
            speed_intervals['2-3 km/h'] += 1
        elif 3 <= speed2 < 4:
            speed_intervals['3-4 km/h'] += 1
        elif 4 <= speed2 < 5:
            speed_intervals['4-5 km/h'] += 1
        elif 5 <= speed2 < 10:
            speed_intervals['5-10 km/h'] += 1
        elif 10 <= speed2 < 15:
            speed_intervals['10-15 km/h'] += 1
        elif 15 <= speed2 < 20:
            speed_intervals['15-20 km/h'] += 1
        elif 20 <= speed2 < 25:
            speed_intervals['20-25 km/h'] += 1
        else:
            speed_intervals['25+ km/h'] += 1

    if total_time > 0:
        average_speed_kmh_direct = total_distance / total_time
    else:
        average_speed_kmh_direct = 0

    if num_points > 1:
        average_point_interval_km = total_distance / (num_points - 1)
    else:
        average_point_interval_km = 0

    # 使用每100个坐标点计算的平均速度
    interval_speeds = []
    for i in range(0, num_points, 100):
        if i + 100 < num_points:
            lat1, lon1, _, time1 = gps_data[i]
            lat2, lon2, _, time2 = gps_data[i + 100]
            distance = haversine(lat1, lon1, lat2, lon2)
            time_diff = (time2 - time1).total_seconds() / 3600.0  # 转换为小时
            if time_diff > 0:
                speed = distance / time_diff
                interval_speeds.append(speed)

    if interval_speeds:
        average_speed_kmh_interval = sum(interval_speeds) / len(interval_speeds)
    else:
        average_speed_kmh_interval = 0

    return {
        'total_distance_km': total_distance,
        'average_speed_kmh_direct': average_speed_kmh_direct,
        'average_speed_kmh_interval': average_speed_kmh_interval,  # 每100个坐标点计算的平均速度
        'total_time_hours': total_time,
        'average_point_interval_km': average_point_interval_km,
        'num_points': num_points,
        'max_speed_kmh': max_speed_kmh,  # 最大速度（公里/小时）
        'speed_intervals': speed_intervals  # 速度区间内的坐标点数量
    }

# 格式化统计结果为中文
def format_statistics(statistics):
    total_distance_km = statistics['total_distance_km']
    average_point_interval_km = statistics['average_point_interval_km']

    # 总距离
    if total_distance_km < 1:
        total_distance_str = f"{total_distance_km * 1000:.2f} 米"
    else:
        total_distance_str = f"{total_distance_km:.2f} 公里"

    # 平均点间隔
    if average_point_interval_km < 1:
        average_point_interval_str = f"{average_point_interval_km * 1000:.4f} 米"
    else:
        average_point_interval_str = f"{average_point_interval_km:.5f} 公里"

    # 速度区间分布
    speed_intervals_str = "\n".join([f"{k}: {v} 个点" for k, v in statistics['speed_intervals'].items()])

    formatted_stats = (
        f"总距离: {total_distance_str}\n"
        f"平均速度（直接取值）: {statistics['average_speed_kmh_direct']:.2f} 公里/小时\n"
        f"平均速度（每100个点计算）: {statistics['average_speed_kmh_interval']:.2f} 公里/小时\n"
        f"总时长: {statistics['total_time_hours']:.2f} 小时\n"
        f"平均点间隔: {average_point_interval_str}\n"
        f"坐标点数量: {statistics['num_points']}\n"
        f"最大速度: {statistics['max_speed_kmh']:.2f} 公里/小时\n"
        f"速度区间分布:\n{speed_intervals_str}"
    )
    return formatted_stats


# 创建地图并绘制轨迹
def create_map_with_trajectory(coordinates, output_file_path):
    if not coordinates:
        print("No coordinates to plot.")
        return

    # 创建一个地图对象
    m = folium.Map(location=coordinates[0], zoom_start=12)

    # 添加 Google 瓦片图层
    folium.TileLayer(
        tiles='http://{s}.google.com/vt/lyrs=s&x={x}&y={y}&z={z}',
        attr='Google',
        name='Google卫星影像',
        overlay=True,
        control=True,
        subdomains=['mt0', 'mt1', 'mt2', 'mt3']
    ).add_to(m)

    # 添加轨迹
    AntPath(locations=coordinates, color='blue', weight=3, opacity=0.7, delay=30000, pulse_color='#F0F0F0').add_to(m)

    # 添加测面积的控件
    m.add_child(MeasureControl(primary_length_unit='kilometers', primary_area_unit='sqmeters'))

    # 添加热力图
    # HeatMap(coordinates).add_to(m)

    # # 添加聚合控件
    # marker_cluster = MarkerCluster().add_to(m)
    # for coord in coordinates:
    #     folium.Marker(location=coord).add_to(marker_cluster)

    # 保存地图到文件
    m.save(output_file_path)
    print(f"Map has been saved to {output_file_path}")


# 文件路径
log_file_path = '2024-01-05 15_21_34.log'
output_map_path = 'gps_trajectory_map_with_all_features.html'

# 从 log 文件中提取 GPS 数据
gps_data = extract_gps_data_from_log(log_file_path)

# 计算统计数据
statistics = calculate_statistics(gps_data)

# 格式化统计结果
formatted_stats = format_statistics(statistics)
print(formatted_stats)

# 创建并保存轨迹地图
coordinates = [(data[0], data[1]) for data in gps_data]
create_map_with_trajectory(coordinates, output_map_path)
