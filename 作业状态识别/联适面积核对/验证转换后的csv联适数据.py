import pandas as pd
from math import radians, cos, sin, sqrt, atan2
from datetime import datetime


# 计算两点间的距离（哈弗辛公式）
def haversine(lat1, lon1, lat2, lon2):
    R = 6371.0  # 地球半径，单位千米
    dlat = radians(lat2 - lat1)
    dlon = radians(lon1 - lon2)  # 修正此处经度差值方向
    a = sin(dlat / 2) ** 2 + cos(radians(lat1)) * cos(radians(lat2)) * sin(dlon / 2) ** 2
    c = 2 * atan2(sqrt(a), sqrt(1 - a))
    distance = R * c
    return distance


# 从 CSV 文件中提取 GPS 数据
def extract_gps_data_from_csv(file_path):
    df = pd.read_csv(file_path)
    gps_data = []
    for _, row in df.iterrows():
        latitude = row['latitude']
        longitude = row['longitude']
        datetime_obj = datetime.strptime(row['time'], '%Y-%m-%d %H:%M:%S')
        gps_data.append((latitude, longitude, datetime_obj))
    return gps_data


# 计算统计数据
def calculate_statistics(gps_data):
    total_distance = 0.0
    total_time = 0.0
    num_points = len(gps_data)
    total_speed_knots = 0.0
    max_speed_knots = 0.0
    points_in_range = 0

    # 初始化速度区间计数器
    speed_intervals = {
        '0-1 km/h': 0,
        '1-2 km/h': 0,
        '2-3 km/h': 0,
        '3-4 km/h': 0,
        '4-5 km/h': 0,
        '5-10 km/h': 0,
        '10-15 km/h': 0,
        '15-20 km/h': 0,
        '20-25 km/h': 0,
        '25+ km/h': 0
    }

    for i in range(1, num_points):
        lat1, lon1, time1 = gps_data[i - 1]
        lat2, lon2, time2 = gps_data[i]
        distance = haversine(lat1, lon1, lat2, lon2)
        total_distance += distance
        time_diff = (time2 - time1).total_seconds() / 3600.0  # 转换为小时
        total_time += time_diff

        if time_diff > 0:
            speed = distance / time_diff  # km/h
        else:
            speed = 0.0

        total_speed_knots += speed / 1.852  # 将 km/h 转换为节

        if speed > max_speed_knots:
            max_speed_knots = speed / 1.852  # 将 km/h 转换为节

        if 2 <= distance <= 15:
            points_in_range += 1

        # 统计速度区间
        if 0 <= speed < 1:
            speed_intervals['0-1 km/h'] += 1
        elif 1 <= speed < 2:
            speed_intervals['1-2 km/h'] += 1
        elif 2 <= speed < 3:
            speed_intervals['2-3 km/h'] += 1
        elif 3 <= speed < 4:
            speed_intervals['3-4 km/h'] += 1
        elif 4 <= speed < 5:
            speed_intervals['4-5 km/h'] += 1
        elif 5 <= speed < 10:
            speed_intervals['5-10 km/h'] += 1
        elif 10 <= speed < 15:
            speed_intervals['10-15 km/h'] += 1
        elif 15 <= speed < 20:
            speed_intervals['15-20 km/h'] += 1
        elif 20 <= speed < 25:
            speed_intervals['20-25 km/h'] += 1
        else:
            speed_intervals['25+ km/h'] += 1

    if total_time > 0:
        average_speed_kmh = total_distance / total_time
    else:
        average_speed_kmh = 0

    if num_points > 1:
        average_point_interval = total_distance / (num_points - 1)
    else:
        average_point_interval = 0

    average_speed_knots = total_speed_knots / num_points  # 直接用速度字段的平均值
    average_speed_kmh_direct = average_speed_knots * 1.852  # 将平均速度从节转换为公里/小时
    max_speed_kmh = max_speed_knots * 1.852  # 将最大速度从节转换为公里/小时

    return {
        'total_distance_km': total_distance,
        'average_speed_kmh': average_speed_kmh,
        'total_time_hours': total_time,
        'average_point_interval_km': average_point_interval,
        'num_points': num_points,
        'average_speed_kmh_direct': average_speed_kmh_direct,  # 直接用速度字段计算的平均速度（公里/小时）
        'max_speed_kmh': max_speed_kmh,  # 最大速度（公里/小时）
        'points_in_range': points_in_range,  # 距离在2到15公里之间的坐标点数量
        'speed_intervals': speed_intervals  # 速度区间内的坐标点数量
    }


# 格式化统计结果为中文
def format_statistics(statistics):
    total_distance_km = statistics['total_distance_km']
    average_point_interval_km = statistics['average_point_interval_km']

    # 总距离
    if total_distance_km < 1:
        total_distance_str = f"{total_distance_km * 1000:.2f} 米"
    else:
        total_distance_str = f"{total_distance_km:.2f} 公里"

    # 平均点间隔
    if average_point_interval_km < 1:
        average_point_interval_str = f"{average_point_interval_km * 1000:.2f} 米"
    else:
        average_point_interval_str = f"{average_point_interval_km:.5f} 公里"

    # 速度区间分布
    speed_intervals_str = "\n".join([f"{k}: {v} 个点" for k, v in statistics['speed_intervals'].items()])

    formatted_stats = (
        f"总距离: {total_distance_str}\n"
        f"平均速度: {statistics['average_speed_kmh']:.2f} 公里/小时\n"
        f"总时长: {statistics['total_time_hours']:.2f} 小时\n"
        f"平均点间隔: {average_point_interval_str}\n"
        f"坐标点数量: {statistics['num_points']}\n"
        f"平均速度（直接计算）: {statistics['average_speed_kmh_direct']:.2f} 公里/小时\n"
        f"最大速度: {statistics['max_speed_kmh']:.2f} 公里/小时\n"
        f"距离在2到15公里之间的坐标点数量: {statistics['points_in_range']}\n"
        f"速度区间分布:\n{speed_intervals_str}"
    )
    return formatted_stats


# 文件路径
csv_file_path = 'extracted_gps_data100.csv'

# 从 CSV 文件中提取 GPS 数据
gps_data = extract_gps_data_from_csv(csv_file_path)

# 计算统计数据
statistics = calculate_statistics(gps_data)

# 格式化统计结果
formatted_stats = format_statistics(statistics)
print(formatted_stats)
