import re
from datetime import datetime
import folium
from folium.plugins import AntPath, MeasureControl, HeatMap, MarkerCluster

def parse_nmea_sentence(sentence):
    match = re.match(r'\$(\w+),(.*)\*(\w+)', sentence)
    if not match:
        return None
    sentence_type = match.group(1)
    fields = match.group(2).split(',')
    return sentence_type, fields

def convert_to_decimal_degrees(degree_minute, direction):
    if not degree_minute or len(degree_minute) < 4:  # 检查是否为空和长度
        return None
    try:
        if direction in ['N', 'S']:  # 纬度格式
            degrees = int(degree_minute[:2])
            minutes = float(degree_minute[2:])
        elif direction in ['E', 'W']:  # 经度格式
            degrees = int(degree_minute[:3])
            minutes = float(degree_minute[3:])
        else:
            return None
    except ValueError:
        return None
    decimal_degrees = degrees + minutes / 60
    if direction in ['S', 'W']:
        decimal_degrees *= -1
    return decimal_degrees

def extract_coordinates_from_log(file_path):
    coordinates = []
    with open(file_path, 'r', encoding='utf-8') as file:  # 指定编码格式为 UTF-8
        for line in file:
            parsed = parse_nmea_sentence(line.strip())
            if not parsed:
                continue
            sentence_type, fields = parsed
            if sentence_type == 'GNRMC' and len(fields) >= 10:
                latitude = convert_to_decimal_degrees(fields[2], fields[3])
                longitude = convert_to_decimal_degrees(fields[4], fields[5])

                # 检查字段是否有效
                if latitude is None or longitude is None:
                    continue

                coordinates.append((latitude, longitude))
    return coordinates

def create_map_with_trajectory(coordinates, output_file_path):
    if not coordinates:
        print("No coordinates to plot.")
        return

    # 创建一个地图对象
    m = folium.Map(location=coordinates[0], zoom_start=12)

    # 添加 Google 瓦片图层，设置 max_zoom 为 20
    folium.TileLayer(
        tiles='http://{s}.google.com/vt/lyrs=s&x={x}&y={y}&z={z}',
        attr='Google',
        name='Google卫星影像',
        overlay=True,
        control=True,
        subdomains=['mt0', 'mt1', 'mt2', 'mt3'],
        max_zoom=20  # 设置最大缩放级别为 20
    ).add_to(m)

    # 添加轨迹
    AntPath(locations=coordinates, color='blue', weight=2, opacity=0.7, delay=30000, pulse_color='#F0F0F0').add_to(m)
    # 添加测面积的控件
    m.add_child(MeasureControl(primary_length_unit='kilometers', primary_area_unit='sqmeters'))
    # # 添加热力图
    # HeatMap(coordinates).add_to(m)
    # # 添加聚合控件
    # marker_cluster = MarkerCluster().add_to(m)
    # for coord in coordinates:
    #     folium.Marker(location=coord).add_to(marker_cluster)
    # 保存地图到文件
    m.save(output_file_path)
    print(f"Map has been saved to {output_file_path}")

# 文件路径
log_file_path = '2024-01-05 15_21_34.log'
output_map_path = 'gps_trajectory_maplog.html'

# 从 log 文件中提取坐标数据
coordinates = extract_coordinates_from_log(log_file_path)

# 创建并保存轨迹地图
create_map_with_trajectory(coordinates, output_map_path)
