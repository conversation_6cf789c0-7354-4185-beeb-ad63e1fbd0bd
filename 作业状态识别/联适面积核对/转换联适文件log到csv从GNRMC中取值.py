import re
from datetime import datetime
import csv

# 解析 NMEA 句子
def parse_nmea_sentence(sentence):
    match = re.match(r'\$(\w+),(.*)\*(\w+)', sentence)
    if not match:
        return None
    sentence_type = match.group(1)
    fields = match.group(2).split(',')
    return sentence_type, fields

# 将度分格式转换为十进制度
def convert_to_decimal_degrees(degree_minute, direction):
    if not degree_minute or len(degree_minute) < 4:
        return None
    try:
        if direction in ['N', 'S']:
            degrees = int(degree_minute[:2])
            minutes = float(degree_minute[2:])
        elif direction in ['E', 'W']:
            degrees = int(degree_minute[:3])
            minutes = float(degree_minute[3:])
        else:
            return None
    except ValueError:
        return None
    decimal_degrees = degrees + minutes / 60
    if direction in ['S', 'W']:
        decimal_degrees *= -1
    return decimal_degrees

# 从日志文件中提取 GPS 数据
def extract_gps_data_from_log(file_path):
    extracted_data = []
    with open(file_path, 'r', encoding='utf-8') as file:  # 添加编码参数
        for line in file:
            parsed = parse_nmea_sentence(line.strip())
            if not parsed:
                continue
            sentence_type, fields = parsed
            if sentence_type == 'GNRMC' and len(fields) >= 10:
                time_utc = fields[0]
                latitude = convert_to_decimal_degrees(fields[2], fields[3])
                longitude = convert_to_decimal_degrees(fields[4], fields[5])
                date_utc = fields[8]
                if not time_utc or latitude is None or longitude is None or not date_utc:
                    continue
                try:
                    datetime_obj = datetime.strptime(date_utc + time_utc, '%d%m%y%H%M%S.%f')
                    formatted_time = datetime_obj.strftime('%Y-%m-%d %H:%M:%S')
                    extracted_data.append((latitude, longitude, formatted_time))
                except ValueError as e:
                    print(f"Error parsing date and time: {e}")
                    continue
    return extracted_data

# 写入 CSV 文件
def write_to_csv(data, output_file_path):
    with open(output_file_path, mode='w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        writer.writerow(['latitude', 'longitude', 'time'])
        for row in data:
            writer.writerow([row[0], row[1], row[2]])

# 文件路径
log_file_path = '2024-01-05 15_21_34.log'
output_csv_path = 'extracted_gps_data.csv'

# 从 log 文件中提取 GPS 数据
gps_data = extract_gps_data_from_log(log_file_path)

# 将提取的数据写入 CSV 文件
write_to_csv(gps_data, output_csv_path)

print(f"Data has been written to {output_csv_path}")
