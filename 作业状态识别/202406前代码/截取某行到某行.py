import pandas as pd

# 加载CSV文件
data = pd.read_csv('20240511_cleaned.csv')

# 设定需要保留的起始行号和结束行号（假设从第10行到第50行，注意这里行号从文件的第二行数据开始计算）
start_row = 150
end_row = 12514

# 由于首行为标题，实际数据索引应从1开始计算，调整start_row和end_row
adjusted_start_row = start_row - 1  # 减去1以调整索引
adjusted_end_row = end_row - 1      # 减去1以调整索引

# 截取指定行范围
filtered_data = data.iloc[adjusted_start_row:adjusted_end_row + 1]

# 保存到新的CSV文件，保留标题行，不包含索引
filtered_data.to_csv('20240511_cleaned-2.csv', index=False)
