import pandas as pd
import numpy as np
from datetime import datetime
from haversine import haversine
from sklearn.neighbors import RadiusNeighborsClassifier
import folium
from folium import plugins

data = pd.read_csv('20240511.csv')
data.sort_values(by='time', inplace=True)
def calculate_time_delta(time1, time2):
    dt_format = '%Y-%m-%d %H:%M:%S'
    time1_str = str(time1).split('.')[0]
    time2_str = str(time2).split('.')[0]
    datetime1 = datetime.strptime(time1_str, dt_format)
    datetime2 = datetime.strptime(time2_str, dt_format)
    return (datetime2 - datetime1).total_seconds() / 3600
def calculate_speed(data):
    speeds = []
    for i in range(len(data) - 1):
        loc1 = (data.iloc[i]['latitude'], data.iloc[i]['longitude'])
        loc2 = (data.iloc[i + 1]['latitude'], data.iloc[i + 1]['longitude'])
        distance = haversine(loc1, loc2)
        time_delta_hours = calculate_time_delta(data.iloc[i]['time'], data.iloc[i + 1]['time'])
        speeds.append(distance / time_delta_hours if time_delta_hours else 0)
    speeds.append(0)
    return speeds

data['speed'] = calculate_speed(data)
cleaned_data = data[(data['speed'] >= 2) & (data['speed'] <= 15)].copy()
cleaned_data.reset_index(drop=True, inplace=True)
cleaned_data.dropna(subset=['latitude', 'longitude'], inplace=True)
cleaned_data.sort_values(by='time', inplace=True)

coords = np.radians(cleaned_data[['latitude', 'longitude']])

# Setup for RadiusNeighborsClassifier
radius_km = 0.01
earth_radius_km = 6371
radius_radians = radius_km / earth_radius_km

# Assign 'Non-work' to all initially
cleaned_data['status'] = 'Non-work'
neigh = RadiusNeighborsClassifier(radius=radius_radians, metric='haversine', outlier_label='Non-work')
neigh.fit(coords, cleaned_data['status'])

neighbor_threshold = 20
distances, indices = neigh.radius_neighbors(coords)
cleaned_data['status'] = ['Work' if len(neighbors) >= neighbor_threshold else 'Non-work' for neighbors in indices]


work_threshold = 20
work_start_end = []
current_start = None

for i, row in cleaned_data.iterrows():
    if row['status'] == 'Work':
        if current_start is None:
            current_start = i
    elif current_start is not None:
        work_length = i - current_start
        if work_length >= work_threshold:
            work_start_end.append((current_start, i - 1))
        current_start = None

if current_start is not None and (len(cleaned_data) - current_start) >= work_threshold:
    work_start_end.append((current_start, len(cleaned_data) - 1))

# 增加详细调试输出，以监控每个步骤
def debug_and_validate(segment, radius_radians, neighbor_threshold):
    print(f"Revalidating segment from index {segment.index[0]} to {segment.index[-1]}")
    segment_coords = np.radians(segment[['latitude', 'longitude']])
    neigh = RadiusNeighborsClassifier(radius=radius_radians, metric='haversine', outlier_label=0)
    neigh.fit(segment_coords, np.zeros(len(segment)))

    _, segment_indices = neigh.radius_neighbors(segment_coords)
    segment['status'] = [1 if len(neighbors) >= neighbor_threshold else 0 for neighbors in segment_indices]

    # 输出邻居数量和状态分布
    print("Neighbors count:", [len(neighbors) for neighbors in segment_indices])
    print("Segment status distribution:", segment['status'].value_counts())

    # 检查是否需要重新切割区域
    new_work_areas = []
    start = None
    for i, status in enumerate(segment['status']):
        if status == 1 and start is None:
            start = i
        elif status == 0 and start is not None:
            if i - start >= work_threshold:
                new_work_areas.append((start + segment.index[0], i - 1 + segment.index[0]))
            start = None
    if start is not None and (len(segment) - start) >= work_threshold:
        new_work_areas.append((start + segment.index[0], len(segment) - 1 + segment.index[0]))
    return new_work_areas

# 应用改进的二次验证函数
final_work_areas = []
for start, end in work_start_end:
    segment = cleaned_data.iloc[start:end + 1].copy()
    new_areas = debug_and_validate(segment, radius_radians, neighbor_threshold)
    final_work_areas.extend(new_areas)



# 最后，根据 final_work_areas 更新 cleaned_data 的状态
for start, end in final_work_areas:
    cleaned_data.loc[start:end, 'status'] = 1  # 确保这些区域被标记为“工作”区域


# 地图显示
map_center = cleaned_data[['latitude', 'longitude']].mean().values
map = folium.Map(location=map_center, zoom_start=12)
for start, end in final_work_areas:
    path = cleaned_data.iloc[start:end + 1][['latitude', 'longitude']].values
    plugins.AntPath(locations=path, color='green', weight=2, opacity=1, delay=5000).add_to(map)
    start_location = cleaned_data.iloc[start][['latitude', 'longitude']].values
    end_location = cleaned_data.iloc[end][['latitude', 'longitude']].values
    folium.Marker(start_location, icon=folium.DivIcon(html=f'<div style="font-size: 15pt; color : red">S{start}</div>')).add_to(map)
    folium.Marker(end_location, icon=folium.DivIcon(html=f'<div style="font-size: 15pt; color : blue">E{end}</div>')).add_to(map)
map.save('20240511-验证后.html')

for number, (start, end) in enumerate(final_work_areas, start=1):
    start_coords = cleaned_data.iloc[start][['latitude', 'longitude']].values
    end_coords = cleaned_data.iloc[end][['latitude', 'longitude']].values
    print(f"工作区域 {number}: 起点坐标 {start_coords} (索引 {start}), 终点坐标 {end_coords} (索引 {end})")


if 'time' in cleaned_data.columns:
    cleaned_data['time'] = pd.to_datetime(cleaned_data['time'], format='%Y-%m-%d %H:%M:%S')
    total_time_seconds = (cleaned_data['time'].iloc[-1] - cleaned_data['time'].iloc[0]).total_seconds()
    total_time_hours = total_time_seconds / 3600
    average_speed_kmh = cleaned_data['speed'].mean()
    print(f"总时长: {total_time_hours:.3f} 小时")
    print(f"平均速度: {average_speed_kmh:.3f} 公里/小时")
else:
    print("没有时间数据无法计算总时长。")
