import pandas as pd
import numpy as np
from haversine import haversine
import folium
from folium import plugins
import time
from folium.plugins import Fullscreen, MeasureControl, MarkerCluster, HeatMap
from sklearn.neighbors import RadiusNeighborsClassifier

# 阈值设置
# 第一次计算的阈值
SPEED_MIN_1 = 2
SPEED_MAX_1 = 15
NEIGHBOR_THRESHOLD_1 = 10
WORK_THRESHOLD_1 = 30
OFFSET_POINTS_1 = 0
RADIUS_KM_1 = 0.015
EARTH_RADIUS_KM = 6371

# 第二次计算的阈值
SPEED_MIN_2 = 2
SPEED_MAX_2 = 15
NEIGHBOR_THRESHOLD_2 = 20
WORK_THRESHOLD_2 = 30
OFFSET_POINTS_2 = 0
RADIUS_KM_2 = 0.015

# 第一部分: 数据清洗
data = pd.read_csv('20240511.csv')
# 确保数据按照时间戳排序
data.sort_values(by='time', inplace=True)
# 记录开始时间
start_time = time.time()


def vectorized_haversine(lat1, lon1, lat2, lon2):
    lat1, lon1, lat2, lon2 = map(np.radians, [lat1, lon1, lat2, lon2])
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = np.sin(dlat / 2.0) ** 2 + np.cos(lat1) * np.cos(lat2) * np.sin(dlon / 2.0) ** 2
    c = 2 * np.arcsin(np.sqrt(a))
    km = 6371 * c  # Multiply by Earth radius to get kilometers
    return km


# 使用优化后的计算距离函数
def calculate_speed_optimized(data):
    data['time'] = pd.to_datetime(data['time'])
    data['latitude_shifted'] = data['latitude'].shift(-1)
    data['longitude_shifted'] = data['longitude'].shift(-1)
    data['distance'] = vectorized_haversine(data['latitude'], data['longitude'],
                                            data['latitude_shifted'], data['longitude_shifted'])
    time_deltas = data['time'].diff().dt.total_seconds() / 3600
    data['speed'] = data['distance'] / time_deltas
    data['speed'] = data['speed'].fillna(0)
    data.drop(['latitude_shifted', 'longitude_shifted', 'distance'], axis=1, inplace=True)
    return data


# 使用优化后的计算速度函数
data = calculate_speed_optimized(data)


def optimize_data_cleaning(data, speed_min, speed_max):
    # 筛选速度范围并直接重置索引，避免使用额外的.copy()操作
    cleaned_data = data[(data['speed'] >= speed_min) & (data['speed'] <= speed_max)].reset_index(drop=True)
    # 删除NaN值
    cleaned_data = cleaned_data.dropna(subset=['latitude', 'longitude'])
    return cleaned_data


# 使用优化的数据清洗函数
cleaned_data = optimize_data_cleaning(data, SPEED_MIN_1, SPEED_MAX_1)

radius_radians_1 = RADIUS_KM_1 / EARTH_RADIUS_KM


# 使用 RadiusNeighborsClassifier 来查找指定半径范围内的邻居
def apply_neighbors_classifier(data, radius_radians, neighbor_threshold):
    coords = np.radians(data[['latitude', 'longitude']])
    neigh = RadiusNeighborsClassifier(radius=radius_radians, metric='haversine', outlier_label='Non-work')
    neigh.fit(coords, data['status'])
    distances, indices = neigh.radius_neighbors(coords)
    data['status'] = ['Work' if len(neighbors) >= neighbor_threshold else 'Non-work' for neighbors in indices]
    return data


# 使用 RadiusNeighborsClassifier 来标记数据的工作状态
cleaned_data['status'] = 'Non-work'
cleaned_data = apply_neighbors_classifier(cleaned_data, radius_radians_1, NEIGHBOR_THRESHOLD_1)


# 识别第一次计算的连续工作区域
def identify_work_areas(data, work_threshold, offset_points):
    work_start_end = []
    current_start = None
    adjusted_points = set()
    for i, row in data.iterrows():
        if row['status'] == 'Work':
            if current_start is None:
                current_start = i
        else:
            if current_start is not None:
                work_length = i - current_start
                if work_length >= work_threshold:
                    start_index = max(current_start + offset_points, 0)
                    end_index = min(i - offset_points - 1, len(data) - 1)
                    work_start_end.append((start_index, end_index))
                    adjusted_points.update(range(current_start, start_index))
                    adjusted_points.update(range(end_index + 1, i))
                else:
                    adjusted_points.update(range(current_start, i))
                current_start = None

    # 检查最后一段工作区域是否满足阈值条件
    if current_start is not None and (len(data) - current_start) >= work_threshold:
        start_index = max(current_start + offset_points, 0)
        end_index = min(len(data) - offset_points - 1, len(data) - 1)
        work_start_end.append((start_index, end_index))
        adjusted_points.update(range(current_start, start_index))
        adjusted_points.update(range(end_index + 1, len(data)))

    return work_start_end, adjusted_points


# 第一次计算识别工作区域
work_start_end_1, adjusted_points_1 = identify_work_areas(cleaned_data, WORK_THRESHOLD_1, OFFSET_POINTS_1)

# 初始化 adjusted 列
cleaned_data['adjusted'] = False
cleaned_data.loc[list(adjusted_points_1), 'adjusted'] = True

# 计算距离并计算总距离、平均距离
distances = vectorized_haversine(cleaned_data['latitude'].shift(), cleaned_data['longitude'].shift(),
                                 cleaned_data['latitude'], cleaned_data['longitude'])
total_distance_km = distances.sum()
average_distance_km = distances.mean()

# 第二次计算逻辑
radius_radians_2 = RADIUS_KM_2 / EARTH_RADIUS_KM
second_pass_results = []

for start, end in work_start_end_1:
    sub_data = cleaned_data.iloc[start:end + 1].copy()
    sub_data = optimize_data_cleaning(sub_data, SPEED_MIN_2, SPEED_MAX_2)
    sub_data['status'] = 'Non-work'
    sub_data = apply_neighbors_classifier(sub_data, radius_radians_2, NEIGHBOR_THRESHOLD_2)
    sub_work_start_end, _ = identify_work_areas(sub_data, WORK_THRESHOLD_2, OFFSET_POINTS_2)
    second_pass_results.append((start, end, sub_work_start_end))

# 输出结果并标记地图
colors = ['blue', 'green', 'purple', 'orange', 'yellow', 'brown', 'pink', 'gray']
group_number = 1
map_center = cleaned_data[['latitude', 'longitude']].mean().values
map = folium.Map(location=map_center, zoom_start=12)

# 创建带方向的轨迹
locations = cleaned_data[['latitude', 'longitude']].values
plugins.AntPath(locations, color='green', weight=3, opacity=0.7, delay=20000, pulse_color='#F0F0F0').add_to(map)

# # 标记整个坐标集的起点和终点
# folium.CircleMarker(location=locations[0], radius=10, color='black', fill=True, fill_color='green',
#                     fill_opacity=1).add_to(map)
# folium.CircleMarker(location=locations[-1], radius=10, color='black', fill=True, fill_color='red',
#                     fill_opacity=1).add_to(map)

for group_number, (start, end) in enumerate(work_start_end_1, start=1):
    start_location = cleaned_data.iloc[start][['latitude', 'longitude']].values
    end_location = cleaned_data.iloc[end][['latitude', 'longitude']].values
    print(
        f"第一次计算工作区域 {group_number}: 起点坐标 {start_location} (索引 {start}), 终点坐标 {end_location} (索引 {end})")

    for sub_index, (sub_start, sub_end) in enumerate(second_pass_results[group_number - 1][2], start=1):
        if start + sub_start < len(cleaned_data) and start + sub_end < len(cleaned_data):
            sub_start_location = cleaned_data.iloc[start + sub_start][['latitude', 'longitude']].values
            sub_end_location = cleaned_data.iloc[start + sub_end][['latitude', 'longitude']].values
            print(
                f"  第二次计算工作区域 {group_number}.{sub_index}: 起点坐标 {sub_start_location} (索引 {start + sub_start}), 终点坐标 {sub_end_location} (索引 {start + sub_end})")

            sub_area_coords = cleaned_data.iloc[start + sub_start:end + sub_end + 1][['latitude', 'longitude']].values
            folium.PolyLine(sub_area_coords, color=colors[group_number % len(colors)], weight=2.5, opacity=1).add_to(
                map)

            folium.Marker(sub_start_location, icon=folium.DivIcon(
                html=f'<div style="font-size: 10pt; color : black">{group_number}.{sub_index}S</div>')).add_to(map)
            folium.Marker(sub_end_location, icon=folium.DivIcon(
                html=f'<div style="font-size: 10pt; color : black">{group_number}.{sub_index}E</div>')).add_to(map)

# 保存地图到 HTML 文件
map.save('result.html')

# 记录结束时间并打印运行时间
end_time = time.time()
elapsed_time = end_time - start_time
print(f"代码运行时间：{elapsed_time:.2f} 秒")

# 打印统计和分析结果
print(f"总距离: {total_distance_km:.3f} 公里")
print(f"平均每点距离: {average_distance_km:.5f} 公里")
print(cleaned_data['status'].value_counts())

# 如果数据中包含时间戳，我们可以计算总时长
if 'time' in cleaned_data.columns:
    cleaned_data['time'] = pd.to_datetime(cleaned_data['time'], format='%Y-%m-%d %H:%M:%S')
    total_time_seconds = (cleaned_data['time'].iloc[-1] - cleaned_data['time'].iloc[0]).total_seconds()
    total_time_hours = total_time_seconds / 3600
    print(f"总时长: {total_time_hours:.3f} 小时")
    average_speed_kmh = cleaned_data['speed'].mean()
    print(f"平均速度: {average_speed_kmh:.3f} 公里/小时")
else:
    print("没有时间数据无法计算总时长。")

