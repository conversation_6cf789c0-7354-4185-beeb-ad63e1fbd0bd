import pandas as pd
import numpy as np
import folium
from folium import plugins
import time
from sklearn.neighbors import RadiusNeighborsClassifier
import datetime
import streamlit as st
from streamlit_folium import folium_static

# Streamlit sliders for threshold values
SPEED_MIN_1 = st.slider('SPEED_MIN_1', 0, 10, 2)
SPEED_MAX_1 = st.slider('SPEED_MAX_1', 10, 20, 15)
NEIGHBOR_THRESHOLD_1 = st.slider('NEIGHBOR_THRESHOLD_1', 0, 20, 10)
WORK_THRESHOLD_1 = st.slider('WORK_THRESHOLD_1', 0, 50, 30)
OFFSET_POINTS_1 = 0
RADIUS_KM_1 = st.slider('RADIUS_KM_1', 0.0, 1.0, 0.010)
EARTH_RADIUS_KM = 6371

SPEED_MIN_2 = st.slider('SPEED_MIN_2', 0, 10, 2)
SPEED_MAX_2 = st.slider('SPEED_MAX_2', 10, 20, 15)
NEIGHBOR_THRESHOLD_2 = st.slider('NEIGHBOR_THRESHOLD_2', 0, 20, 20)
WORK_THRESHOLD_2 = st.slider('WORK_THRESHOLD_2', 0, 50, 30)
OFFSET_POINTS_2 = 0
RADIUS_KM_2 = st.slider('RADIUS_KM_2', 0.0, 1.0, 0.015)
INTERVAL_THRESHOLD = st.slider('INTERVAL_THRESHOLD', 0, 10, 5)
AVERAGE_SPEED_THRESHOLD = st.slider('AVERAGE_SPEED_THRESHOLD', 0, 10, 3)

# 确认文件路径
data = pd.read_csv('/Volumes/Work/Python/作业状态识别/20240512.csv')

# 确保数据按照时间戳排序
data.sort_values(by='time', inplace=True)

start_time = time.time()

def vectorized_haversine(lat1, lon1, lat2, lon2):
    lat1, lon1, lat2, lon2 = map(np.radians, [lat1, lon1, lat2, lon2])
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = np.sin(dlat / 2.0) ** 2 + np.cos(lat1) * np.cos(lat2) * np.sin(dlon / 2.0) ** 2
    c = 2 * np.arcsin(np.sqrt(a))
    km = 6371 * c
    return km

def calculate_speed_optimized(data):
    data['time'] = pd.to_datetime(data['time'])
    data['latitude_shifted'] = data['latitude'].shift(-1)
    data['longitude_shifted'] = data['longitude'].shift(-1)
    data['distance'] = vectorized_haversine(data['latitude'], data['longitude'], data['latitude_shifted'], data['longitude_shifted'])
    time_deltas = data['time'].diff().dt.total_seconds() / 3600
    data['speed'] = data['distance'] / time_deltas
    data['speed'] = data['speed'].fillna(0)
    data.drop(['latitude_shifted', 'longitude_shifted', 'distance'], axis=1, inplace=True)
    return data

data = calculate_speed_optimized(data)

def optimize_data_cleaning(data, speed_min, speed_max):
    cleaned_data = data[(data['speed'] >= speed_min) & (data['speed'] <= speed_max)].reset_index(drop=True)
    cleaned_data = cleaned_data.dropna(subset=['latitude', 'longitude'])
    return cleaned_data

cleaned_data = optimize_data_cleaning(data, SPEED_MIN_1, SPEED_MAX_1)
radius_radians_1 = RADIUS_KM_1 / EARTH_RADIUS_KM

def apply_neighbors_classifier(data, radius_radians, neighbor_threshold):
    coords = np.radians(data[['latitude', 'longitude']])
    neigh = RadiusNeighborsClassifier(radius=radius_radians, metric='haversine', outlier_label='Non-work')
    neigh.fit(coords, data['status'])
    distances, indices = neigh.radius_neighbors(coords)
    data['status'] = ['Work' if len(neighbors) >= neighbor_threshold else 'Non-work' for neighbors in indices]
    return data

st.write(f"清洗前数据量: {len(data)}")
cleaned_data['status'] = 'Non-work'
cleaned_data = apply_neighbors_classifier(cleaned_data, radius_radians_1, NEIGHBOR_THRESHOLD_1)
st.write(f"清洗后数据量: {len(cleaned_data)}")

def identify_work_areas(data, work_threshold, offset_points):
    work_start_end = []
    current_start = None
    adjusted_points = set()

    def process_work_area(start, end):
        if start is not None and (end - start) >= work_threshold:
            start_index = max(start + offset_points, 0)
            end_index = min(end - offset_points - 1, len(data) - 1)
            work_start_end.append((start_index, end_index))
            adjusted_points.update(range(start, start_index))
            adjusted_points.update(range(end_index + 1, end))
        elif start is not None:
            adjusted_points.update(range(start, end))

    for i, row in data.iterrows():
        if row['status'] == 'Work':
            if current_start is None:
                current_start = i
        else:
            process_work_area(current_start, i)
            current_start = None
    process_work_area(current_start, len(data))
    return work_start_end, adjusted_points

work_start_end_1, adjusted_points_1 = identify_work_areas(cleaned_data, WORK_THRESHOLD_1, OFFSET_POINTS_1)
cleaned_data['adjusted'] = False
cleaned_data.loc[list(adjusted_points_1), 'adjusted'] = True

distances = vectorized_haversine(cleaned_data['latitude'].shift(), cleaned_data['longitude'].shift(), cleaned_data['latitude'], cleaned_data['longitude'])
total_distance_km = distances.sum()
average_distance_km = distances.mean()

radius_radians_2 = RADIUS_KM_2 / EARTH_RADIUS_KM
second_pass_results = []
final_status = ['Unknown'] * len(cleaned_data)

for start, end in work_start_end_1:
    sub_data = cleaned_data.iloc[start:end + 1].copy()
    sub_data = optimize_data_cleaning(sub_data, SPEED_MIN_2, SPEED_MAX_2)

    if len(sub_data) > 0:
        sub_data['status'] = 'Non-work'
        sub_data = apply_neighbors_classifier(sub_data, radius_radians_2, NEIGHBOR_THRESHOLD_2)
        sub_work_start_end, _ = identify_work_areas(sub_data, WORK_THRESHOLD_2, OFFSET_POINTS_2)

        valid_sub_work_areas = []
        for sub_start, sub_end in sub_work_start_end:
            sub_area_data = sub_data.iloc[sub_start:sub_end + 1]
            average_speed = sub_area_data['speed'].mean()
            point_interval = sub_area_data['time'].diff().dt.total_seconds().mean()
            if point_interval <= INTERVAL_THRESHOLD and average_speed >= AVERAGE_SPEED_THRESHOLD:
                valid_sub_work_areas.append((sub_start, sub_end))

        second_pass_results.append((start, end, valid_sub_work_areas))

        for sub_start, sub_end in valid_sub_work_areas:
            for i in range(start + sub_start, start + sub_end + 1):
                final_status[i] = 'Work'
        for i in range(start, end + 1):
            if final_status[i] == 'Unknown':
                final_status[i] = 'Non-work-area-work'
    else:
        second_pass_results.append((start, end, []))
        for i in range(start, end + 1):
            final_status[i] = 'Non-work-area-work'

for i, row in cleaned_data.iterrows():
    if final_status[i] == 'Unknown':
        final_status[i] = 'Non-work'

colors = ['#1f77b4', '#2ca02c', '#d62728', '#ff7f0e']
color_index = 0

map_center = cleaned_data[['latitude', 'longitude']].mean().values
map = folium.Map(location=map_center, zoom_start=12)

work_layer = folium.FeatureGroup(name='工作状态')
non_work_layer = folium.FeatureGroup(name='非工作状态')
track_layer = folium.FeatureGroup(name='轨迹')
non_work_area_work_layer = folium.FeatureGroup(name='非区域工作状态')

locations = cleaned_data[['latitude', 'longitude']].values
plugins.AntPath(locations, color='black', weight=2, opacity=0.7, delay=20000, pulse_color='#F0F0F0').add_to(track_layer)

for i, row in cleaned_data.iterrows():
    coord = row[['latitude', 'longitude']].values
    if final_status[i] == 'Non-work':
        folium.CircleMarker(location=coord, radius=5, color='gray', fill=True, fill_color='gray', fill_opacity=0.4, opacity=0.4).add_to(non_work_layer)
    elif final_status[i] == 'Non-work-area-work':
        folium.CircleMarker(location=coord, radius=5, color='black', fill=True, fill_color='black', fill_opacity=0.4, opacity=0.4).add_to(non_work_area_work_layer)
    else:
        folium.CircleMarker(location=coord, radius=5, color='blue', fill=True, fill_color='blue', fill_opacity=0.4, opacity=0.4).add_to(work_layer)

color_index = 0
for group_number, (start, end) in enumerate(work_start_end_1, start=1):
    start_location = cleaned_data.iloc[start][['latitude', 'longitude']].values
    end_location = cleaned_data.iloc[end][['latitude', 'longitude']].values

    sub_work_areas = second_pass_results[group_number - 1][2]

    if sub_work_areas:
        print(f"第一次计算工作区域 {group_number}: 起点坐标 {start_location} (索引 {start}), 终点坐标 {end_location} (索引 {end})")

        for sub_index, (sub_start, sub_end) in enumerate(sub_work_areas, start=1):
            if start + sub_start < len(cleaned_data) and start + sub_end < len(cleaned_data):
                sub_start_location = cleaned_data.iloc[start + sub_start][['latitude', 'longitude']].values
                sub_end_location = cleaned_data.iloc[start + sub_end][['latitude', 'longitude']].values
                sub_data = cleaned_data.iloc[start + sub_start:start + sub_end + 1]

                average_speed = sub_data['speed'].mean()
                point_interval = sub_data['time'].diff().dt.total_seconds().mean()

                print(f"  第二次计算工作区域 {group_number}.{sub_index}: 起点坐标 {sub_start_location} (索引 {start + sub_start}), 终点坐标 {sub_end_location} (索引 {start + sub_end}), 平均速度: {average_speed:.3f} 公里/小时, 点间隔: {point_interval:.2f} 秒")

                for i in range(start + sub_start, start + sub_end + 1):
                    coord = cleaned_data.iloc[i][['latitude', 'longitude']].values
                    folium.CircleMarker(location=coord, radius=5, color=colors[color_index % len(colors)], fill=True, fill_color=colors[color_index % len(colors)], fill_opacity=0.4, opacity=0.4).add_to(work_layer)

                folium.Marker(sub_start_location, icon=folium.DivIcon(html=f'<div style="font-size: 10pt; color : black">{group_number}.{sub_index}S</div>')).add_to(work_layer)
                folium.Marker(sub_end_location, icon=folium.DivIcon(html=f'<div style="font-size: 10pt; color : black">{group_number}.{sub_index}E</div>')).add_to(work_layer)

        color_index += 1

work_layer.add_to(map)
non_work_layer.add_to(map)
non_work_area_work_layer.add_to(map)
track_layer.add_to(map)

folium.LayerControl().add_to(map)

current_time_str = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
input_filename = '20240512.csv'
base_filename = input_filename.split('.')[0]
output_filename = f"{base_filename}_{current_time_str}.html"

map.save(output_filename)

end_time = time.time()
elapsed_time = end_time - start_time
st.write(f"代码运行时间：{elapsed_time:.2f} 秒")

st.write(f"总距离: {total_distance_km:.3f} 公里")
st.write(f"平均每点距离: {average_distance_km:.5f} 公里")
st.write(cleaned_data['status'].value_counts())

if 'time' in cleaned_data.columns:
    cleaned_data['time'] = pd.to_datetime(cleaned_data['time'], format='%Y-%m-%d %H:%M:%S')
    total_time_seconds = (cleaned_data['time'].iloc[-1] - cleaned_data['time'].iloc[0]).total_seconds()
    total_time_hours = total_time_seconds / 3600
    st.write(f"总时长: {total_time_hours:.3f} 小时")
    average_speed_kmh = cleaned_data['speed'].mean()
    st.write(f"平均速度: {average_speed_kmh:.3f} 公里/小时")
else:
    st.write("没有时间数据无法计算总时长。")

folium_static(map)
