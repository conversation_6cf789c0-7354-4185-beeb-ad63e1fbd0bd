import pandas as pd
import numpy as np
import folium
from folium import plugins
import time
from sklearn.neighbors import RadiusNeighborsClassifier
import datetime
from scipy.spatial import Delaunay
from shapely.ops import unary_union, polygonize
from shapely.geometry import Polygon, LineString
from math import radians, cos, sin, asin, sqrt
import builtins
from scipy.spatial import KDTree


# 第一次计算的阈值
SPEED_MIN_1 = 2
SPEED_MAX_1 = 15
NEIGHBOR_THRESHOLD_1 = 15
WORK_THRESHOLD_1 = 60
OFFSET_POINTS_1 = 20
RADIUS_KM_1 = 0.01
EARTH_RADIUS_KM = 6371
DISTANCE_THRESHOLD = 0.1  # 距离阈值，公里

# 第二次计算的阈值
SPEED_MIN_2 = 2
SPEED_MAX_2 = 15
NEIGHBOR_THRESHOLD_2 = 1
WORK_THRESHOLD_2 = 2
OFFSET_POINTS_2 = 0
RADIUS_KM_2 = 0.1
INTERVAL_THRESHOLD = 5  # 点间隔阈值（秒）
AVERAGE_SPEED_THRESHOLD = 2  # 平均速度阈值（公里/小时）

# 三角法的最长边阈值
MAX_EDGE_LENGTH_M = 10  # 最大边长，米

# 第一部分: 数据清洗
data = pd.read_csv('20240511.csv')
# 确保数据按照时间戳排序
data.sort_values(by='time', inplace=True)
# 记录开始时间
start_time = time.time()

def vectorized_haversine(lat1, lon1, lat2, lon2):  # 定义一个计算两点之间距离的函数，使用Haversine公式
    lat1, lon1, lat2, lon2 = builtins.map(np.radians, [lat1, lon1, lat2, lon2])  # 将纬度和经度从度数转换为弧度，因为Haversine公式要求输入的角度是弧度制
    dlat = lat2 - lat1  # 计算两个点的纬度差
    dlon = lon2 - lon1  # 计算两个点的经度差
    a = np.sin(dlat / 2.0) ** 2 + np.cos(lat1) * np.cos(lat2) * np.sin(dlon / 2.0) ** 2  # 计算Haversine公式的 'a' 参数
    c = 2 * np.arcsin(np.sqrt(a))  # 计算Haversine公式的 'c' 参数
    km = 6371 * c  # 将 'c' 转换为实际距离（公里），地球半径约为6371公里
    return km  # 返回两个点之间的距离，单位为公里

def calculate_speed_optimized(data):
    data['time'] = pd.to_datetime(data['time'])  # 确保时间列为datetime格式
    data['latitude_shifted'] = data['latitude'].shift(-1)  # 获取前一个点的纬度
    data['longitude_shifted'] = data['longitude'].shift(-1)  # 获取前一个点的经度
    data['distance'] = vectorized_haversine(data['latitude'], data['longitude'], data['latitude_shifted'], data['longitude_shifted'])  # 计算相邻点之间的距离
    time_deltas = data['time'].diff().dt.total_seconds() / 3600  # 秒转换为小时
    data['speed'] = data['distance'] / time_deltas  # 计算速度（公里/小时）
    data['speed'] = data['speed'].fillna(0)  # 将NaN值填充为0
    data['distance'] = data['distance'].fillna(0)  # 将NaN值填充为0
    data.drop(['latitude_shifted', 'longitude_shifted'], axis=1, inplace=True)  # 删除临时生成的列
    return data

# 使用优化后的计算速度函数
data = calculate_speed_optimized(data)

def optimize_data_cleaning(data, speed_min, speed_max):  # 定义一个用于清洗数据的函数，参数为数据、最小速度和最大速度
    cleaned_data = data[(data['speed'] >= speed_min) & (data['speed'] <= speed_max)].reset_index(drop=True)  # 筛选速度在指定范围内的数据，并重置索引，避免使用额外的 .copy() 操作
    cleaned_data = cleaned_data.dropna(subset=['latitude', 'longitude'])  # 删除缺失经纬度值的数据，确保所有数据点都有有效的经纬度值
    return cleaned_data  # 返回清洗后的数据

# 使用优化的数据清洗函数
cleaned_data = optimize_data_cleaning(data, SPEED_MIN_1, SPEED_MAX_1)  # 调用清洗函数，传入数据和速度范围，获取清洗后的数据
radius_radians_1 = RADIUS_KM_1 / EARTH_RADIUS_KM  # 将半径从公里转换为地球半径单位的弧度值，用于后续的地理计算

# 使用 RadiusNeighborsClassifier 来查找指定半径范围内的邻居
def apply_neighbors_classifier(data, radius_radians, neighbor_threshold):  # 定义一个函数，用于应用邻居分类器，参数包括数据、半径和邻居阈值
    coords = np.radians(data[['latitude', 'longitude']])  # 将数据中的纬度和经度转换为弧度
    neigh = RadiusNeighborsClassifier(radius=radius_radians, metric='haversine',
                                      outlier_label='Non-work')  # 创建一个半径邻居分类器，使用Haversine度量，并标记异常值为“Non-work”
    neigh.fit(coords, data['status'])  # 使用坐标和状态数据训练分类器
    distances, indices = neigh.radius_neighbors(coords)  # 查找每个点在指定半径范围内的邻居

    # 增加距离判断条件
    for i in range(len(data)):
        if i == 0:
            data.at[i, 'status'] = 'Non-work'  # 第一个点默认设为非工作状态
        else:
            prev_lat, prev_lon = data.at[i - 1, 'latitude'], data.at[i - 1, 'longitude']
            curr_lat, curr_lon = data.at[i, 'latitude'], data.at[i, 'longitude']
            distance_to_prev = vectorized_haversine(prev_lat, prev_lon, curr_lat, curr_lon)
            if distance_to_prev > DISTANCE_THRESHOLD:  # 使用提取的距离阈值
                data.at[i, 'status'] = 'Non-work'
            else:
                if len(indices[i]) >= neighbor_threshold:
                    data.at[i, 'status'] = 'Work'
                else:
                    data.at[i, 'status'] = 'Non-work'
    return data  # 返回更新后的数据


# 使用 RadiusNeighborsClassifier 来标记数据的工作状态
cleaned_data['status'] = 'Non-work'
cleaned_data = apply_neighbors_classifier(cleaned_data, radius_radians_1, NEIGHBOR_THRESHOLD_1)

# 添加调试信息，打印清洗前的数据量
print(f"清洗前数据量: {len(data)}")

# 添加调试信息，打印清洗后的数据量
print(f"清洗后数据量: {len(cleaned_data)}")

def identify_work_areas(data, work_threshold, offset_points):  # 定义一个函数，用于识别连续的工作区域，参数包括数据、工作阈值和偏移点数
    work_start_end = []  # 存储工作区域的起始和结束索引
    current_start = None  # 当前工作区域的起始索引
    adjusted_points = set()  # 存储需要调整的点的索引

    def process_work_area(start, end):  # 辅助函数，用于处理工作区域并更新相关索引
        if start is not None and (end - start) >= work_threshold:  # 如果起始索引存在且工作区域长度满足阈值
            start_index = max(start + offset_points, 0)  # 计算调整后的起始索引，确保不小于0
            end_index = min(end - offset_points - 1, len(data) - 1)  # 计算调整后的结束索引，确保不超过数据长度
            work_start_end.append((start_index, end_index))  # 将工作区域的起始和结束索引添加到列表中
            adjusted_points.update(range(start, start_index))  # 更新需要调整的点的索引（起始部分）
            adjusted_points.update(range(end_index + 1, end))  # 更新需要调整的点的索引（结束部分）
        elif start is not None:  # 如果工作区域长度不满足阈值
            adjusted_points.update(range(start, end))  # 将整个区域标记为需要调整

    for i, row in data.iterrows():  # 遍历数据中的每一行
        if row['status'] == 'Work':  # 如果当前点的状态是“工作”
            if current_start is None:  # 如果没有正在记录的工作区域
                current_start = i  # 记录当前点为工作区域的起始点
        else:  # 如果当前点的状态不是“工作”
            process_work_area(current_start, i)  # 处理当前工作区域
            current_start = None  # 重置当前工作区域的起始索引
    process_work_area(current_start, len(data))  # 检查并处理最后一个工作区域
    return work_start_end, adjusted_points  # 返回工作区域的起始和结束索引，以及需要调整的点的索引

# 第一次计算识别工作区域
work_start_end_1, adjusted_points_1 = identify_work_areas(cleaned_data, WORK_THRESHOLD_1, OFFSET_POINTS_1)  # 调用函数识别工作区域

# 初始化 adjusted 列
cleaned_data['adjusted'] = False  # 初始化 adjusted 列，默认值为 False
cleaned_data.loc[list(adjusted_points_1), 'adjusted'] = True  # 将需要调整的点的 adjusted 列设置为 True

# 第二次计算逻辑
radius_radians_2 = RADIUS_KM_2 / EARTH_RADIUS_KM  # 将第二次计算使用的半径从公里转换为弧度
second_pass_results = []  # 初始化一个列表，用于存储第二次计算的结果
final_status = ['Unknown'] * len(cleaned_data)  # 用于记录每个点的最终状态

for start, end in work_start_end_1:  # 遍历第一次计算识别的每个工作区域
    sub_data = cleaned_data.iloc[start:end + 1].copy()  # 提取当前工作区域的数据，并创建其副本
    sub_data = optimize_data_cleaning(sub_data, SPEED_MIN_2, SPEED_MAX_2)  # 对提取的数据进行进一步清洗，使用第二次计算的速度范围

    if len(sub_data) > 0:  # 检查清洗后的数据点是否足够多
        sub_data['status'] = 'Non-work'  # 初始化所有数据点的状态为“Non-work”
        sub_data = apply_neighbors_classifier(sub_data, radius_radians_2, NEIGHBOR_THRESHOLD_2)  # 应用邻居分类器，根据邻居数量更新每个点的状态
        sub_work_start_end, _ = identify_work_areas(sub_data, WORK_THRESHOLD_2, OFFSET_POINTS_2)  # 识别清洗后数据中的工作区域

        # 过滤掉不符合点间隔和平均速度条件的区域
        valid_sub_work_areas = []
        for sub_start, sub_end in sub_work_start_end:
            sub_area_data = sub_data.iloc[sub_start:sub_end + 1]
            average_speed = sub_area_data['speed'].mean()
            point_interval = sub_area_data['time'].diff().dt.total_seconds().mean()
            if point_interval <= INTERVAL_THRESHOLD and average_speed >= AVERAGE_SPEED_THRESHOLD:
                valid_sub_work_areas.append((sub_start, sub_end))

        second_pass_results.append((start, end, valid_sub_work_areas))  # 将当前工作区域的结果添加到第二次计算结果列表中

        # 标记通过第二次验证的点为 'Work'
        for sub_start, sub_end in valid_sub_work_areas:
            for i in range(start + sub_start, start + sub_end + 1):
                final_status[i] = 'Work'
        # 标记未通过第二次验证的点为 'Non-work-area-work'
        for i in range(start, end + 1):
            if final_status[i] == 'Unknown':
                final_status[i] = 'Non-work-area-work'
    else:
        second_pass_results.append((start, end, []))  # 如果数据点不够多，添加空结果以保持结构一致性
        for i in range(start, end + 1):
            final_status[i] = 'Non-work-area-work'

# 标记所有非工作状态的点
for i, row in cleaned_data.iterrows():
    if final_status[i] == 'Unknown':
        final_status[i] = 'Non-work'

# 定义函数，用于计算球面多边形的面积
def calculate_spherical_polygon_area(coords, radius=EARTH_RADIUS_KM * 1000):
    coords = [(radians(lon), radians(lat)) for lon, lat in coords if lon and lat]  # 将经纬度转换为弧度，并排除空值
    total = 0.0
    for i in range(len(coords)):
        lon1, lat1 = coords[i]
        lon2, lat2 = coords[(i + 1) % len(coords)]  # 循环列表实现闭环
        total += (lon2 - lon1) * (2 + sin(lat1) + sin(lat2))
    return abs(total * radius ** 2 / 2)

# 定义函数，使用Haversine公式计算两点之间的地球表面距离
def haversine(lon1, lat1, lon2, lat2):
    lon1, lat1, lon2, lat2 = builtins.map(radians, [lon1, lat1, lon2, lat2])  # 将角度转换为弧度
    dlon = lon2 - lon1
    dlat = lat2 - lat1
    a = sin(dlat / 2) ** 2 + cos(lat1) * cos(lat2) * sin(dlon / 2) ** 2
    c = 2 * asin(sqrt(a))
    return EARTH_RADIUS_KM * 1000 * c  # 返回计算的距离

# 定义函数，检查三角形是否为有效的三角形
def valid_triangle(triangle, points, max_length=MAX_EDGE_LENGTH_M):
    for i in range(3):
        point1 = points[triangle[i]]
        point2 = points[triangle[(i + 1) % 3]]
        if haversine(point1[0], point1[1], point2[0], point2[1]) > max_length:
            return False
    return True

# 定义函数，从三角形集合中提取最外圈的边
def extract_outer_edges(triangles, points):
    edge_count = {}
    for tri in triangles:
        for i in range(3):
            edge = tuple(sorted([tri[i], tri[(i + 1) % 3]]))  # 排序后的边，无向边
            edge_count[edge] = edge_count.get(edge, 0) + 1
    outer_edges = [edge for edge, count in edge_count.items() if count == 1]  # 选择只出现一次的边
    return outer_edges

# 计算外围多边形的周长
def calculate_perimeter(coords):
    total_perimeter = 0.0  # 初始化总周长为0
    num_points = len(coords)  # 获取顶点数量
    if num_points > 1:  # 当顶点数量超过1时，才可能形成闭环
        for i in range(num_points):
            lon1, lat1 = coords[i]  # 当前顶点
            lon2, lat2 = coords[(i + 1) % num_points]  # 下一个顶点，利用模运算回到列表开始，形成闭环
            total_perimeter += haversine(lon1, lat1, lon2, lat2)  # 计算两点间距离并累加到总周长
    return total_perimeter  # 返回计算得到的总周长

# 输出结果并标记地图
colors = ['#1f77b4', '#2ca02c', '#d62728', '#ff7f0e']
color_index = 0

# 创建地图并添加测量控件
map_center = cleaned_data[['latitude', 'longitude']].mean().values  # 计算地图中心点
map = folium.Map(location=map_center, zoom_start=12)  # 创建地图

# 添加测量控件
measure_control = plugins.MeasureControl(
    position='topright',
    primary_length_unit='meters',
    primary_area_unit='sqmeters',
    active_color='red',
    completed_color='red',
    popup_options={'className': 'measure_popup'}
)
map.add_child(measure_control)

# 创建图层
work_layer = folium.FeatureGroup(name='工作状态')
non_work_layer = folium.FeatureGroup(name='非工作状态')
track_layer = folium.FeatureGroup(name='轨迹')
non_work_area_work_layer = folium.FeatureGroup(name='非区域工作状态')
area_layer = folium.FeatureGroup(name='工作区域面积')
polygon_layer = folium.FeatureGroup(name='工作区域边界')
triangle_layer = folium.FeatureGroup(name='三角剖分')

# 创建带方向的整体轨迹
locations = cleaned_data[['latitude', 'longitude']].values
plugins.AntPath(locations, color='black', weight=2, opacity=0.5, delay=10000, pulse_color='#F0F0F0').add_to(track_layer)

# 标记所有点，包括工作状态和非工作状态点
for i, row in cleaned_data.iterrows():
    coord = row[['latitude', 'longitude']].values
    if final_status[i] == 'Non-work':
        folium.CircleMarker(location=coord, radius=1, color='gray', fill=True, fill_color='gray', fill_opacity=0.4, opacity=0.1).add_to(non_work_layer)
    elif final_status[i] == 'Non-work-area-work':
        folium.CircleMarker(location=coord, radius=1, color='black', fill=True, fill_color='black', fill_opacity=0.4, opacity=0.1).add_to(non_work_area_work_layer)
    else:
        folium.CircleMarker(location=coord, radius=1, color='blue', fill=True, fill_color='blue', fill_opacity=0.4, opacity=0.1).add_to(work_layer)

# 遍历第一次计算的工作区域，进一步细化显示通过第二次验证的工作区域
color_index = 0  # 重新初始化 color_index，用于不同工作区域使用不同的颜色
for group_number, (start, end) in enumerate(work_start_end_1, start=1):
    start_location = cleaned_data.iloc[start][['latitude', 'longitude']].values
    end_location = cleaned_data.iloc[end][['latitude', 'longitude']].values

    sub_work_areas = second_pass_results[group_number - 1][2]

    if sub_work_areas:
        print(f"第一次计算工作区域 {group_number}: 起点坐标 {start_location} (索引 {start}), 终点坐标 {end_location} (索引 {end})")

        for sub_index, (sub_start, sub_end) in enumerate(sub_work_areas, start=1):
            if start + sub_start < len(cleaned_data) and start + sub_end < len(cleaned_data):
                sub_start_location = cleaned_data.iloc[start + sub_start][['latitude', 'longitude']].values
                sub_end_location = cleaned_data.iloc[start + sub_end][['latitude', 'longitude']].values
                sub_data = cleaned_data.iloc[start + sub_start:start + sub_end + 1]

                average_speed = sub_data['speed'].mean()
                point_interval = sub_data['time'].diff().dt.total_seconds().mean()

                print(f"  第二次计算工作区域 {group_number}.{sub_index}: 起点坐标 {sub_start_location} (索引 {start + sub_start}), 终点坐标 {sub_end_location} (索引 {start + sub_end}), 平均速度: {average_speed:.3f} 公里/小时, 点间隔: {point_interval:.2f} 秒")

                for i in range(start + sub_start, start + sub_end + 1):
                    coord = cleaned_data.iloc[i][['latitude', 'longitude']].values
                    color = colors[(color_index + sub_index - 1) % len(colors)]  # 为子区域分配不同颜色
                    folium.CircleMarker(location=coord, radius=5, color=color, fill=True, fill_color=color, fill_opacity=0.4, opacity=0.4).add_to(work_layer)

                # 添加以下代码块，计算并标记多边形
                points = sub_data[['longitude', 'latitude']].to_numpy()  # 提取经纬度数据
                if len(points) > 2:  # 检查点数是否足够构成多边形
                    tri = Delaunay(points)  # 进行Delaunay三角剖分
                    valid_triangles = [t for t in tri.simplices if valid_triangle(t, points)]  # 筛选有效三角形
                    outer_edges = extract_outer_edges(valid_triangles, points)  # 提取最外圈边
                    edge_lines = [LineString([points[edge[0]], points[edge[1]]]) for edge in outer_edges]  # 创建边线
                    merged_lines = unary_union(edge_lines)  # 合并边线
                    polygons = list(polygonize(merged_lines))  # 生成多边形
                    if polygons:  # 检查多边形列表是否为空
                        outer_polygon = max(polygons, key=lambda p: p.area)  # 取最大的多边形
                        outer_polygon_coords = list(outer_polygon.exterior.coords)  # 获取多边形顶点坐标
                        # 计算并显示面积
                        outer_polygon_area_m2 = calculate_spherical_polygon_area(outer_polygon_coords)  # 计算包围面积
                        coverage_area = sum(calculate_spherical_polygon_area(points[tri]) for tri in valid_triangles)  # 计算覆盖面积

                        folium.Polygon(locations=[(lat, lon) for lon, lat in outer_polygon_coords], color=color, weight=5, opacity=0.8).add_to(polygon_layer)  # 在地图上显示多边形
                        folium.Marker(location=sub_start_location, icon=folium.DivIcon(html=f'<div style="font-size: 10pt; color : black">{group_number}.{sub_index}S</div>')).add_to(work_layer)  # 标记起点
                        folium.Marker(location=sub_end_location, icon=folium.DivIcon(html=f'<div style="font-size: 10pt; color : black">{group_number}.{sub_index}E</div>')).add_to(work_layer)  # 标记终点

                        # 改进后的面积标记
                        folium.Marker(
                            location=np.mean(points, axis=0)[::-1],
                            icon=folium.DivIcon(html=f'''
                                <div style="font-size: 12pt; color: {color}; white-space: nowrap;">
                                    覆盖面积: {coverage_area:.2f} m²<br>包围面积: {outer_polygon_area_m2:.2f} m²
                                </div>
                            ''')
                        ).add_to(area_layer)  # 在多边形中间显示面积

                        # 添加以下打印语句
                        print(f"包围面积: {outer_polygon_area_m2:.2f} 平方米")
                        print(f"覆盖面积: {coverage_area:.2f} 平方米")

                        # 处理内边界
                        for poly in polygons:
                            for interior in poly.interiors:
                                inner_coords = list(interior.coords)
                                folium.Polygon(locations=[(lat, lon) for lon, lat in inner_coords], color='red', weight=3, opacity=0.5, dash_array='5, 10').add_to(polygon_layer)  # 显示内边界

                        # 添加三角剖分结果到图层
                        for triangle in valid_triangles:
                            triangle_coords = [(lat, lon) for lon, lat in points[triangle]]
                            folium.Polygon(locations=triangle_coords, color='green', weight=1, opacity=0.2).add_to(triangle_layer)
                    else:
                        print("No valid polygons found for this area.")  # 添加空多边形检查
        color_index += 1  # 在每个工作区域结束后增加 color_index，以便下一个工作区域使用不同的颜色

# 添加各图层到地图
work_layer.add_to(map)
non_work_layer.add_to(map)
non_work_area_work_layer.add_to(map)
track_layer.add_to(map)
area_layer.add_to(map)
polygon_layer.add_to(map)
triangle_layer.add_to(map)

# 添加图层控制
folium.LayerControl().add_to(map)

# 获取当前时间并格式化为字符串
current_time_str = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')

# 生成动态文件名
input_filename = '20240511.csv'
base_filename = input_filename.split('.')[0]
output_filename = f"{base_filename}_{current_time_str}.html"

# 保存地图到动态生成的文件名
map.save(output_filename)

# 记录结束时间并打印运行时间
end_time = time.time()
elapsed_time = end_time - start_time
print(f"代码运行时间：{elapsed_time:.2f} 秒")

# 计算并打印统计和分析结果
total_distance_km = cleaned_data['distance'].sum()  # 计算总距离
average_distance_km = cleaned_data['distance'].mean()  # 计算平均每点距离
max_speed = cleaned_data['speed'].max()  # 计算最大速度

print(f"总距离: {total_distance_km:.3f} 公里")
print(f"平均每点距离: {average_distance_km:.5f} 公里")
print(f"最大速度: {max_speed:.3f} 公里/小时")  # 添加最大速度输出
print(cleaned_data['status'].value_counts())

# 如果数据中包含时间戳，我们可以计算总时长
if 'time' in cleaned_data.columns:
    cleaned_data['time'] = pd.to_datetime(cleaned_data['time'], format='%Y-%m-%d %H:%M:%S')
    total_time_seconds = (cleaned_data['time'].iloc[-1] - cleaned_data['time'].iloc[0]).total_seconds()
    total_time_hours = total_time_seconds / 3600
    print(f"总时长: {total_time_hours:.3f} 小时")
    average_speed_kmh = total_distance_km / total_time_hours
    print(f"平均速度: {average_speed_kmh:.3f} 公里/小时")
else:
    print("没有时间数据无法计算总时长。")

# 计算每个坐标点到其最近邻点的距离
import numpy as np
from scipy.spatial import KDTree

def calculate_average_nearest_distance(coords):
    tree = KDTree(coords)
    distances, indices = tree.query(coords, k=2)  # k=2 because the nearest neighbor is the point itself
    nearest_distances = []

    for i in range(len(coords)):
        j = indices[i][1]  # index of the nearest neighbor
        nearest_distance = haversine(coords[i][1], coords[i][0], coords[j][1], coords[j][0])
        nearest_distances.append(nearest_distance)

    return np.mean(nearest_distances)

coords = cleaned_data[['latitude', 'longitude']].to_numpy()
average_nearest_distance = calculate_average_nearest_distance(coords)

print(f"所有坐标点到最近邻点的平均间距为: {average_nearest_distance:.2f} 米")
