import pandas as pd
import numpy as np
from datetime import datetime
from haversine import haversine
from sklearn.neighbors import NearestNeighbors
import folium
from folium import plugins

# 第一部分: 数据清洗
data = pd.read_csv('0928_cleaned-2.csv')
# 确保数据按照时间戳排序
data.sort_values(by='time', inplace=True)

def calculate_time_delta(time1, time2):
    dt_format = '%Y%m%d%H%M%S'
    time1_str = str(time1).split('.')[0]
    time2_str = str(time2).split('.')[0]
    datetime1 = datetime.strptime(time1_str, dt_format)
    datetime2 = datetime.strptime(time2_str, dt_format)
    # 计算两个时间点之间的总秒数
    time_delta_seconds = (datetime2 - datetime1).total_seconds()
    return time_delta_seconds / 3600  # 返回小时单位的时间差

def calculate_speed(data):
    speeds = []
    for i in range(len(data) - 1):
        loc1 = (data.iloc[i]['latitude'], data.iloc[i]['longitude'])
        loc2 = (data.iloc[i + 1]['latitude'], data.iloc[i + 1]['longitude'])
        distance = haversine(loc1, loc2)  # 单位：公里
        time_delta_hours = calculate_time_delta(data.iloc[i]['time'], data.iloc[i + 1]['time'])  # 单位：小时
        speed = distance / time_delta_hours if time_delta_hours else 0  # 单位：公里/小时
        speeds.append(speed)
    speeds.append(0)  # 给最后一个点赋速度值0，因为没有下一个点进行计算
    return speeds

# 计算每个点的速度
data['speed'] = calculate_speed(data)

# 筛选出速度在2到20公里/小时之间的数据点
# 使用.copy()确保操作的是DataFrame的副本
cleaned_data = data[(data['speed'] >= 2) & (data['speed'] <= 15)].copy()
cleaned_data.reset_index(drop=True, inplace=True)  # 重置索引
# cleaned_data.to_csv('1014_cleaned.csv', index=False)

# 删除任何包含NaN值的行，以确保地图可视化时不会出错
cleaned_data.dropna(subset=['latitude', 'longitude'], inplace=True)
cleaned_data.sort_values(by='time', inplace=True)

# 导出清洗后的数据到CSV文件
cleaned_data.to_csv('0928_cleaned.csv', index=False)

def calculate_distances(data):
    distances = []
    for i in range(len(data) - 1):
        loc1 = (data.iloc[i]['latitude'], data.iloc[i]['longitude'])
        loc2 = (data.iloc[i + 1]['latitude'], data.iloc[i + 1]['longitude'])
        distance = haversine(loc1, loc2)
        distances.append(distance)
    return distances

# 计算距离并计算总距离、平均距离
distances = calculate_distances(cleaned_data)
total_distance_km = np.sum(distances)
average_distance_km = np.round(np.mean(distances), 5)

# 转换经纬度为弧度用于距离计算
# 将经纬度数据转换为弧度，用于距离计算
coords = np.radians(cleaned_data[['latitude', 'longitude']])

from sklearn.neighbors import RadiusNeighborsClassifier

# 设定半径范围（公里）并转换为弧度
radius_km = 0.03  # 设定你需要的范围（比如 0.01 公里）
earth_radius_km = 6371  # 地球平均半径
radius_radians = radius_km / earth_radius_km

# **添加初始化代码**：确保 `status` 列存在
cleaned_data['status'] = 'Non-work'

# 使用 RadiusNeighborsClassifier 来查找指定半径范围内的邻居
neigh = RadiusNeighborsClassifier(radius=radius_radians, metric='haversine', outlier_label='Non-work')
neigh.fit(coords, cleaned_data['status'])

# 设定邻居数量阈值
neighbor_threshold = 10  # 设定需要的邻居数量

# 获取每个点的邻居数量，并标记状态
distances, indices = neigh.radius_neighbors(coords)
cleaned_data['status'] = ['Work' if len(neighbors) >= neighbor_threshold else 'Non-work' for neighbors in indices]


# 设置连续工作状态的最小阈值和内缩的点数
work_threshold = 10  # 设置阈值为n
offset = 0  # 设置内缩n个点

# 新增：识别连续工作区域，并考虑内缩和阈值
work_start_end = []
current_start = None
adjusted_points = set()

group_number = 0  # 添加这行来初始化颜色索引计数器
for i, row in cleaned_data.iterrows():
    if row['status'] == 'Work':
        if current_start is None:
            current_start = i
    else:
        if current_start is not None:
            work_length = i - current_start
            if work_length >= work_threshold:
                start_index = max(current_start + offset, 0)
                end_index = min(i - offset - 1, len(cleaned_data) - 1)
                work_start_end.append((start_index, end_index, group_number))  # 修改这里，添加 group_number
                group_number += 1  # 更新颜色索引
                adjusted_points.update(range(current_start, start_index))
                adjusted_points.update(range(end_index + 1, i))
            else:
                adjusted_points.update(range(current_start, i))
            current_start = None

if current_start is not None and (len(cleaned_data) - current_start) >= work_threshold:
    start_index = max(current_start + offset, 0)
    end_index = min(len(cleaned_data) - offset - 1, len(cleaned_data) - 1)
    work_start_end.append((start_index, end_index, group_number))
    group_number += 1
    adjusted_points.update(range(current_start, start_index))
    adjusted_points.update(range(end_index + 1, len(cleaned_data)))


# 检查最后一段工作区域是否满足阈值条件
if current_start is not None and (len(cleaned_data) - current_start) >= work_threshold:
    start_index = max(current_start + offset, 0)
    end_index = min(len(cleaned_data) - offset - 1, len(cleaned_data) - 1)
    work_start_end.append((start_index, end_index))
    adjusted_points.update(range(current_start, start_index))
    adjusted_points.update(range(end_index + 1, len(cleaned_data)))

# 定义两种颜色用于标记奇数和偶数工作区域的坐标点
colors = ['green', 'orange']

# 更新点的状态：标记那些被内缩或阈值调整的点
cleaned_data['adjusted'] = False
for i in range(len(cleaned_data)):
    if i in adjusted_points:
        cleaned_data.at[i, 'adjusted'] = True

# 删除任何包含NaN值的行，以确保地图可视化时不会出错
cleaned_data.dropna(subset=['latitude', 'longitude'], inplace=True)
locations = cleaned_data[['latitude', 'longitude']].values

# 初始化地图
map_center = cleaned_data[['latitude', 'longitude']].mean().values
map = folium.Map(location=map_center, zoom_start=12)

# 创建带方向的轨迹
plugins.AntPath(locations, color='green', weight=2, opacity=1, delay=5000).add_to(map)

# 标记整个坐标集的起点和终点
folium.CircleMarker(location=locations[0], radius=10, color='black', fill=True, fill_color='green', fill_opacity=1).add_to(map)
folium.CircleMarker(location=locations[-1], radius=10, color='black', fill=True, fill_color='red', fill_opacity=1).add_to(map)

# 在地图上标记点，保持奇偶数工作区域颜色区分，同时区分工作、非工作及调整状态
for i, row in cleaned_data.iterrows():
    if row['adjusted']:
        color = 'black'
    elif row['status'] == 'Work':
        for start, end, color_index in work_start_end:
            if start <= i <= end:
                color = colors[color_index % 2]  # 使用颜色索引决定颜色
                break
        else:
            color = 'red'  # 默认非工作状态颜色
    else:
        color = 'red'

    folium.CircleMarker(location=(row['latitude'], row['longitude']), radius=3, color=color, fill=True).add_to(map)


# 标记每个连续工作地块的缩进后的起点和终点
group_number = 1
for start, end, color_index in work_start_end:
    start_location = cleaned_data.iloc[start][['latitude', 'longitude']].values
    end_location = cleaned_data.iloc[end][['latitude', 'longitude']].values
    # 标记起点
    folium.Marker(start_location, icon=folium.DivIcon(html=f'<div style="font-size: 15pt; color : red">S{group_number}:{start}</div>')).add_to(map)
    # 标记终点
    folium.Marker(end_location, icon=folium.DivIcon(html=f'<div style="font-size: 15pt; color : blue">E{group_number}:{end}</div>')).add_to(map)
    group_number += 1

map.save('0928_cleaned-2.html')

# 打印统计和分析结果
print(f"总距离: {total_distance_km:.3f} 公里")
print(f"平均每点距离: {average_distance_km:.5f} 公里")
print(cleaned_data['status'].value_counts())

for group_number, (start, end, color_index) in enumerate(work_start_end, start=1):
    start_location = cleaned_data.iloc[start][['latitude', 'longitude']].values
    end_location = cleaned_data.iloc[end][['latitude', 'longitude']].values
    print(f"工作区域 {group_number}: 起点坐标 {start_location} (索引 {start}), 终点坐标 {end_location} (索引 {end})")

# 如果数据中包含时间戳，我们可以计算总时长
if 'time' in cleaned_data.columns:
    cleaned_data['time'] = pd.to_datetime(cleaned_data['time'], format='%Y%m%d%H%M%S')
    total_time_seconds = (cleaned_data['time'].iloc[-1] - cleaned_data['time'].iloc[0]).total_seconds()
    total_time_hours = total_time_seconds / 3600
    print(f"总时长: {total_time_hours:.3f} 小时")
    average_speed_kmh = cleaned_data['speed'].mean()
    print(f"平均速度: {average_speed_kmh:.3f} 公里/小时")
else:
    print("没有时间数据无法计算总时长。")
