import pandas as pd
import numpy as np
from haversine import haversine
import folium
from folium import plugins
import time
from folium.plugins import Fullscreen, MeasureControl, MarkerCluster, HeatMap

# 阈值设置
SPEED_MIN = 2
SPEED_MAX = 15
NEIGHBOR_THRESHOLD = 20
WORK_THRESHOLD = 30
OFFSET_POINTS = 0
RADIUS_KM = 0.07
EARTH_RADIUS_KM = 6371

# 第一部分: 数据清洗
data = pd.read_csv('m_866214077648004_20240512.csv')
# 确保数据按照时间戳排序
data.sort_values(by='time', inplace=True)
# 记录开始时间
start_time = time.time()

def vectorized_haversine(lat1, lon1, lat2, lon2):
    lat1, lon1, lat2, lon2 = map(np.radians, [lat1, lon1, lat2, lon2])
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = np.sin(dlat/2.0)**2 + np.cos(lat1) * np.cos(lat2) * np.sin(dlon/2.0)**2
    c = 2 * np.arcsin(np.sqrt(a))
    km = 6371 * c  # Multiply by Earth radius to get kilometers
    return km

# 使用优化后的计算距离函数
def calculate_speed_optimized(data):
    data['time'] = pd.to_datetime(data['time'])
    data['latitude_shifted'] = data['latitude'].shift(-1)
    data['longitude_shifted'] = data['longitude'].shift(-1)
    data['distance'] = vectorized_haversine(data['latitude'], data['longitude'],
                                            data['latitude_shifted'], data['longitude_shifted'])
    time_deltas = data['time'].diff().dt.total_seconds() / 3600
    data['speed'] = data['distance'] / time_deltas
    data['speed'] = data['speed'].fillna(0)
    data.drop(['latitude_shifted', 'longitude_shifted', 'distance'], axis=1, inplace=True)
    return data

# 使用优化后的计算速度函数
data = calculate_speed_optimized(data)

def optimize_data_cleaning(data):
    # 筛选速度范围并直接重置索引，避免使用额外的.copy()操作
    cleaned_data = data[(data['speed'] >= SPEED_MIN) & (data['speed'] <= SPEED_MAX)].reset_index(drop=True)
    # 删除NaN值
    cleaned_data = cleaned_data.dropna(subset=['latitude', 'longitude'])
    # 导出数据到CSV
    cleaned_data.to_csv('20240511_cleaned.csv', index=False)
    return cleaned_data


# 使用优化的数据清洗函数
cleaned_data = optimize_data_cleaning(data)

radius_radians = RADIUS_KM / EARTH_RADIUS_KM

def calculate_distances(data):
    distances = []
    for i in range(len(data) - 1):
        loc1 = (data.iloc[i]['latitude'], data.iloc[i]['longitude'])
        loc2 = (data.iloc[i + 1]['latitude'], data.iloc[i + 1]['longitude'])
        distance = haversine(loc1, loc2)
        distances.append(distance)
    return distances

# 计算距离并计算总距离、平均距离
distances = calculate_distances(cleaned_data)
total_distance_km = np.sum(distances)
average_distance_km = np.round(np.mean(distances), 5)

# 转换经纬度为弧度用于距离计算
# 将经纬度数据转换为弧度，用于距离计算
coords = np.radians(cleaned_data[['latitude', 'longitude']])

from sklearn.neighbors import RadiusNeighborsClassifier

# **添加初始化代码**：确保 `status` 列存在
cleaned_data['status'] = 'Non-work'

# 使用 RadiusNeighborsClassifier 来查找指定半径范围内的邻居
neigh = RadiusNeighborsClassifier(radius=radius_radians, metric='haversine', outlier_label='Non-work')
neigh.fit(coords, cleaned_data['status'])

# 获取每个点的邻居数量，并标记状态
distances, indices = neigh.radius_neighbors(coords)
cleaned_data['status'] = ['Work' if len(neighbors) >= NEIGHBOR_THRESHOLD else 'Non-work' for neighbors in indices]

# 新增：识别连续工作区域，并考虑内缩和阈值
work_start_end = []
current_start = None
adjusted_points = set()

group_number = 0  # 添加这行来初始化颜色索引计数器
for i, row in cleaned_data.iterrows():
    if row['status'] == 'Work':
        if current_start is None:
            current_start = i
    else:
        if current_start is not None:
            work_length = i - current_start
            if work_length >= WORK_THRESHOLD:
                start_index = max(current_start + OFFSET_POINTS, 0)
                end_index = min(i - OFFSET_POINTS - 1, len(cleaned_data) - 1)
                work_start_end.append((start_index, end_index, group_number))  # 修改这里，添加 group_number
                group_number += 1  # 更新颜色索引
                adjusted_points.update(range(current_start, start_index))
                adjusted_points.update(range(end_index + 1, i))
            else:
                adjusted_points.update(range(current_start, i))
            current_start = None

# 检查最后一段工作区域是否满足阈值条件
if current_start is not None and (len(cleaned_data) - current_start) >= WORK_THRESHOLD:
    start_index = max(current_start + OFFSET_POINTS, 0)
    end_index = min(len(cleaned_data) - OFFSET_POINTS - 1, len(cleaned_data) - 1)
    work_start_end.append((start_index, end_index, group_number))  # 修改这里，添加 group_number
    adjusted_points.update(range(current_start, start_index))
    adjusted_points.update(range(end_index + 1, len(cleaned_data)))


# 定义两种颜色用于标记奇数和偶数工作区域的坐标点
colors = ['green', 'orange']

# 更新点的状态：标记那些被内缩或阈值调整的点
cleaned_data['adjusted'] = False
for i in range(len(cleaned_data)):
    if i in adjusted_points:
        cleaned_data.at[i, 'adjusted'] = True

# 删除任何包含NaN值的行，以确保地图可视化时不会出错
locations = cleaned_data[['latitude', 'longitude']].values

# 初始化地图
map_center = cleaned_data[['latitude', 'longitude']].mean().values
map = folium.Map(location=map_center, zoom_start=12)

# # # 创建带方向的轨迹
# plugins.AntPath(locations, color='green', weight=3, opacity=0.7, delay=20000, pulse_color='#F0F0F0').add_to(map)

# # 标记整个坐标集的起点和终点
# folium.CircleMarker(location=locations[0], radius=10, color='black', fill=True, fill_color='green', fill_opacity=1).add_to(map)
# folium.CircleMarker(location=locations[-1], radius=10, color='black', fill=True, fill_color='red', fill_opacity=1).add_to(map)

# 在地图上标记点，保持奇偶数工作区域颜色区分，同时区分工作、非工作及调整状态
for i, row in cleaned_data.iterrows():
    if row['adjusted']:
        color = 'black'
    elif row['status'] == 'Work':
        for start, end, color_index in work_start_end:
            if start <= i <= end:
                color = colors[color_index % 2]  # 使用颜色索引决定颜色
                break
        else:
            color = 'red'  # 默认非工作状态颜色
    else:
        color = 'red'

    folium.CircleMarker(location=(row['latitude'], row['longitude']), radius=3, color=color, fill=True).add_to(map)

# # 在连续工作区域显示热力图
# for start, end, _ in work_start_end:
#     work_area_coords = cleaned_data.iloc[start:end+1][['latitude', 'longitude']]
#     heat_data = work_area_coords.values.tolist()
#     if heat_data:  # 确保列表不为空
#         HeatMap(heat_data).add_to(map)

# # 创建 MarkerCluster 管理密集的点
# marker_cluster = MarkerCluster().add_to(map)
# for i, row in cleaned_data.iterrows():
#     folium.Marker(
#         location=[row['latitude'], row['longitude']],
#         icon=None,  # 可以自定义图标
#     ).add_to(marker_cluster)

# 标记每个连续工作地块的缩进后的起点和终点
# group_number = 1
# for start, end, color_index in work_start_end:
#     start_location = cleaned_data.iloc[start][['latitude', 'longitude']].values
#     end_location = cleaned_data.iloc[end][['latitude', 'longitude']].values
#     # 标记起点
#     folium.Marker(start_location, icon=folium.DivIcon(html=f'<div style="font-size: 15pt; color : red">S{group_number}:{start}</div>')).add_to(map)
#     # 标记终点
#     folium.Marker(end_location, icon=folium.DivIcon(html=f'<div style="font-size: 15pt; color : blue">E{group_number}:{end}</div>')).add_to(map)
#     group_number += 1


map.save('20240511.html')
# 记录结束时间并打印运行时间
end_time = time.time()
elapsed_time = end_time - start_time
print(f"代码运行时间：{elapsed_time:.2f} 秒")

# 打印统计和分析结果
print(f"总距离: {total_distance_km:.3f} 公里")
print(f"平均每点距离: {average_distance_km:.5f} 公里")
print(cleaned_data['status'].value_counts())

for group_number, (start, end, color_index) in enumerate(work_start_end, start=1):
    start_location = cleaned_data.iloc[start][['latitude', 'longitude']].values
    end_location = cleaned_data.iloc[end][['latitude', 'longitude']].values
    print(f"工作区域 {group_number}: 起点坐标 {start_location} (索引 {start}), 终点坐标 {end_location} (索引 {end})")

# 如果数据中包含时间戳，我们可以计算总时长
if 'time' in cleaned_data.columns:
    cleaned_data['time'] = pd.to_datetime(cleaned_data['time'], format='%Y-%m-%d %H:%M:%S')
    total_time_seconds = (cleaned_data['time'].iloc[-1] - cleaned_data['time'].iloc[0]).total_seconds()
    total_time_hours = total_time_seconds / 3600
    print(f"总时长: {total_time_hours:.3f} 小时")
    average_speed_kmh = cleaned_data['speed'].mean()
    print(f"平均速度: {average_speed_kmh:.3f} 公里/小时")
else:
    print("没有时间数据无法计算总时长。")
