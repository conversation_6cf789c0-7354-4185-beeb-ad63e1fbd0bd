import folium  # 导入 folium 库，用于创建地图
from folium.plugins import AntPath, MeasureControl, HeatMap, MarkerCluster  # 导入 folium 插件
import csv  # 导入 csv 库，用于读取 CSV 文件
import numpy as np  # 导入 numpy 库，用于数值计算
import os  # 导入 os 库，用于文件操作
import datetime  # 导入 datetime 库，用于获取当前日期和时间

# 定义函数，将 GCJ-02 坐标转换为 WGS-84 坐标
def gcj02_to_wgs84(lng, lat):
    a, ee = 6378245.0, 0.00669342162296594323  # 椭球参数
    dlat = _transformlat(lng - 105.0, lat - 35.0)
    dlng = _transformlng(lng - 105.0, lat - 35.0)
    radlat = lat / 180.0 * np.pi
    magic = np.sin(radlat)
    magic = 1 - ee * magic * magic
    sqrtmagic = np.sqrt(magic)
    dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * np.pi)
    dlng = (dlng * 180.0) / (a / sqrtmagic * np.cos(radlat) * np.pi)
    mglat, mglng = lat + dlat, lng + dlng
    return lng * 2 - mglng, lat * 2 - mglat  # 返回转换后的经纬度

# 定义辅助函数，用于转换纬度
def _transformlat(lng, lat):
    ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * np.sqrt(abs(lng))
    ret += (20.0 * np.sin(6.0 * lng * np.pi) + 20.0 * np.sin(2.0 * lng * np.pi)) * 2.0 / 3.0
    ret += (20.0 * np.sin(lat * np.pi) + 40.0 * np.sin(lat / 3.0 * np.pi)) * 2.0 / 3.0
    ret += (160.0 * np.sin(lat / 12.0 * np.pi) + 320 * np.sin(lat * np.pi / 30.0)) * 2.0 / 3.0
    return ret

# 定义辅助函数，用于转换经度
def _transformlng(lng, lat):
    ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * np.sqrt(abs(lng))
    ret += (20.0 * np.sin(6.0 * lng * np.pi) + 20.0 * np.sin(2.0 * lng * np.pi)) * 2.0 / 3.0
    ret += (20.0 * np.sin(lng * np.pi) + 40.0 * np.sin(lng / 3.0 * np.pi)) * 2.0 / 3.0
    ret += (150.0 * np.sin(lng / 12.0 * np.pi) + 300.0 * np.sin(lng / 30.0 * np.pi)) * 2.0 / 3.0
    return ret

# 从 CSV 文件中提取坐标数据，可选择是否进行坐标转换
def extract_coordinates_from_csv(file_path, convert_to_wgs84=False):
    coordinates = []
    with open(file_path, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)  # 读取 CSV 文件
        for row in reader:
            latitude, longitude = float(row['latitude']), float(row['longitude'])  # 提取经纬度
            if convert_to_wgs84:
                longitude, latitude = gcj02_to_wgs84(longitude, latitude)  # 进行坐标转换
            coordinates.append((latitude, longitude))  # 添加到坐标列表
    return coordinates  # 返回坐标列表

# 创建地图并绘制轨迹
def create_map_with_trajectory(coordinates, output_file_path):
    if not coordinates:  # 如果没有坐标数据，打印提示并返回
        print("No coordinates to plot.")
        return

    m = folium.Map(location=coordinates[0], zoom_start=12)  # 创建地图对象，初始视角设定为第一个坐标点

    folium.TileLayer(tiles='http://{s}.google.com/vt/lyrs=s&x={x}&y={y}&z={z}', attr='Google', name='Google卫星影像',
                     overlay=True, control=True, subdomains=['mt0', 'mt1', 'mt2', 'mt3'], max_zoom=20).add_to(m)  # 添加Google瓦片图层

    tian_di_tu_satellite_url = 'http://t0.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=e7f645439e09d5fdbc5158bacab6d024'
    folium.TileLayer(tiles=tian_di_tu_satellite_url, attr='天地图', name='天地图卫星影像', overlay=True, control=True, max_zoom=20).add_to(m)  # 添加天地图的卫星图层

    AntPath(locations=coordinates, color='blue', weight=3, opacity=0.7, delay=30000, pulse_color='#F0F0F0').add_to(m)  # 添加轨迹线

    m.add_child(MeasureControl(primary_length_unit='kilometers', primary_area_unit='sqmeters'))  # 添加测量控件

    heatmap_layer = folium.FeatureGroup(name='热力图', show=False)
    HeatMap(coordinates).add_to(heatmap_layer)
    heatmap_layer.add_to(m)  # 添加热力图层

    marker_cluster_layer = folium.FeatureGroup(name='聚合', show=False)
    marker_cluster = MarkerCluster().add_to(marker_cluster_layer)
    for coord in coordinates:
        folium.Marker(location=coord).add_to(marker_cluster)  # 添加聚合图层
    marker_cluster_layer.add_to(m)

    folium.LayerControl().add_to(m)  # 添加图层控制

    m.save(output_file_path)  # 保存地图到文件
    print(f"Map has been saved to {output_file_path}")  # 打印保存文件路径

csv_file_path = '20240511.csv'  # CSV 文件路径

base_name = os.path.splitext(os.path.basename(csv_file_path))[0]  # 获取文件名的基础名称
current_time = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")  # 获取当前时间，格式为 "年月日_时分秒"
output_map_path = f'{base_name}_{current_time}_trajectory_map.html'  # 动态生成 HTML 文件名

convert_to_wgs84 = True  # 设置为 True 进行坐标转换，设置为 False 不进行坐标转换

coordinates = extract_coordinates_from_csv(csv_file_path, convert_to_wgs84)  # 从 CSV 文件中提取坐标数据

create_map_with_trajectory(coordinates, output_map_path)  # 创建并保存轨迹地图
