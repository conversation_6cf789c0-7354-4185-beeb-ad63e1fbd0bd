import pandas as pd
import numpy as np
import folium
import pyproj
from folium import plugins
import time
from sklearn.neighbors import RadiusNeighborsClassifier
import datetime
from scipy.spatial import Delaunay
from shapely.ops import unary_union, polygonize
from shapely.geometry import Polygon, LineString
from math import radians, cos, sin, asin, sqrt
import builtins
from scipy.spatial import KDTree
from folium.plugins import MarkerCluster, HeatMap


# 地球半径常量
EARTH_RADIUS_KM = 6371
def trace_start_end_area():
    # 在函数外定义 input_filename 变量
    input_filename = '20240511.csv'
    # 数据清洗和初始计算
    data = pd.read_csv(input_filename)
    data.sort_values(by='time', inplace=True)
    start_time = time.time()
    # 转换所有坐标
    data[['longitude', 'latitude']] = data.apply(
        lambda row: pd.Series(gcj02_to_wgs84(row['longitude'], row['latitude'])), axis=1)
    # 使用优化后的计算速度函数
    data = calculate_speed_optimized(data)

    # 使用优化的数据清洗函数
    cleaned_data = optimize_data_cleaning(data, 2, 15)

    # 计算平均前后两点的间距
    average_distance_between_points = cleaned_data['distance'].mean()

    print(f"所有坐标点的平均前后间距为: {average_distance_between_points * 1000:.2f} 米")

    # 动态设定阈值
    SPEED_MIN_1 = 2
    SPEED_MAX_1 = 15
    NEIGHBOR_THRESHOLD_1 = 15
    WORK_THRESHOLD_1 = average_distance_between_points * 1000 * 4
    OFFSET_POINTS_1 = 20
    RADIUS_KM_1 = average_distance_between_points * 5
    DISTANCE_THRESHOLD = average_distance_between_points * 1000 * 20

    # 定义扩展边界的距离（以米为单位）
    BORDER_EXPANSION_M = 2  # 可以根据需要调整

    # 第二次计算的阈值
    SPEED_MIN_2 = 2
    SPEED_MAX_2 = 15
    NEIGHBOR_THRESHOLD_2 = 10
    WORK_THRESHOLD_2 = average_distance_between_points * 1000 * 4
    OFFSET_POINTS_2 = 0
    RADIUS_KM_2 = average_distance_between_points * 5
    INTERVAL_THRESHOLD = 5  # 点间隔阈值（秒）
    AVERAGE_SPEED_THRESHOLD = 2  # 平均速度阈值（公里/小时）

    # 三角法的最长边阈值
    MAX_EDGE_LENGTH_M = average_distance_between_points * 1000 * 3  # 单位: 米

    print(f"WORK_THRESHOLD_1 的动态值为: {WORK_THRESHOLD_1:.2f} ")
    print(f"RADIUS_KM_1 的动态值为: {RADIUS_KM_1:.5f} 公里")
    print(f"DISTANCE_THRESHOLD 的动态值为: {DISTANCE_THRESHOLD:.2f} 米")
    print(f"WORK_THRESHOLD_2 的动态值为: {WORK_THRESHOLD_2:.2f} ")
    print(f"RADIUS_KM_2 的动态值为: {RADIUS_KM_2:.5f} 公里")
    print(f"MAX_EDGE_LENGTH_M 的动态值为: {MAX_EDGE_LENGTH_M:.2f} 米")

    # 确保半径转换成弧度
    radius_radians_1 = RADIUS_KM_1 / EARTH_RADIUS_KM
    radius_radians_2 = RADIUS_KM_2 / EARTH_RADIUS_KM
    # 使用 RadiusNeighborsClassifier 来标记数据的工作状态
    cleaned_data['status'] = 'Non-work'
    cleaned_data = apply_neighbors_classifier(cleaned_data, radius_radians_1, NEIGHBOR_THRESHOLD_1,DISTANCE_THRESHOLD)

    # 添加调试信息，打印清洗前的数据量
    print(f"清洗前数据量: {len(data)}")

    # 添加调试信息，打印清洗后的数据量
    print(f"清洗后数据量: {len(cleaned_data)}")
    # 第一次计算识别工作区域
    work_start_end_1, adjusted_points_1 = identify_work_areas(cleaned_data, WORK_THRESHOLD_1,
                                                              OFFSET_POINTS_1)  # 调用函数识别工作区域

    # 初始化状态为非工作区域
    cleaned_data['region_status'] = 'Non-work-region'

    # 标记工作区域
    for start, end in work_start_end_1:
        cleaned_data.loc[start:end, 'region_status'] = 'Work-region'

    # 创建工作区域和非工作区域的轨迹列表
    work_areas = []
    non_work_areas = []
    previous_end = 0

    for start, end in work_start_end_1:
        work_areas.append(cleaned_data.iloc[start:end + 1][['latitude', 'longitude']].values.tolist())
        if previous_end < start - 1:
            non_work_areas.append(cleaned_data.iloc[previous_end:start][['latitude', 'longitude']].values.tolist())
        previous_end = end + 1

    if previous_end < len(cleaned_data) - 1:
        non_work_areas.append(cleaned_data.iloc[previous_end:][['latitude', 'longitude']].values.tolist())

    # 第二次计算逻辑
    radius_radians_2 = RADIUS_KM_2 / EARTH_RADIUS_KM  # 将第二次计算使用的半径从公里转换为弧度
    second_pass_results = []  # 初始化一个列表，用于存储第二次计算的结果
    final_status = ['Unknown'] * len(cleaned_data)  # 用于记录每个点的最终状态

    for start, end in work_start_end_1:  # 遍历第一次计算识别的每个工作区域
        sub_data = cleaned_data.iloc[start:end + 1].copy()  # 提取当前工作区域的数据，并创建其副本
        sub_data = optimize_data_cleaning(sub_data, SPEED_MIN_2, SPEED_MAX_2)  # 对提取的数据进行进一步清洗，使用第二次计算的速度范围

        if len(sub_data) > 0:  # 检查清洗后的数据点是否足够多
            sub_data['status'] = 'Non-work'  # 初始化所有数据点的状态为“Non-work”
            sub_data = apply_neighbors_classifier(sub_data, radius_radians_2,
                                                  NEIGHBOR_THRESHOLD_2,DISTANCE_THRESHOLD)  # 应用邻居分类器，根据邻居数量更新每个点的状态
            sub_work_start_end, _ = identify_work_areas(sub_data, WORK_THRESHOLD_2, OFFSET_POINTS_2)  # 识别清洗后数据中的工作区域

            # 过滤掉不符合点间隔和平均速度条件的区域
            valid_sub_work_areas = []
            for sub_start, sub_end in sub_work_start_end:
                sub_area_data = sub_data.iloc[sub_start:sub_end + 1]
                average_speed = sub_area_data['speed'].mean()
                point_interval = sub_area_data['time'].diff().dt.total_seconds().mean()
                if point_interval <= INTERVAL_THRESHOLD and average_speed >= AVERAGE_SPEED_THRESHOLD:
                    valid_sub_work_areas.append((sub_start, sub_end))

            second_pass_results.append((start, end, valid_sub_work_areas))  # 将当前工作区域的结果添加到第二次计算结果列表中

            # 标记通过第二次验证的点为 'Work'
            for sub_start, sub_end in valid_sub_work_areas:
                for i in range(start + sub_start, start + sub_end + 1):
                    final_status[i] = 'Work'
            # 标记未通过第二次验证的点为 'Non-work-area-work'
            for i in range(start, end + 1):
                if final_status[i] == 'Unknown':
                    final_status[i] = 'Non-work-area-work'
        else:
            second_pass_results.append((start, end, []))  # 如果数据点不够多，添加空结果以保持结构一致性
            for i in range(start, end + 1):
                final_status[i] = 'Non-work-area-work'

    # 标记所有非工作状态的点
    for i, row in cleaned_data.iterrows():
        if final_status[i] == 'Unknown':
            final_status[i] = 'Non-work'

    # 输出结果并标记地图
    # colors = ['#1f77b4', '#2ca02c', '#d62728', '#ff7f0e']
    # colors = ['#98FB98', '#20B2AA', '#6B8E23', '#FFD700', '#FFA07A', '#9370DB']
    colors = ['#FF4500', '#32CD32', '#1E90FF', '#FFD700', '#FF69B4', '#8A2BE2']

    color_index = 0

    # 创建地图并添加测量控件
    map_center = cleaned_data[['latitude', 'longitude']].mean().values  # 计算地图中心点
    map = folium.Map(location=map_center, zoom_start=12)  # 创建地图

    # 添加 Google 瓦片图层，设置 max_zoom 为 20
    folium.TileLayer(
        tiles='http://{s}.google.com/vt/lyrs=s&x={x}&y={y}&z={z}',
        attr='Google',
        name='Google卫星影像',
        overlay=True,
        control=True,
        subdomains=['mt0', 'mt1', 'mt2', 'mt3'],
        max_zoom=20  # 设置最大缩放级别为 20
    ).add_to(map)

    # 定义天地图的卫星图层 URL
    tian_di_tu_satellite_url = 'http://t0.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=e7f645439e09d5fdbc5158bacab6d024'

    # 创建地图并添加天地图的卫星图层
    folium.TileLayer(
        tiles=tian_di_tu_satellite_url,
        attr='天地图',
        name='天地图卫星影像',
        overlay=True,  # 确保这里设置为 True，以正确叠加图层
        control=True,
        max_zoom=20  # 设置最大缩放级别为 20
    ).add_to(map)

    # 添加测量控件
    measure_control = plugins.MeasureControl(
        position='topright',
        primary_length_unit='meters',
        primary_area_unit='sqmeters',
        active_color='red',
        completed_color='red',
        popup_options={'className': 'measure_popup'}
    )
    map.add_child(measure_control)

    # 创建图层
    work_layer = folium.FeatureGroup(name='坐标点', show=False)
    non_work_layer = folium.FeatureGroup(name='非工作状态', show=False)
    track_layer = folium.FeatureGroup(name='完整轨迹', show=False)
    non_work_area_work_layer = folium.FeatureGroup(name='非区域工作状态', show=False)
    area_layer = folium.FeatureGroup(name='工作区域面积', show=False)
    polygon_layer = folium.FeatureGroup(name='工作区域边界', show=True)
    triangle_layer = folium.FeatureGroup(name='三角剖分', show=False)

    marker_cluster_layer = folium.FeatureGroup(name='聚合', show=False)
    marker_cluster = MarkerCluster().add_to(marker_cluster_layer)
    for i, row in cleaned_data.iterrows():
        folium.Marker(
            location=[row['latitude'], row['longitude']],
            icon=None,  # 可以自定义图标
        ).add_to(marker_cluster)

    # 创建 HeatMap 图层
    heatmap_layer = folium.FeatureGroup(name='热力图', show=False)
    for start, end in work_start_end_1:
        work_area_coords = cleaned_data.iloc[start:end + 1][['latitude', 'longitude']]
        heat_data = work_area_coords.values.tolist()
        if heat_data:  # 确保列表不为空
            HeatMap(heat_data).add_to(heatmap_layer)

    work_track_layer = folium.FeatureGroup(name='作业轨迹', show=False)
    non_work_track_layer = folium.FeatureGroup(name='非作业轨迹', show=False)

    # 添加工作区域轨迹
    for work_locations in work_areas:
        if len(work_locations) > 1:
            plugins.AntPath(locations=work_locations, color='green', weight=3, opacity=0.7, delay=30000,
                            pulse_color='#F0F0F0', dash_array=[10, 30]).add_to(work_track_layer)

    # 添加非工作区域轨迹
    for non_work_locations in non_work_areas:
        if len(non_work_locations) > 1:
            plugins.AntPath(non_work_locations, color='blue', weight=3, opacity=0.7, delay=15000,
                            pulse_color='#F0F0F0').add_to(non_work_track_layer)

    # 创建带方向的整体轨迹
    locations = cleaned_data[['latitude', 'longitude']].values
    plugins.AntPath(locations, color='blue', weight=3, opacity=0.7, delay=30000, pulse_color='#F0F0F0').add_to(
        track_layer)

    # 标记所有点，包括工作状态和非工作状态点
    for i, row in cleaned_data.iterrows():
        coord = row[['latitude', 'longitude']].values
        if final_status[i] == 'Non-work':
            folium.CircleMarker(location=coord, radius=1, color='gray', fill=True, fill_color='gray', fill_opacity=0.5,
                                opacity=0.4).add_to(non_work_layer)
        elif final_status[i] == 'Non-work-area-work':
            folium.CircleMarker(location=coord, radius=1, color='black', fill=True, fill_color='black',
                                fill_opacity=0.5, opacity=0.4).add_to(non_work_area_work_layer)
        # else:
        #     folium.CircleMarker(location=coord, radius=1, color='blue', fill=True, fill_color='blue', fill_opacity=0.5, opacity=0.4).add_to(work_layer)

    # 准备显示地块编号和面积信息的 HTML 内容
    html_content = '<div style="font-size: 14px;"><h4>地块编号和面积信息</h4>'
    area_counter = 1  # 初始化地块编号计数器

    # 遍历第一次计算的工作区域，进一步细化显示通过第二次验证的工作区域
    color_index = 0  # 重新初始化 color_index，用于不同工作区域使用不同的颜色
    label_layer = folium.FeatureGroup(name='工作区域编号', show=True)  # 添加标记图层
    area_layer = folium.FeatureGroup(name='工作区域面积', show=True)  # 确保面积图层可见
    area_counter = 1  # 初始化区域计数器
    for group_number, (start, end) in enumerate(work_start_end_1, start=1):
        start_location = cleaned_data.iloc[start][['latitude', 'longitude']].values
        end_location = cleaned_data.iloc[end][['latitude', 'longitude']].values

        sub_work_areas = second_pass_results[group_number - 1][2]

        if sub_work_areas:
            print(
                f"第一次计算工作区域 {group_number}: 起点坐标 {start_location} (索引 {start}), 终点坐标 {end_location} (索引 {end})")

            for sub_index, (sub_start, sub_end) in enumerate(sub_work_areas, start=1):
                if start + sub_start < len(cleaned_data) and start + sub_end < len(cleaned_data):
                    sub_start_location = cleaned_data.iloc[start + sub_start][['latitude', 'longitude']].values
                    sub_end_location = cleaned_data.iloc[start + sub_end][['latitude', 'longitude']].values
                    sub_data = cleaned_data.iloc[start + sub_start:start + sub_end + 1]

                    average_speed = sub_data['speed'].mean()
                    point_interval = sub_data['time'].diff().dt.total_seconds().mean()
                    # 计算前后点的平均间隔
                    distances = []
                    for i in range(1, len(sub_data)):
                        lat1, lon1 = sub_data.iloc[i - 1][['latitude', 'longitude']].values
                        lat2, lon2 = sub_data.iloc[i][['latitude', 'longitude']].values
                        distance = haversine(lon1, lat1, lon2, lat2)
                        distances.append(distance)
                    average_distance = np.mean(distances) if distances else 0

                    # 使用 KDTree 计算空间密度
                    coords = sub_data[['latitude', 'longitude']].to_numpy()
                    average_nearest_distance = calculate_average_nearest_distance(coords)

                    # 计算每个点10米范围内的邻居点数
                    average_neighbors_within_10m = calculate_neighbors_within_radius(sub_data, 10)

                    print(
                        f"  第二次计算工作区域 {group_number}.{sub_index}: 起点坐标 {sub_start_location} (索引 {start + sub_start}), 终点坐标 {sub_end_location} (索引 {start + sub_end}), 平均速度: {average_speed:.3f} 公里/小时, 点间隔: {point_interval:.2f} 秒, 前后点平均间隔: {average_distance:.2f} 米, 工作区域密度: {average_nearest_distance:.2f} 米, 10米范围内平均邻居点数: {average_neighbors_within_10m:.2f}")

                    for i in range(start + sub_start, start + sub_end + 1):
                        coord = cleaned_data.iloc[i][['latitude', 'longitude']].values
                        color = colors[(color_index + sub_index - 1) % len(colors)]  # 为子区域分配不同颜色
                        folium.CircleMarker(location=coord, radius=3, color=color, fill=True, fill_color=color,
                                            fill_opacity=0.4, opacity=0.4).add_to(work_layer)

                    # 定义一个函数，将经纬度坐标转换为平面坐标
                    def lonlat_to_utm(lon, lat):
                        proj_utm = pyproj.Proj(proj='utm', zone=33, ellps='WGS84')
                        return proj_utm(lon, lat)

                    # 定义一个函数，将平面坐标转换为经纬度坐标
                    def utm_to_lonlat(x, y):
                        proj_utm = pyproj.Proj(proj='utm', zone=33, ellps='WGS84')
                        return proj_utm(x, y, inverse=True)

                    # 添加以下代码块，计算并标记多边形
                    points = sub_data[['longitude', 'latitude']].to_numpy()  # 提取经纬度数据
                    if len(points) > 2:  # 检查点数是否足够构成多边形
                        tri = Delaunay(points)  # 进行Delaunay三角剖分
                        valid_triangles = [t for t in tri.simplices if
                                           valid_triangle(t, points, MAX_EDGE_LENGTH_M)]  # 筛选有效三角形
                        outer_edges = extract_outer_edges(valid_triangles, points)  # 提取最外圈边
                        edge_lines = [LineString([points[edge[0]], points[edge[1]]]) for edge in outer_edges]  # 创建边线
                        merged_lines = unary_union(edge_lines)  # 合并边线
                        polygons = list(polygonize(merged_lines))  # 生成多边形
                        if polygons:  # 检查多边形列表是否为空
                            outer_polygon = max(polygons, key=lambda p: p.area)  # 从所有多边形中取面积最大的多边形
                            outer_polygon_coords = list(outer_polygon.exterior.coords)  # 获取最大的多边形的顶点坐标列表

                            # 计算该多边形的包围面积（m²），使用地球半径转换为米
                            outer_polygon_area_m2 = calculate_spherical_polygon_area(outer_polygon_coords,EARTH_RADIUS_KM * 1000)

                            # 计算覆盖面积，累加所有有效三角形的面积
                            coverage_area = sum(calculate_spherical_polygon_area(points[tri], EARTH_RADIUS_KM * 1000) for tri in valid_triangles)

                            # 在地图上绘制外圈边界，设置颜色、线条宽度、不透明度等属性
                            folium.Polygon(locations=[(lat, lon) for lon, lat in outer_polygon_coords], color=color,weight=3, opacity=0.8, fill=True, fill_opacity=0).add_to(polygon_layer)

                            # 将多边形的经纬度坐标转换为UTM坐标系
                            outer_polygon_utm_coords = [lonlat_to_utm(lon, lat) for lon, lat in outer_polygon_coords]

                            # 扩展多边形的边界，并将扩展后的UTM坐标转换回经纬度坐标
                            expanded_polygon_coords = [utm_to_lonlat(x, y) for x, y in Polygon(outer_polygon_utm_coords).buffer(BORDER_EXPANSION_M).exterior.coords]

                            # 计算扩展后的多边形面积
                            expanded_polygon_area_m2 = calculate_spherical_polygon_area(expanded_polygon_coords,EARTH_RADIUS_KM * 1000)

                            # 在地图上绘制扩展后的多边形边界，设置颜色、线条宽度、不透明度等属性
                            # folium.Polygon(locations=[(lat, lon) for lon, lat in expanded_polygon_coords], color='red',weight=1, opacity=0.8, fill=True, fill_opacity=0).add_to(polygon_layer)

                            # 填充外圈和内圈之间的区域
                            inner_polygons = [poly for poly in polygons if poly != outer_polygon]  # 获取所有不等于外圈的多边形
                            inner_polygons_union = unary_union(inner_polygons)  # 合并所有内圈多边形
                            filled_area = outer_polygon.difference(inner_polygons_union)  # 计算外圈与内圈的差集区域

                            # 在地图上填充外圈内部区域，设置颜色、线条宽度、不透明度等属性
                            folium.Polygon(locations=[(lat, lon) for lon, lat in filled_area.exterior.coords],color=color, weight=5, opacity=0, fill=True, fill_opacity=0.4).add_to(polygon_layer)

                            # 填充内圈区域为白色
                            for poly in polygons:
                                for interior in poly.interiors:
                                    folium.Polygon(locations=[(lat, lon) for lon, lat in list(interior.coords)],color='white', weight=3, opacity=0.5, fill=True, fill_opacity=0.5).add_to(polygon_layer)

                            folium.Marker(location=sub_start_location, icon=folium.DivIcon(html=f'<div style="font-size: 10pt; color : black">{group_number}.{sub_index}S</div>')).add_to(work_layer)  # 标记起点
                            folium.Marker(location=sub_end_location, icon=folium.DivIcon(html=f'<div style="font-size: 10pt; color : black">{group_number}.{sub_index}E</div>')).add_to(work_layer)  # 标记终点

                            html_content += f'<p><strong>地块 {area_counter}</strong></p>'
                            # html_content += f'<p>去重面积: {int(coverage_area)} m²<br>包围面积: {int(outer_polygon_area_m2)} m²<br>扩展包围面积: {int(expanded_polygon_area_m2)} m²</p>'
                            html_content += f'<p>去重面积: {int(coverage_area)} m²<br>包围面积: {int(outer_polygon_area_m2)} m²<br>'

                            center_point = calculate_centroid(points)
                            folium.Marker(location=center_point, icon=folium.DivIcon(html=f'<div style="font-size: 18pt; color: red; font-weight: bold;">{area_counter}</div>')).add_to(label_layer)
                            area_counter += 1

                            for poly in polygons:  # 显示内边界
                                for interior in poly.interiors:
                                    folium.Polygon(locations=[(lat, lon) for lon, lat in list(interior.coords)],color='red', weight=3, opacity=0.7, dash_array='5, 5').add_to(polygon_layer)
                            for triangle in valid_triangles:  # 添加三角剖分结果到图层
                                folium.Polygon(locations=[(lat, lon) for lon, lat in points[triangle]], color='green',weight=1, opacity=0.2).add_to(triangle_layer)
                        else:
                            print("No valid polygons found for this area.")
                    color_index += 1  # 在每个工作区域结束后增加 color_index，以便下一个工作区域使用不同的颜色

    # 结束HTML控件内容
    html_content += '</div>'

    # 创建一个 HTML 文件来显示地块编号和面积信息
    html_file_path = 'areas_info.html'
    with open(html_file_path, 'w', encoding='utf-8') as file:
        file.write(html_content)

    # 使用 Div 将面积信息显示在地图的左侧
    html = """
    <div id="info-box" style="position: fixed; top: 50px; left: 10px; z-index: 1000; background-color: white; padding: 10px; border: 2px solid black; max-height: 1000px; overflow-y: auto;">
        {content}
    </div>
    <button id="toggle-button" style="position: fixed; top: 10px; left: 10px; z-index: 1001; background-color: white; border: 2px solid black;">收起/展开</button>
    <script>
        document.getElementById('toggle-button').onclick = function() {{
            var infoBox = document.getElementById('info-box');
            if (infoBox.style.display === 'none') {{
                infoBox.style.display = 'block';
            }} else {{
                infoBox.style.display = 'none';
            }}
        }};
    </script>
    """.replace("{content}", html_content).replace("{{", "{").replace("}}", "}")

    # 将 HTML 添加到地图中
    map.get_root().html.add_child(folium.Element(html))

    # 添加各图层到地图
    work_layer.add_to(map)
    # non_work_layer.add_to(map)
    # non_work_area_work_layer.add_to(map)
    track_layer.add_to(map)
    # area_layer.add_to(map)
    polygon_layer.add_to(map)
    triangle_layer.add_to(map)
    marker_cluster_layer.add_to(map)
    heatmap_layer.add_to(map)
    work_track_layer.add_to(map)
    non_work_track_layer.add_to(map)
    label_layer.add_to(map)  # 添加地块编号图层

    # 添加图层控制
    folium.LayerControl().add_to(map)

    # 获取当前时间并格式化为字符串
    current_time_str = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')

    # 生成动态文件名
    base_filename = input_filename.split('.')[0]
    output_filename = f"{base_filename}_{current_time_str}.html"

    # 保存地图到动态生成的文件名
    map.save(output_filename)

    # 记录结束时间并打印运行时间
    end_time = time.time()
    elapsed_time = end_time - start_time
    print(f"代码运行时间：{elapsed_time:.2f} 秒")

    # 计算并打印统计和分析结果
    total_distance_km = cleaned_data['distance'].sum()  # 计算总距离
    average_distance_km = cleaned_data['distance'].mean()  # 计算平均每点距离
    max_speed = cleaned_data['speed'].max()  # 计算最大速度

    print(f"总距离: {total_distance_km:.3f} 公里")
    print(f"最大速度: {max_speed:.3f} 公里/小时")  # 添加最大速度输出
    print(cleaned_data['status'].value_counts())

    # 如果数据中包含时间戳，我们可以计算总时长
    if 'time' in cleaned_data.columns:
        cleaned_data['time'] = pd.to_datetime(cleaned_data['time'], format='%Y-%m-%d %H:%M:%S')
        total_time_seconds = (cleaned_data['time'].iloc[-1] - cleaned_data['time'].iloc[0]).total_seconds()
        total_time_hours = total_time_seconds / 3600
        print(f"总时长: {total_time_hours:.3f} 小时")
        average_speed_kmh = total_distance_km / total_time_hours
        print(f"平均速度: {average_speed_kmh:.3f} 公里/小时")
    else:
        print("没有时间数据无法计算总时长。")

# GCJ-02 to WGS-84转换函数
def gcj02_to_wgs84(lng, lat):
    a = 6378245.0
    ee = 0.00669342162296594323
    dlat = _transformlat(lng - 105.0, lat - 35.0)
    dlng = _transformlng(lng - 105.0, lat - 35.0)
    radlat = lat / 180.0 * np.pi
    magic = np.sin(radlat)
    magic = 1 - ee * magic * magic
    sqrtmagic = np.sqrt(magic)
    dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * np.pi)
    dlng = (dlng * 180.0) / (a / sqrtmagic * np.cos(radlat) * np.pi)
    mglat = lat + dlat
    mglng = lng + dlng
    return lng * 2 - mglng, lat * 2 - mglat

def _transformlat(lng, lat):
    ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * np.sqrt(np.abs(lng))
    ret += (20.0 * np.sin(6.0 * lng * np.pi) + 20.0 * np.sin(2.0 * lng * np.pi)) * 2.0 / 3.0
    ret += (20.0 * np.sin(lat * np.pi) + 40.0 * np.sin(lat / 3.0 * np.pi)) * 2.0 / 3.0
    ret += (160.0 * np.sin(lat / 12.0 * np.pi) + 320 * np.sin(lat * np.pi / 30.0)) * 2.0 / 3.0
    return ret

def _transformlng(lng, lat):
    ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * np.sqrt(np.abs(lng))
    ret += (20.0 * np.sin(6.0 * lng * np.pi) + 20.0 * np.sin(2.0 * lng * np.pi)) * 2.0 / 3.0
    ret += (20.0 * np.sin(lng * np.pi) + 40.0 * np.sin(lng / 3.0 * np.pi)) * 2.0 / 3.0
    ret += (150.0 * np.sin(lng / 12.0 * np.pi) + 300.0 * np.sin(lng / 30.0 * np.pi)) * 2.0 / 3.0
    return ret

def vectorized_haversine(lat1, lon1, lat2, lon2):
    lat1, lon1, lat2, lon2 = builtins.map(np.radians, [lat1, lon1, lat2, lon2])
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = np.sin(dlat / 2.0) ** 2 + np.cos(lat1) * np.cos(lat2) * np.sin(dlon / 2.0) ** 2
    c = 2 * np.arcsin(np.sqrt(a))
    km = 6371 * c  # 地球半径 6371 公里
    return km

def calculate_speed_optimized(data):
    data['time'] = pd.to_datetime(data['time'])
    data['latitude_shifted'] = data['latitude'].shift(-1)
    data['longitude_shifted'] = data['longitude'].shift(-1)
    data['distance'] = vectorized_haversine(data['latitude'], data['longitude'], data['latitude_shifted'], data['longitude_shifted'])
    time_deltas = data['time'].diff().dt.total_seconds() / 3600
    data['speed'] = data['distance'] / time_deltas
    data['speed'] = data['speed'].fillna(0)
    data['distance'] = data['distance'].fillna(0)
    data.drop(['latitude_shifted', 'longitude_shifted'], axis=1, inplace=True)
    return data

def optimize_data_cleaning(data, speed_min, speed_max):
    cleaned_data = data[(data['speed'] >= speed_min) & (data['speed'] <= speed_max)].reset_index(drop=True)
    cleaned_data = cleaned_data.dropna(subset=['latitude', 'longitude'])
    return cleaned_data

# 使用 RadiusNeighborsClassifier 来查找指定半径范围内的邻居
def apply_neighbors_classifier(data, radius_radians, neighbor_threshold,DISTANCE_THRESHOLD):  # 定义一个函数，用于应用邻居分类器，参数包括数据、半径和邻居阈值
    coords = np.radians(data[['latitude', 'longitude']])  # 将数据中的纬度和经度转换为弧度
    neigh = RadiusNeighborsClassifier(radius=radius_radians, metric='haversine',
                                      outlier_label='Non-work')  # 创建一个半径邻居分类器，使用Haversine度量，并标记异常值为“Non-work”
    neigh.fit(coords, data['status'])  # 使用坐标和状态数据训练分类器
    distances, indices = neigh.radius_neighbors(coords)  # 查找每个点在指定半径范围内的邻居

    # 增加距离判断条件
    for i in range(len(data)):
        if i == 0:
            data.at[i, 'status'] = 'Non-work'  # 第一个点默认设为非工作状态
        else:
            prev_lat, prev_lon = data.at[i - 1, 'latitude'], data.at[i - 1, 'longitude']
            curr_lat, curr_lon = data.at[i, 'latitude'], data.at[i, 'longitude']
            distance_to_prev = vectorized_haversine(prev_lat, prev_lon, curr_lat, curr_lon)
            if distance_to_prev > DISTANCE_THRESHOLD:  # 使用提取的距离阈值
                data.at[i, 'status'] = 'Non-work'
            else:
                if len(indices[i]) >= neighbor_threshold:
                    data.at[i, 'status'] = 'Work'
                else:
                    data.at[i, 'status'] = 'Non-work'
    return data  # 返回更新后的数据

def identify_work_areas(data, work_threshold, offset_points):  # 定义一个函数，用于识别连续的工作区域，参数包括数据、工作阈值和偏移点数
    work_start_end = []  # 存储工作区域的起始和结束索引
    current_start = None  # 当前工作区域的起始索引
    adjusted_points = set()  # 存储需要调整的点的索引

    def process_work_area(start, end):  # 辅助函数，用于处理工作区域并更新相关索引
        if start is not None and (end - start) >= work_threshold:  # 如果起始索引存在且工作区域长度满足阈值
            start_index = max(start + offset_points, 0)  # 计算调整后的起始索引，确保不小于0
            end_index = min(end - offset_points - 1, len(data) - 1)  # 计算调整后的结束索引，确保不超过数据长度
            work_start_end.append((start_index, end_index))  # 将工作区域的起始和结束索引添加到列表中
            adjusted_points.update(range(start, start_index))  # 更新需要调整的点的索引（起始部分）
            adjusted_points.update(range(end_index + 1, end))  # 更新需要调整的点的索引（结束部分）
        elif start is not None:  # 如果工作区域长度不满足阈值
            adjusted_points.update(range(start, end))  # 将整个区域标记为需要调整

    for i, row in data.iterrows():  # 遍历数据中的每一行
        if row['status'] == 'Work':  # 如果当前点的状态是“工作”
            if current_start is None:  # 如果没有正在记录的工作区域
                current_start = i  # 记录当前点为工作区域的起始点
        else:  # 如果当前点的状态不是“工作”
            process_work_area(current_start, i)  # 处理当前工作区域
            current_start = None  # 重置当前工作区域的起始索引
    process_work_area(current_start, len(data))  # 检查并处理最后一个工作区域
    return work_start_end, adjusted_points  # 返回工作区域的起始和结束索引，以及需要调整的点的索引

# 定义函数，用于计算球面多边形的面积
def calculate_spherical_polygon_area(coords, radius):
    coords = [(radians(lon), radians(lat)) for lon, lat in coords if lon and lat]  # 将经纬度转换为弧度，并排除空值
    total = 0.0
    for i in range(len(coords)):
        lon1, lat1 = coords[i]
        lon2, lat2 = coords[(i + 1) % len(coords)]  # 循环列表实现闭环
        total += (lon2 - lon1) * (2 + sin(lat1) + sin(lat2))
    return abs(total * radius ** 2 / 2)

# 定义函数，使用Haversine公式计算两点之间的地球表面距离
def haversine(lon1, lat1, lon2, lat2):
    lon1, lat1, lon2, lat2 = builtins.map(radians, [lon1, lat1, lon2, lat2])  # 将角度转换为弧度
    dlon = lon2 - lon1
    dlat = lat2 - lat1
    a = sin(dlat / 2) ** 2 + cos(lat1) * cos(lat2) * sin(dlon / 2) ** 2
    c = 2 * asin(sqrt(a))
    return EARTH_RADIUS_KM * 1000 * c  # 返回计算的距离

# 定义函数，检查三角形是否为有效的三角形
def valid_triangle(triangle, points, max_length):
    for i in range(3):
        point1 = points[triangle[i]]
        point2 = points[triangle[(i + 1) % 3]]
        if haversine(point1[0], point1[1], point2[0], point2[1]) > max_length:
            return False
    return True

# 定义函数，从三角形集合中提取最外圈的边
def extract_outer_edges(triangles, points):
    edge_count = {}
    for tri in triangles:
        for i in range(3):
            edge = tuple(sorted([tri[i], tri[(i + 1) % 3]]))  # 排序后的边，无向边
            edge_count[edge] = edge_count.get(edge, 0) + 1
    outer_edges = [edge for edge, count in edge_count.items() if count == 1]  # 选择只出现一次的边
    return outer_edges

# 计算外围多边形的周长
def calculate_perimeter(coords):
    total_perimeter = 0.0  # 初始化总周长为0
    num_points = len(coords)  # 获取顶点数量
    if num_points > 1:  # 当顶点数量超过1时，才可能形成闭环
        for i in range(num_points):
            lon1, lat1 = coords[i]  # 当前顶点
            lon2, lat2 = coords[(i + 1) % num_points]  # 下一个顶点，利用模运算回到列表开始，形成闭环
            total_perimeter += haversine(lon1, lat1, lon2, lat2)  # 计算两点间距离并累加到总周长
    return total_perimeter  # 返回计算得到的总周长

# 定义计算每个坐标点到其最近邻点的距离的函数
def calculate_average_nearest_distance(coords):
    tree = KDTree(coords)
    distances, indices = tree.query(coords, k=2)  # k=2 because the nearest neighbor is the point itself
    nearest_distances = []

    for i in range(len(coords)):
        j = indices[i][1]  # index of the nearest neighbor
        nearest_distance = haversine(coords[i][1], coords[i][0], coords[j][1], coords[j][0])
        nearest_distances.append(nearest_distance)

    return np.mean(nearest_distances)

# 定义计算每个坐标点到其最近邻点的距离的函数
def calculate_average_nearest_distance(coords):
    tree = KDTree(coords)
    distances, indices = tree.query(coords, k=2)  # k=2 because the nearest neighbor is the point itself
    nearest_distances = []

    for i in range(len(coords)):
        j = indices[i][1]  # index of the nearest neighbor
        nearest_distance = haversine(coords[i][1], coords[i][0], coords[j][1], coords[j][0])
        nearest_distances.append(nearest_distance)

    return np.mean(nearest_distances)

# 定义计算每个点10米范围内的邻居点数的函数
def calculate_neighbors_within_radius(sub_data, radius_m):
    sub_data = sub_data.copy()
    sub_data.loc[:, 'latitude'] = sub_data['latitude'].astype(float)
    sub_data.loc[:, 'longitude'] = sub_data['longitude'].astype(float)

    coords = np.radians(sub_data[['latitude', 'longitude']].to_numpy())
    tree = KDTree(coords)
    radius_radians = radius_m / 6371000
    neighbor_counts = []

    for coord in coords:
        neighbors = tree.query_ball_point(coord, radius_radians)
        neighbors_count = len(neighbors) - 1  # 减去自身
        neighbor_counts.append(neighbors_count)

    # 调试信息：打印邻居点数统计
    print(f"最大邻居点数: {max(neighbor_counts)}")
    print(f"最小邻居点数: {min(neighbor_counts)}")
    # print(f"邻居点数分布: {np.histogram(neighbor_counts, bins=10)}")

    return np.mean(neighbor_counts)

# 定义函数，计算区域的中心点
def calculate_centroid(coords):
    if len(coords) == 0:
        return [0, 0]
    lon = [c[0] for c in coords]
    lat = [c[1] for c in coords]
    return [sum(lat) / len(lat), sum(lon) / len(lon)]

if __name__ == '__main__':
    trace_start_end_area()
