import pandas as pd
import numpy as np
import folium
from folium import plugins
from sklearn.neighbors import RadiusNeighborsClassifier
from scipy.spatial import Delaunay, QhullError
from shapely.geometry import Polygon, Point, LineString
from shapely.ops import unary_union, polygonize
from math import radians, sin, cos, sqrt, asin

# 地球半径常量
EARTH_RADIUS_KM = 6371

# 定义辅助函数检查一个点是否在多边形内
def point_in_polygon(point, polygon):
    return Polygon(polygon).contains(Point(point))

# 定义函数计算两点间的地球表面距离
def vectorized_haversine(lon1, lat1, lon2, lat2):
    lon1, lat1, lon2, lat2 = np.radians([lon1, lat1, lon2, lat2])
    dlon = lon2 - lon1
    dlat = lat2 - lat1
    a = np.sin(dlat / 2.0) ** 2 + np.cos(lat1) * np.cos(lat2) * np.sin(dlon / 2.0) ** 2
    c = 2 * np.arcsin(np.sqrt(a))
    return EARTH_RADIUS_KM * c

# 定义GCJ-02转WGS-84的转换函数
def gcj02_to_wgs84(lng, lat):
    a = 6378245.0
    ee = 0.00669342162296594323
    dlat = _transformlat(lng - 105.0, lat - 35.0)
    dlng = _transformlng(lng - 105.0, lat - 35.0)
    radlat = lat / 180.0 * np.pi
    magic = sin(radlat)
    magic = 1 - ee * magic * magic
    sqrtmagic = sqrt(magic)
    dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * np.pi)
    dlng = (dlng * 180.0) / (a / sqrtmagic * cos(radlat) * np.pi)
    mglat = lat + dlat
    mglng = lng + dlng
    return lng * 2 - mglng, lat * 2 - mglat

def _transformlat(lng, lat):
    ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * sqrt(abs(lng))
    ret += (20.0 * sin(6.0 * lng * np.pi) + 20.0 * sin(2.0 * lng * np.pi)) * 2.0 / 3.0
    ret += (20.0 * sin(lat *np.pi) + 40.0 * sin(lat / 3.0 * np.pi)) * 2.0 / 3.0
    ret += (160.0 * sin(lat / 12.0 * np.pi) + 320 * sin(lat * np.pi / 30.0)) * 2.0 / 3.0
    return ret

def _transformlng(lng, lat):
    ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * sqrt(abs(lng))
    ret += (20.0 * sin(6.0 * lng * np.pi) + 20.0 * sin(2.0 * lng * np.pi)) * 2.0 / 3.0
    ret += (20.0 * sin(lng * np.pi) + 40.0 * sin(lng / 3.0 * np.pi)) * 2.0 / 3.0
    ret += (150.0 * sin(lng / 12.0 * np.pi) + 300.0 * sin(lng / 30.0 * np.pi)) * 2.0 / 3.0
    return ret

# 定义计算速度的函数
def calculate_speed_optimized(data):
    data['time'] = pd.to_datetime(data['time'])
    data['latitude_shifted'] = data['latitude'].shift(-1)
    data['longitude_shifted'] = data['longitude'].shift(-1)
    data['distance'] = vectorized_haversine(data['longitude'], data['latitude'], data['longitude_shifted'], data['latitude_shifted'])
    time_deltas = data['time'].diff().dt.total_seconds() / 3600
    data['speed'] = data['distance'] / time_deltas
    data['speed'] = data['speed'].fillna(0)
    data['distance'] = data['distance'].fillna(0)
    data.drop(['latitude_shifted', 'longitude_shifted'], axis=1, inplace=True)
    return data

# 优化的数据清洗函数
def optimize_data_cleaning(data, speed_min, speed_max):
    cleaned_data = data[(data['speed'] >= speed_min) & (data['speed'] <= speed_max)].reset_index(drop=True)
    cleaned_data = cleaned_data.dropna(subset=['latitude', 'longitude'])
    return cleaned_data

# 使用 RadiusNeighborsClassifier 来标记数据的工作状态
def apply_neighbors_classifier(data, radius_km, neighbor_threshold):
    data = data.reset_index(drop=True)
    coords = np.radians(data[['latitude', 'longitude']])
    radius_radians = radius_km / EARTH_RADIUS_KM
    neigh = RadiusNeighborsClassifier(radius=radius_radians, metric='haversine', outlier_label='Non-work')
    neigh.fit(coords, data['status'])
    distances, indices = neigh.radius_neighbors(coords)
    for i in range(len(data)):
        if len(indices[i]) >= neighbor_threshold:
            data.at[i, 'status'] = 'Work'
        else:
            data.at[i, 'status'] = 'Non-work'
    return data

# 识别工作区域
def identify_work_areas(data, work_threshold, offset_points):
    work_start_end = []
    current_start = None
    adjusted_points = set()
    for i, row in data.iterrows():
        if row['status'] == 'Work':
            if current_start is None:
                current_start = i
        else:
            if current_start is not None and (i - current_start) >= work_threshold:
                start_index = max(current_start + offset_points, 0)
                end_index = min(i - offset_points - 1, len(data) - 1)
                work_start_end.append((start_index, end_index))
                adjusted_points.update(range(current_start, start_index))
                adjusted_points.update(range(end_index + 1, i))
            current_start = None
    if current_start is not None and (len(data) - current_start) >= work_threshold:
        start_index = max(current_start + offset_points, 0)
        end_index = len(data) - 1
        work_start_end.append((start_index, end_index))
        adjusted_points.update(range(current_start, start_index))
        adjusted_points.update(range(end_index + 1, len(data)))
    return work_start_end, adjusted_points

# 计算球面多边形面积的函数
def calculate_spherical_polygon_area(coords, radius):
    coords = [(radians(lon), radians(lat)) for lon, lat in coords if lon and lat]
    total = 0.0
    for i in range(len(coords)):
        lon1, lat1 = coords[i]
        lon2, lat2 = coords[(i + 1) % len(coords)]
        total += (lon2 - lon1) * (2 + sin(lat1) + sin(lat2))
    return abs(total * radius ** 2 / 2)

# 三角剖分方法片段
def perform_delaunay_triangulation(points):
    try:
        tri = Delaunay(points)
    except QhullError as e:
        print("Delaunay 三角剖分失败: ", e)
        raise
    return tri

# 检查三角形是否为有效的三角形
def valid_triangle(triangle, points, max_length):
    for i in range(3):
        point1 = points[triangle[i]]
        point2 = points[triangle[(i + 1) % 3]]
        if vectorized_haversine(point1[0], point1[1], point2[0], point2[1]) > max_length:
            return False
    return True

# 提取三角形集合中的外边
def extract_outer_edges(triangles, points):
    edge_count = {}
    for tri in triangles:
        for i in range(3):
            edge = tuple(sorted([tri[i], tri[(i + 1) % 3]]))
            edge_count[edge] = edge_count.get(edge, 0) + 1
    outer_edges = [edge for edge, count in edge_count.items() if count == 1]
    return outer_edges

# 使用三角剖分方法处理区域
def process_work_area(points, max_length, points_threshold):
    if len(points) > points_threshold:
        tri = perform_delaunay_triangulation(points)
        valid_triangles = [t for t in tri.simplices if valid_triangle(t, points, max_length)]
        outer_edges = extract_outer_edges(valid_triangles, points)
        edge_lines = [LineString([points[edge[0]], points[edge[1]]]) for edge in outer_edges]
        merged_lines = unary_union(edge_lines)
        polygons = list(polygonize(merged_lines))
        return polygons, valid_triangles
    return [], []

# 主函数
def trace_start_end_area():
    input_filename = '20240511.csv'  # 数据文件名
    data = pd.read_csv(input_filename)
    data.sort_values(by='time', inplace=True)
    data[['longitude', 'latitude']] = data.apply(lambda row: pd.Series(gcj02_to_wgs84(row['longitude'], row['latitude'])), axis=1)
    data = calculate_speed_optimized(data)  # 计算速度和距离
    SPEED_MIN_1, SPEED_MAX_1 = 2, 15  # 定义速度阈值
    cleaned_data = optimize_data_cleaning(data, SPEED_MIN_1, SPEED_MAX_1)  # 清洗数据
    average_distance_between_points = cleaned_data['distance'].mean()  # 计算平均点间距
    NEIGHBOR_THRESHOLD_1 = 15  # 邻居阈值
    WORK_THRESHOLD_1 = average_distance_between_points * 1000 * 4  # 工作阈值
    OFFSET_POINTS_1 = 5  # 偏移点数
    RADIUS_KM_1 = average_distance_between_points * 5  # 半径阈值
    MAX_EDGE_LENGTH_M = 7  # 单位: 米
    POINTS_THRESHOLD = 300  # 未被包围点的阈值

    cleaned_data['status'] = 'Non-work'
    cleaned_data = apply_neighbors_classifier(cleaned_data, RADIUS_KM_1, NEIGHBOR_THRESHOLD_1)  # 标记工作状态
    # 处理工作区域
    work_start_end_1, _ = identify_work_areas(cleaned_data, WORK_THRESHOLD_1, OFFSET_POINTS_1)
    work_areas = []

    for start, end in work_start_end_1:
        sub_data = cleaned_data.iloc[start:end + 1]
        points = sub_data[['longitude', 'latitude']].to_numpy()
        polygons, valid_triangles = process_work_area(points, MAX_EDGE_LENGTH_M, POINTS_THRESHOLD)
        if polygons:
            outer_polygon = max(polygons, key=lambda p: p.area)
            outer_polygon_coords = list(outer_polygon.exterior.coords)
            uncontained_points = [points[i] for i in range(len(points)) if not point_in_polygon(points[i], outer_polygon_coords)]
            if len(uncontained_points) > POINTS_THRESHOLD:
                # 分割原工作区域并更新
                new_work_start_end = (start, start + len(points) - len(uncontained_points) - 1)
                new_independent_area = (start + len(points) - len(uncontained_points), end)
                work_start_end_1.append(new_independent_area)
                work_start_end_1 = [new_work_start_end if area == (start, end) else area for area in work_start_end_1]
                break

    # 更新工作区域
    updated_work_areas = []
    for start, end in work_start_end_1:
        sub_data = cleaned_data.iloc[start:end + 1]
        points = sub_data[['longitude', 'latitude']].to_numpy()
        polygons, valid_triangles = process_work_area(points, MAX_EDGE_LENGTH_M, POINTS_THRESHOLD)
        if polygons:
            outer_polygon = max(polygons, key=lambda p: p.area)
            outer_polygon_coords = list(outer_polygon.exterior.coords)
            coverage_area = calculate_spherical_polygon_area(outer_polygon_coords, EARTH_RADIUS_KM * 1000)
            updated_work_areas.append((outer_polygon_coords, coverage_area, points, valid_triangles))

    # 创建地图并初始化图层
    def create_map_with_triangles(data, max_length, points_threshold):
        map_center = data[['latitude', 'longitude']].mean().values
        map = folium.Map(location=map_center, zoom_start=15)

        # 添加谷歌地图图层
        folium.TileLayer(
            tiles='http://{s}.google.com/vt/lyrs=s&x={x}&y={y}&z={z}',
            attr='Google',
            name='Google Satellite',
            overlay=True,
            control=True,
            subdomains=['mt0', 'mt1', 'mt2', 'mt3'],
            max_zoom=20
        ).add_to(map)

        # 初始化地块边界和三角剖分图层
        polygon_layer = folium.FeatureGroup(name='工作区域边界', show=True)
        triangle_layer = folium.FeatureGroup(name='三角剖分', show=True)
        map.add_child(polygon_layer)
        map.add_child(triangle_layer)

        # 添加轨迹
        locations = data[['latitude', 'longitude']].values
        folium.PolyLine(locations, color='blue', weight=2.5, opacity=1).add_to(map)

        # 处理工作区域并标记三角剖分
        points = data[['longitude', 'latitude']].to_numpy()
        polygons, valid_triangles = process_work_area(points, max_length, points_threshold)
        if polygons:
            outer_polygon = max(polygons, key=lambda p: p.area)
            outer_polygon_coords = list(outer_polygon.exterior.coords)
            folium.Polygon(
                locations=[(lat, lon) for lon, lat in outer_polygon_coords],
                color='blue',
                weight=2,
                opacity=0.8,
                fill=True,
                fill_opacity=0.3
            ).add_to(polygon_layer)

            for triangle in valid_triangles:
                triangle_coords = [(points[i][1], points[i][0]) for i in triangle]
                folium.Polygon(
                    locations=triangle_coords,
                    color='green',
                    weight=1,
                    opacity=0.5
                ).add_to(triangle_layer)

        return map

    # 示例调用（假设cleaned_data是已经清洗过的数据）
    max_length = 7  # 设置最大边长为7米
    points_threshold = 300  # 设置点数阈值

    # 创建并保存地图
    map = create_map_with_triangles(cleaned_data, max_length, points_threshold)
    map.save('output_map.html')

if __name__ == '__main__':
    trace_start_end_area()
