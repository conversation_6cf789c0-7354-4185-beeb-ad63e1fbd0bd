import json
import os
import glob # 用于查找文件

def merge_json_files(input_file_paths, output_file_path):
    """
    合并多个JSON文件（每个文件包含一个对象列表）到一个新的JSON文件中。

    参数:
    input_file_paths (list): 包含所有输入JSON文件路径的列表。
    output_file_path (str): 合并后输出的JSON文件路径。
    """
    merged_data = [] # 初始化一个空列表来存储所有合并的数据

    for file_path in input_file_paths:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                print(f"正在读取文件: {file_path}")
                # 假设每个JSON文件的根就是一个列表
                data = json.load(f)
                if isinstance(data, list):
                    merged_data.extend(data) # 将当前文件的列表内容追加到总列表
                else:
                    print(f"警告: 文件 {file_path} 的根内容不是一个列表，已跳过。")
        except FileNotFoundError:
            print(f"错误: 文件 {file_path} 未找到，已跳过。")
        except json.JSONDecodeError:
            print(f"错误: 文件 {file_path} 不是有效的JSON格式，已跳过。")
        except Exception as e:
            print(f"读取文件 {file_path} 时发生未知错误: {e}，已跳过。")

    try:
        with open(output_file_path, 'w', encoding='utf-8') as outfile:
            # indent参数使得输出的JSON文件格式化，更易读
            # ensure_ascii=False 保证中文等非ASCII字符能正确显示而不是被转义
            json.dump(merged_data, outfile, indent=4, ensure_ascii=False)
        print(f"\n成功合并数据到: {output_file_path}")
        print(f"总共合并了 {len(merged_data)} 条记录。")
    except Exception as e:
        print(f"写入合并文件 {output_file_path} 时发生错误: {e}")

# --- 如何使用 ---

if __name__ == "__main__":
    # 方式一：手动指定文件列表 (如果需要，可以取消注释并修改)
    # json_files_to_merge = [
    #     r"F:\Python\作业状态识别\Json\程总\file1.json", # 使用 r"" 来处理Windows路径中的反斜杠
    #     r"F:\Python\作业状态识别\Json\程总\file2.json",
    # ]

    # 方式二：自动查找指定目录下的所有 .json 文件
    # 注意：对于Windows路径，建议使用原始字符串 (r"...") 或者将反斜杠替换为正斜杠 (/)
    input_directory = r"F:\Python\作业状态识别\Json\程总" # <--- 修改为你的JSON文件所在目录
    # 或者使用正斜杠: input_directory = "F:/Python/作业状态识别/Json/程总"

    json_files_to_merge = glob.glob(os.path.join(input_directory, "*.json"))

    if not json_files_to_merge:
        print(f"在目录 '{input_directory}' 下没有找到任何 .json 文件。")
    else:
        print(f"找到以下JSON文件准备合并:")
        for f_path in json_files_to_merge:
            print(f" - {f_path}")

        # 定义合并后的文件名和路径，这里将其保存在与输入文件相同的目录下
        output_merged_file = os.path.join(input_directory, "merged_output.json")

        merge_json_files(json_files_to_merge, output_merged_file)