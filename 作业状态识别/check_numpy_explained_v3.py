"""
NumPy配置和性能检测工具
检测NumPy后端配置(MKL vs OpenBLAS)并测试性能
"""
import sys
import os
import time
import numpy as np
import ctypes
import platform
from pathlib import Path

# 设置所有可能的线程控制变量
os.environ["OMP_NUM_THREADS"] = "8"
os.environ["OPENBLAS_NUM_THREADS"] = "8"
os.environ["MKL_NUM_THREADS"] = "8"
os.environ["NUMEXPR_NUM_THREADS"] = "8"

def print_separator():
    print("-" * 40)

def get_loaded_dlls():
    """获取已加载的DLL列表"""
    loaded_dlls = []
    if platform.system() == 'Windows':
        # 仅在Windows下有效
        try:
            import psutil
            current_process = psutil.Process()
            for dll in current_process.memory_maps():
                if '.dll' in dll.path.lower():
                    if 'mkl' in dll.path.lower() or 'blas' in dll.path.lower() or 'lapack' in dll.path.lower():
                        loaded_dlls.append(os.path.basename(dll.path))
        except ImportError:
            # 如果没有psutil，尝试直接使用ctypes
            try:
                kernel32 = ctypes.WinDLL('kernel32', use_last_error=True)
                process = kernel32.GetCurrentProcess()
                # 这种方法有限制，仅列举部分DLL
                if hasattr(kernel32, 'EnumProcessModules'):
                    loaded_dlls.append("使用ctypes无法获取完整DLL列表")
            except Exception as e:
                loaded_dlls.append(f"无法获取DLL列表: {e}")
    return loaded_dlls

def check_numpy_config():
    """检查NumPy配置"""
    print(f"Python版本: {platform.python_version()}")
    print(f"Python路径: {sys.executable}")
    print(f"NumPy版本: {np.__version__}")
    print(f"NumPy库路径: {np.__file__}")
    print()
    
    # 检查加载的数值计算库
    print("已加载的数值计算库:")
    dlls = get_loaded_dlls()
    if dlls:
        for dll in dlls:
            print(f"  {dll}")
    else:
        print("  未检测到相关DLL信息")
    print()
    
    # 检查OpenBLAS或MKL
    print("NumPy BLAS配置信息:")
    blas_info = None
    
    # 检查是否使用OpenBLAS
    if hasattr(np, '__config__') and hasattr(np.__config__, 'openblas_info'):
        openblas_info = getattr(np.__config__, 'openblas_info', {})
        if openblas_info:
            print("  检测到OpenBLAS配置")
            if 'library_dirs' in openblas_info:
                for lib_dir in openblas_info['library_dirs']:
                    lib_path = Path(lib_dir)
                    openblas_libs = list(lib_path.glob("*openblas*"))
                    if openblas_libs:
                        print(f"  可能的OpenBLAS库: {', '.join(l.name for l in openblas_libs)}")
            blas_info = "OpenBLAS"
    
    # 尝试从build_info检查BLAS信息
    try:
        if hasattr(np.__config__, 'show'):
            build_info = np.__config__.show()
            if 'blas' in build_info and 'scipy-openblas' in build_info:
                print("  已加载OpenBLAS: libscipy_openblas64_-c16e4918366c6bc1f1cd71e28ca36fc0.dll")
                blas_info = "OpenBLAS"
    except:
        pass
    
    # 检查是否使用MKL
    using_mkl = False
    try:
        # 尝试导入mkl模块
        import mkl
        print(f"  检测到Intel MKL，版本: {mkl.get_version_string() if hasattr(mkl, 'get_version_string') else '未知'}")
        blas_info = "MKL"
        using_mkl = True
    except ImportError:
        # 检查是否通过numpy的__config__可以访问MKL信息
        try:
            if hasattr(np, '__config__') and hasattr(np.__config__, 'blas_mkl_info'):
                mkl_info = np.__config__.blas_mkl_info
                if mkl_info:
                    print("  检测到MKL配置")
                    blas_info = "MKL"
                    using_mkl = True
        except:
            pass
    
    # 查看distributor_init.py是否存在(这通常表示Intel发行版)
    numpy_path = Path(np.__file__).parent
    if (numpy_path / '_distributor_init.py').exists():
        print("  NumPy使用了distributor_init (可能是Intel发行版)")
        if not using_mkl:
            # 如果存在distributor_init但没有检测到MKL，可能只是包含了MKL运行时
            using_mkl = True
    
    print()
    print("后端类型总结:")
    if blas_info == "OpenBLAS":
        print("  NumPy使用OpenBLAS后端")
    elif blas_info == "MKL":
        print("  NumPy使用Intel MKL后端")
    else:
        print("  无法确定NumPy使用的BLAS后端")
    
    print(f"  使用MKL: {using_mkl}")
    print()
    
    # 设置线程数
    threads = min(os.cpu_count(), 8) if os.cpu_count() else 4
    if using_mkl:
        try:
            import mkl
            mkl.set_num_threads(threads)
            print(f"  使用MKL模块设置线程数: {threads}")
        except:
            os.environ["MKL_NUM_THREADS"] = str(threads)
            print(f"  MKL模块不可用，使用环境变量设置线程数")
            print(f"  设置MKL_NUM_THREADS={threads}")
    else:
        os.environ["OMP_NUM_THREADS"] = str(threads)
        os.environ["OPENBLAS_NUM_THREADS"] = str(threads)
        print(f"  MKL模块不可用，使用环境变量设置线程数")
        print(f"  设置OMP_NUM_THREADS={threads}")

def run_svd_test(n=4000):
    """运行SVD分解性能测试"""
    print("\n开始性能测试:")
    print_separator()
    
    print(f"创建 {n}x{n} 随机矩阵...")
    A = np.random.random((n, n))
    
    print("计算SVD分解...")
    start_time = time.time()
    U, s, Vh = np.linalg.svd(A)
    end_time = time.time()
    
    print(f"SVD耗时: {end_time - start_time:.4f}秒")
    print_separator()

if __name__ == "__main__":
    check_numpy_config()
    run_svd_test()
