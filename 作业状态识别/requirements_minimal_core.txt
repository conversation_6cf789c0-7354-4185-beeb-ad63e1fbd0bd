# 作业状态识别项目最小依赖库
# 核心数据处理
numpy==2.1.1
pandas==2.2.3

# 地理空间处理
pyproj==3.7.0
shapely==2.0.6

# 科学计算
scipy==1.14.1
scikit-learn==1.6.0

# BLAS后端 (重要：提供数值计算加速)
scipy-openblas  # OpenBLAS支持

# 可选：如果希望使用Intel MKL加速
# mkl==2025.1.0
# intel-openmp==2025.1.1
# intel-cmplr-lib-ur==2025.1.1
# tbb==2022.1.0

# 可视化 (可选，仅当GENERATE_HTML_MAPS=True时需要)
# folium==0.19.5
# branca==0.8.0

# 系统及工具库
# 这些库通常是Python标准库的一部分或随其他库安装
# json, time, datetime, gc, os, math, traceback 

# 可选依赖 - 如需要性能监控
# psutil==7.0.0 