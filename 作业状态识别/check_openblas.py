import os
import numpy as np
import time
import platform
import sys
import subprocess

# 持久化设置线程数环境变量（在脚本开始就设置）
if "OMP_NUM_THREADS" not in os.environ:
    os.environ["OMP_NUM_THREADS"] = "4"
if "OPENBLAS_NUM_THREADS" not in os.environ:
    os.environ["OPENBLAS_NUM_THREADS"] = "4"

# 检查是否设置了线程数环境变量
threads_vars = {
    "OMP_NUM_THREADS": os.environ.get("OMP_NUM_THREADS", "未设置"),
    "OPENBLAS_NUM_THREADS": os.environ.get("OPENBLAS_NUM_THREADS", "未设置"),
    "MKL_NUM_THREADS": os.environ.get("MKL_NUM_THREADS", "未设置"),
    "NUMEXPR_NUM_THREADS": os.environ.get("NUMEXPR_NUM_THREADS", "未设置")
}

# 基本信息
print(f"Python版本: {platform.python_version()}")
print(f"Python路径: {sys.executable}")
print(f"NumPy版本: {np.__version__}")
print(f"NumPy库路径: {np.__file__}")

# 打印线程配置
print("\n线程配置:")
for var, value in threads_vars.items():
    print(f"  {var}: {value}")

# 检查后端类型
print("\n检查NumPy后端:")
try:
    np_config = os.path.join(os.path.dirname(np.__file__), "__config__.py")
    if os.path.exists(np_config):
        with open(np_config, "r") as f:
            config = f.read()
            if "openblas" in config.lower():
                print("  NumPy使用OpenBLAS后端")
            if "mkl" in config.lower():
                print("  NumPy使用MKL后端")
except Exception as e:
    print(f"  检查后端出错: {e}")

# 尝试获取CPU核心数
try:
    import psutil
    cpu_count = psutil.cpu_count(logical=False)
    logical_count = psutil.cpu_count(logical=True)
    print(f"\nCPU信息: {cpu_count}物理核心, {logical_count}逻辑核心")
except ImportError:
    try:
        import multiprocessing
        cpu_count = multiprocessing.cpu_count()
        print(f"\nCPU核心数: {cpu_count}")
    except:
        print("\nCPU核心数: 无法确定")

# 性能测试
print("\n进行性能测试:")
for size in [1000, 2000, 4000]:
    print(f"  测试规模: {size}x{size} 矩阵...")
    # 运行三次测试取平均值
    times = []
    for i in range(3):
        # 强制垃圾回收和内存释放
        import gc
        gc.collect()
        
        a = np.random.random((size, size))
        start = time.time()
        _ = np.linalg.svd(a, full_matrices=False)
        end = time.time()
        times.append(end - start)
        print(f"    测试 {i+1}: {times[-1]:.4f}秒")
    
    avg_time = sum(times) / len(times)
    print(f"  平均耗时: {avg_time:.4f}秒")

print("\n进程信息:")
try:
    import psutil
    process = psutil.Process()
    print(f"  CPU使用率: {process.cpu_percent(interval=0.1)}%")
    print(f"  内存使用: {process.memory_info().rss / (1024 * 1024):.1f} MB")
    if hasattr(psutil, "cpu_freq"):
        freq = psutil.cpu_freq()
        if freq:
            print(f"  CPU频率: {freq.current:.1f} MHz")
except ImportError:
    print("  未安装psutil，无法获取进程信息")

print("\n完成!") 