import json
import os

def convert_custom_to_geojson(data):
    features = []
    for key, value in data.items():
        out_ring = value.get('out', [[]])[0]
        out_coords = [[point['longitude'], point['latitude']] for point in out_ring]
        if out_coords and (out_coords[0] != out_coords[-1]):
            out_coords.append(out_coords[0])

        inner_rings = []
        for hole in value.get('in', []):
            hole_coords = [[point['longitude'], point['latitude']] for point in hole]
            if hole_coords and (hole_coords[0] != hole_coords[-1]):
                hole_coords.append(hole_coords[0])
            inner_rings.append(hole_coords)

        feature = {
            "type": "Feature",
            "properties": {},
            "geometry": {
                "type": "Polygon",
                "coordinates": [out_coords] + inner_rings
            }
        }
        features.append(feature)

    geojson = {
        "type": "FeatureCollection",
        "features": features
    }
    return geojson

if __name__ == "__main__":
    input_path = r'F:\Python\作业状态识别\result\41.31transformed_data_boundary_20250513_021022.json'
    output_path = r'F:\Python\作业状态识别\result\output_geojson.json'

    # 读取自定义格式数据
    with open(input_path, 'r', encoding='utf-8') as f:
        custom_data = json.load(f)

    # 转换
    geojson_data = convert_custom_to_geojson(custom_data)

    # 保存到指定目录
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(geojson_data, f, ensure_ascii=False, indent=2)

    print(f"✅ 转换完成，已保存到 {output_path}")
