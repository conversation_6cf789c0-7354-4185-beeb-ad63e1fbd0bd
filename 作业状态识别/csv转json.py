import pandas as pd

def convert_csv_to_json(input_csv_path):
    try:
        df = pd.read_csv(input_csv_path)  # 读取CSV文件
        output_json_path = input_csv_path.replace('.csv', '.json')  # 自动改扩展名
        df.to_json(output_json_path, orient='records', force_ascii=False)  # 转为JSON数组
        print(f"✅ 成功转换，保存为: {output_json_path}")
    except Exception as e:
        print(f"❌ 转换失败: {e}")

if __name__ == "__main__":
    input_csv = "20221014_converted.csv"  # 🛠️ 你的输入文件名
    convert_csv_to_json(input_csv)
