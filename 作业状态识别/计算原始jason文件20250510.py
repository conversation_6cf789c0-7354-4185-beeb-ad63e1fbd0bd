import json
import folium
from folium.plugins import <PERSON>t<PERSON><PERSON>, MeasureControl
from datetime import datetime
from math import radians, cos, sin, sqrt, atan2

# 计算两点间的距离（哈弗辛公式）
def haversine(lat1, lon1, lat2, lon2):
    R = 6371.0  # 地球半径，单位千米
    dlat = radians(lat2 - lat1)
    dlon = radians(lon2 - lon1)
    a = sin(dlat / 2) ** 2 + cos(radians(lat1)) * cos(radians(lat2)) * sin(dlon / 2) ** 2
    c = 2 * atan2(sqrt(a), sqrt(1 - a))
    distance = R * c
    return distance

# 从JSON文件中提取GPS数据
def extract_gps_data_from_json(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        data = json.load(file)
        gps_data = []
        for entry in data:
            latitude = float(entry['latitude'])    # ← 改了这里
            longitude = float(entry['longitude'])  # ← 和这里
            time = datetime.strptime(entry['time'], '%Y-%m-%d %H:%M:%S')
            gps_data.append((latitude, longitude, 0.0, time))  # 速度初始化为0.0
    return gps_data


# 计算统计数据
def calculate_statistics(gps_data):
    total_distance = 0.0
    total_time = 0.0
    num_points = len(gps_data)
    max_speed_kmh = 0.0
    points_in_range = 0

    # 初始化速度区间计数器
    speed_intervals = {
        '0-1 km/h': 0,
        '1-2 km/h': 0,
        '2-3 km/h': 0,
        '3-4 km/h': 0,
        '4-5 km/h': 0,
        '5-10 km/h': 0,
        '10-15 km/h': 0,
        '15-20 km/h': 0,
        '20-25 km/h': 0,
        '25+ km/h': 0
    }

    for i in range(1, num_points):
        lat1, lon1, speed1, time1 = gps_data[i - 1]
        lat2, lon2, speed2, time2 = gps_data[i]
        distance = haversine(lat1, lon1, lat2, lon2)
        total_distance += distance
        time_diff = (time2 - time1).total_seconds() / 3600.0  # 转换为小时
        total_time += time_diff

        if time_diff > 0:
            speed2 = distance / time_diff
            if speed2 > max_speed_kmh:
                max_speed_kmh = speed2
            gps_data[i] = (lat2, lon2, speed2, time2)  # 更新速度

        # 统计速度区间
        if 0 <= speed2 < 1:
            speed_intervals['0-1 km/h'] += 1
        elif 1 <= speed2 < 2:
            speed_intervals['1-2 km/h'] += 1
        elif 2 <= speed2 < 3:
            speed_intervals['2-3 km/h'] += 1
        elif 3 <= speed2 < 4:
            speed_intervals['3-4 km/h'] += 1
        elif 4 <= speed2 < 5:
            speed_intervals['4-5 km/h'] += 1
        elif 5 <= speed2 < 10:
            speed_intervals['5-10 km/h'] += 1
        elif 10 <= speed2 < 15:
            speed_intervals['10-15 km/h'] += 1
        elif 15 <= speed2 < 20:
            speed_intervals['15-20 km/h'] += 1
        elif 20 <= speed2 < 25:
            speed_intervals['20-25 km/h'] += 1
        else:
            speed_intervals['25+ km/h'] += 1

    if total_time > 0:
        average_speed_kmh_direct = total_distance / total_time
    else:
        average_speed_kmh_direct = 0

    if num_points > 1:
        average_point_interval_km = total_distance / (num_points - 1)
    else:
        average_point_interval_km = 0

    # 使用每100个坐标点计算的平均速度
    interval_speeds = []
    for i in range(0, num_points, 1):
        if i + 1 < num_points:
            lat1, lon1, _, time1 = gps_data[i]
            lat2, lon2, _, time2 = gps_data[i + 1]
            distance = haversine(lat1, lon1, lat2, lon2)
            time_diff = (time2 - time1).total_seconds() / 3600.0  # 转换为小时
            if time_diff > 0:
                speed = distance / time_diff
                interval_speeds.append(speed)

    if interval_speeds:
        average_speed_kmh_interval = sum(interval_speeds) / len(interval_speeds)
    else:
        average_speed_kmh_interval = 0

    return {
        'total_distance_km': total_distance,
        'average_speed_kmh_direct': average_speed_kmh_direct,
        'average_speed_kmh_interval': average_speed_kmh_interval,  # 每100个坐标点计算的平均速度
        'total_time_hours': total_time,
        'average_point_interval_km': average_point_interval_km,
        'num_points': num_points,
        'max_speed_kmh': max_speed_kmh,  # 最大速度（公里/小时）
        'speed_intervals': speed_intervals  # 速度区间内的坐标点数量
    }

# 格式化统计结果为中文
def format_statistics(statistics):
    total_distance_km = statistics['total_distance_km']
    average_point_interval_km = statistics['average_point_interval_km']

    if total_distance_km < 1:
        total_distance_str = f"{total_distance_km * 1000:.2f} 米"
    else:
        total_distance_str = f"{total_distance_km:.2f} 公里"

    if average_point_interval_km < 1:
        average_point_interval_str = f"{average_point_interval_km * 1000:.4f} 米"
    else:
        average_point_interval_str = f"{average_point_interval_km:.5f} 公里"

    speed_intervals_str = "\n".join([f"{k}: {v} 个点" for k, v in statistics['speed_intervals'].items()])

    formatted_stats = (
        f"总距离: {total_distance_str}\n"
        f"平均速度（直接取值）: {statistics['average_speed_kmh_direct']:.2f} 公里/小时\n"
        f"平均速度（每100个点计算）: {statistics['average_speed_kmh_interval']:.2f} 公里/小时\n"
        f"总时长: {statistics['total_time_hours']:.2f} 小时\n"
        f"平均点间隔: {average_point_interval_str}\n"
        f"坐标点数量: {statistics['num_points']}\n"
        f"最大速度: {statistics['max_speed_kmh']:.2f} 公里/小时\n"
        f"速度区间分布:\n{speed_intervals_str}"
    )
    return formatted_stats

# 创建地图并绘制轨迹
def create_map_with_trajectory(coordinates, output_file_path):
    if not coordinates:
        print("No coordinates to plot.")
        return

    # 初始化地图
    m = folium.Map(location=coordinates[0], zoom_start=12)

    # 加入底图
    folium.TileLayer(
        tiles='http://{s}.google.com/vt/lyrs=s&x={x}&y={y}&z={z}',
        attr='Google',
        name='Google卫星影像',
        overlay=True,
        control=True,
        subdomains=['mt0', 'mt1', 'mt2', 'mt3']
    ).add_to(m)

    # 轨迹图层（AntPath）
    track_layer = folium.FeatureGroup(name="轨迹线", show=True)
    AntPath(
        locations=coordinates,
        color='blue',
        weight=3,
        opacity=0.7,
        delay=30000,
        pulse_color='#F0F0F0'
    ).add_to(track_layer)
    track_layer.add_to(m)

    # 坐标点图层（橘黄色点）
    point_layer = folium.FeatureGroup(name="所有坐标点", show=True)
    for lat, lon in coordinates:
        folium.CircleMarker(
            location=[lat, lon],
            radius=2,
            color='orange',        # ✅ 橘黄色描边
            fill=True,
            fill_color='orange',   # ✅ 橘黄色填充
            fill_opacity=0.7
        ).add_to(point_layer)
    point_layer.add_to(m)

    # 测量工具
    m.add_child(MeasureControl(primary_length_unit='kilometers', primary_area_unit='sqmeters'))

    # 图层控制器
    folium.LayerControl(collapsed=False).add_to(m)

    # 保存
    m.save(output_file_path)
    print(f"Map has been saved to {output_file_path}")


# 文件路径
json_file_path = '20221016.json'
output_map_path = '0511jason.html'

# 从 JSON 文件中提取 GPS 数据
gps_data = extract_gps_data_from_json(json_file_path)

# 计算统计数据
statistics = calculate_statistics(gps_data)

# 格式化统计结果
formatted_stats = format_statistics(statistics)
print(formatted_stats)

# 创建并保存轨迹地图
coordinates = [(data[0], data[1]) for data in gps_data]
create_map_with_trajectory(coordinates, output_map_path)
