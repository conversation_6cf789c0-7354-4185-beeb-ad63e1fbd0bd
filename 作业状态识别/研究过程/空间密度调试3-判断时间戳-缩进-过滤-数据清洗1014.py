import pandas as pd
import numpy as np
from sklearn.neighbors import NearestNeighbors
import folium
from folium import plugins
from haversine import haversine

# 加载数据
data = pd.read_csv('1014_cleaned.csv')

# 确保数据按照时间戳排序
data.sort_values(by='time', inplace=True)

# 计算所有相邻点对之间的距离
def calculate_distances(data):
    distances = []
    for i in range(len(data) - 1):
        loc1 = (data.iloc[i]['latitude'], data.iloc[i]['longitude'])
        loc2 = (data.iloc[i + 1]['latitude'], data.iloc[i + 1]['longitude'])
        distance = haversine(loc1, loc2)  # 使用haversine公式计算两点间的距离
        distances.append(distance)
    return distances

# 计算距离并计算总距离、平均距离
distances = calculate_distances(data)
total_distance_km = np.sum(distances)
average_distance_km = np.round(np.mean(distances), 3)

# 转换经纬度为弧度用于距离计算
coords = np.radians(data[['latitude', 'longitude']])

# 使用NearestNeighbors快速找到每个点的最近邻
avg_distance_meters = average_distance_km * 1000
n_neighbors = int(avg_distance_meters * 0.5)  # 需要是整数
neigh = NearestNeighbors(n_neighbors=n_neighbors, metric='haversine')
neigh.fit(coords)

# 计算最近邻的距离
distances, _ = neigh.kneighbors(coords)
# 计算平均距离并转换为公里
distances_km = np.mean(distances[:, 1:], axis=1) * 6371  # 6371是地球半径

# 设置阈值为平均距离的n倍
distance_threshold_km = average_distance_km * 1

# 标记状态
data['status'] = np.where(distances_km < distance_threshold_km, 'Work', 'Non-work')

# 新增：识别连续工作区域，并提取起始和结束点
# 首先，创建一个标记连续区域变化的列
data['shifted_status'] = data['status'].shift(1)  # 将状态列向下移动一位
data['status_change'] = data['status'] != data['shifted_status']

# 设置连续工作状态的最小阈值和内缩的点数
work_threshold = 20  # 设置阈值为n
offset = 2  # 设置内缩n个点

# 新增：识别连续工作区域，并考虑内缩和阈值
work_start_end = []
current_start = None
adjusted_points = set()

for i, row in data.iterrows():
    if row['status'] == 'Work':
        if current_start is None:
            current_start = i
    else:
        if current_start is not None:
            work_length = i - current_start
            if work_length >= work_threshold:
                start_index = max(current_start + offset, 0)
                end_index = min(i - offset - 1, len(data) - 1)
                work_start_end.append((start_index, end_index))
                adjusted_points.update(range(current_start, start_index))
                adjusted_points.update(range(end_index + 1, i))
            else:
                adjusted_points.update(range(current_start, i))
            current_start = None

# 检查最后一段工作区域是否满足阈值条件
if current_start is not None:
    work_length = len(data) - current_start
    if work_length >= work_threshold:
        start_index = max(current_start + offset, 0)
        end_index = min(len(data) - offset - 1, len(data) - 1)
        work_start_end.append((start_index, end_index))
        adjusted_points.update(range(current_start, start_index))
        adjusted_points.update(range(end_index + 1, len(data)))
    else:
        adjusted_points.update(range(current_start, len(data)))

# ...之前的代码保持不变...

# 更新点的状态：标记那些被内缩或阈值调整的点
data['adjusted'] = False
for i in range(len(data)):
    if i in adjusted_points:
        data.at[i, 'adjusted'] = True

# 初始化地图
map_center = data[['latitude', 'longitude']].mean().values
map = folium.Map(location=map_center, zoom_start=12)

# 创建带方向的轨迹
locations = data[['latitude', 'longitude']].values
plugins.AntPath(locations, color='blue', weight=2, opacity=1, delay=5000).add_to(map)

# 在地图上标记点
for i, row in data.iterrows():
    # 选择颜色：调整的点为黄色，工作状态为绿色，非工作状态为红色
    color = 'black' if row['adjusted'] else ('green' if row['status'] == 'Work' else 'red')
    folium.CircleMarker(location=(row['latitude'], row['longitude']), radius=3, color=color, fill=True).add_to(map)

# 标记整个坐标集的起点和终点
folium.CircleMarker(location=locations[0], radius=10, color='black', fill=True, fill_color='green', fill_opacity=1).add_to(map)
folium.CircleMarker(location=locations[-1], radius=10, color='black', fill=True, fill_color='red', fill_opacity=1).add_to(map)

# 标记每个连续工作地块的缩进后的起点和终点
group_number = 1
for start, end in work_start_end:
    start_location = data.iloc[start][['latitude', 'longitude']].values
    end_location = data.iloc[end][['latitude', 'longitude']].values
    # 标记起点
    folium.Marker(start_location, icon=folium.DivIcon(html=f'<div style="font-size: 12pt; color : red">{group_number}起</div>')).add_to(map)
    # 标记终点
    folium.Marker(end_location, icon=folium.DivIcon(html=f'<div style="font-size: 12pt; color : blue">{group_number}终</div>')).add_to(map)
    group_number += 1

# 保存地图
map.save('1014E.html')

# 打印信息
print(f"总距离: {total_distance_km:.3f} 公里")
print(f"平均每点距离: {average_distance_km:.3f} 公里")
print(data['status'].value_counts())

# 打印每组被识别为连续工作区域的最终起点和终点
for group_number, (start, end) in enumerate(work_start_end, start=1):
    start_location = data.iloc[start][['latitude', 'longitude']].values
    end_location = data.iloc[end][['latitude', 'longitude']].values
    print(f"工作区域 {group_number}: 起点坐标 {start_location} (索引 {start}), 终点坐标 {end_location} (索引 {end})")


# 如果数据中包含时间戳，我们可以计算总时长
if 'time' in data.columns:
    # 将时间戳从字符串转换为datetime对象
    data['time'] = pd.to_datetime(data['time'], format='%Y%m%d%H%M%S')
    total_time_seconds = (data['time'].iloc[-1] - data['time'].iloc[0]).total_seconds()
    total_time_hours = total_time_seconds / 3600
    print(f"总时长: {total_time_hours:.3f} 小时")

    # 直接使用点速度的平均值作为平均速度
    average_speed_kmh = data['speed'].mean()
    print(f"平均速度: {average_speed_kmh:.3f} 公里/小时")
else:
    print("没有时间数据无法计算总时长。")


