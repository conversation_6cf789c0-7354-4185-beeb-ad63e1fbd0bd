import pandas as pd
import numpy as np
from sklearn.neighbors import NearestNeighbors
import folium
from folium import plugins
from haversine import haversine

# 加载数据
data = pd.read_csv('20221016.csv')

# 确保数据按照时间戳排序
data.sort_values(by='time', inplace=True)

# 计算所有相邻点对之间的距离
def calculate_distances(data):
    distances = []
    for i in range(len(data) - 1):
        loc1 = (data.iloc[i]['latitude'], data.iloc[i]['longitude'])
        loc2 = (data.iloc[i + 1]['latitude'], data.iloc[i + 1]['longitude'])
        distance = haversine(loc1, loc2)  # 使用haversine公式计算两点间的距离
        distances.append(distance)
    return distances

# 计算距离并计算总距离、平均距离
distances = calculate_distances(data)
total_distance_km = np.sum(distances)
average_distance_km = np.round(np.mean(distances), 3)

# 转换经纬度为弧度用于距离计算
coords = np.radians(data[['latitude', 'longitude']])

# 使用NearestNeighbors快速找到每个点的最近邻
avg_distance_meters = average_distance_km * 1000
n_neighbors = int(avg_distance_meters * 2.5)  # 需要是整数
neigh = NearestNeighbors(n_neighbors=n_neighbors, metric='haversine')
neigh.fit(coords)

# 计算最近邻的距离
distances, _ = neigh.kneighbors(coords)
# 计算平均距离并转换为公里
distances_km = np.mean(distances[:, 1:], axis=1) * 6371  # 6371是地球半径

# 设置阈值为平均距离的两倍
distance_threshold_km = average_distance_km * 3

# 标记状态
data['status'] = np.where(distances_km < distance_threshold_km, 'Work', 'Non-work')

# 新增：识别连续工作区域，并提取起始和结束点
# 首先，创建一个标记连续区域变化的列
data['shifted_status'] = data['status'].shift(1)  # 将状态列向下移动一位
data['status_change'] = data['status'] != data['shifted_status']

# 设置内缩的点数
offset = 2  # 比如内缩2个点

# 新增：识别连续工作区域，并考虑内缩
work_start_end = []
current_start = None
for i, row in data.iterrows():
    if row['status'] == 'Work':
        if current_start is None:
            current_start = i  # 工作开始
    else:
        if current_start is not None:
            # 确保起始和结束点不超出数据范围
            start_index = max(current_start + offset, 0)
            end_index = min(i - offset, len(data) - 1)
            work_start_end.append((start_index, end_index))  # 工作结束
            current_start = None

# 检查最后一个区域是否为工作状态
if current_start is not None:
    end_index = min(current_start + offset, len(data) - 1)
    work_start_end.append((end_index, data.index[-1]))

# 输出工作区域的起始和结束点信息
for start, end in work_start_end:
    print(f"工作区域开始于索引 {start} (坐标: {data.iloc[start][['latitude', 'longitude']].values}), " +
          f"结束于索引 {end} (坐标: {data.iloc[end][['latitude', 'longitude']].values})")

# 初始化地图
map_center = data[['latitude', 'longitude']].mean().values
map = folium.Map(location=map_center, zoom_start=12)

group_number = 1
for start, end in work_start_end:
    start_location = data.iloc[start][['latitude', 'longitude']].values
    end_location = data.iloc[end][['latitude', 'longitude']].values

    # 标记起点
    folium.Marker(
        start_location,
        icon=folium.DivIcon(html=f'<div style="font-size: 12pt; color : red">{group_number}起</div>')
    ).add_to(map)

    # 标记终点
    folium.Marker(
        end_location,
        icon=folium.DivIcon(html=f'<div style="font-size: 12pt; color : blue">{group_number}终</div>')
    ).add_to(map)

    group_number += 1

# 创建轨迹
locations = data[['latitude', 'longitude']].values
# 使用AntPath添加带有方向的轨迹
plugins.AntPath(locations, color='blue', weight=2.5, opacity=1, delay=5000).add_to(map)

# 标记起点
folium.CircleMarker(location=locations[0],
                    radius=10, color='yellow', fill=True,
                    fill_color='green', fill_opacity=1).add_to(map)

# 标记终点
folium.CircleMarker(location=locations[-1],
                    radius=10, color='purple', fill=True,
                    fill_color='red', fill_opacity=1).add_to(map)

# 标记点
for _, row in data.iterrows():
    color = 'green' if row['status'] == 'Work' else 'red'
    folium.CircleMarker(location=(row['latitude'], row['longitude']),
                        radius=3, color=color, fill=True).add_to(map)

# 保存地图
map.save('1016A.html')

# 打印信息
print(f"总距离: {total_distance_km:.3f} 公里")
print(f"平均每点距离: {average_distance_km:.3f} 公里")
print(data['status'].value_counts())

# 如果数据中包含时间戳，我们可以计算总时长和平均速度
if 'time' in data.columns:
    # 假设时间戳格式是YYYYMMDDHHMMSS
    data['time'] = pd.to_datetime(data['time'], format='%Y%m%d%H%M%S')
    total_time_seconds = (data['time'].iloc[-1] - data['time'].iloc[0]).total_seconds()
    total_time_hours = total_time_seconds / 3600
    average_speed_kmh = total_distance_km / total_time_hours if total_time_hours > 0 else 0
    print(f"总时长: {total_time_hours:.3f} 小时")
    print(f"平均速度: {average_speed_kmh:.3f} 公里/小时")
else:
    print("没有时间数据无法计算总时长和平均速度。")
