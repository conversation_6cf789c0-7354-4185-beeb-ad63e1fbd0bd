import pandas as pd
import numpy as np
from sklearn.neighbors import NearestNeighbors
import folium
from haversine import haversine

# 加载数据
data = pd.read_csv('20221016.csv')
# 转换经纬度为弧度用于距离计算
coords = np.radians(data[['latitude', 'longitude']])

# 使用NearestNeighbors快速找到每个点的最近邻
neigh = NearestNeighbors(n_neighbors=13, metric='haversine')  # n_neighbors设置为11是因为最近的10个加上点本身
neigh.fit(coords)

# 计算最近邻的距离（第一列是点自身到自身的距离，所以我们取2到最后）
distances, _ = neigh.kneighbors(coords)
# 计算除自身外的最近10个邻居的平均距离
distances = np.mean(distances[:, 1:], axis=1)

# 将平均距离转换为公里并添加到data中
data['avg_distance_to_neighbors_km'] = distances * 6371  # 6371是地球半径

# 设置一个阈值来确定是否为工作状态，单位为公里
distance_threshold_km = 0.013*2  # 根据您的实际情况调整这个值

# 根据平均距离来标记每个点的状态
data['status'] = np.where(data['avg_distance_to_neighbors_km'] < distance_threshold_km, 'Work', 'Non-work')

# 初始化地图
map_center = data[['latitude', 'longitude']].mean().values
map = folium.Map(location=map_center, zoom_start=12)

# 根据状态在地图上标记每个点
for _, row in data.iterrows():
    color = 'green' if row['status'] == 'Work' else 'red'
    folium.CircleMarker(location=(row['latitude'], row['longitude']),
                        radius=3, color=color, fill=True).add_to(map)

# 保存地图为HTML文件
map.save('map_with_status.html')

# 打印状态统计信息
print(data['status'].value_counts())
