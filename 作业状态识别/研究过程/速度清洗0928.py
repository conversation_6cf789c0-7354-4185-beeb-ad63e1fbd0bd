import pandas as pd
from haversine import haversine
from datetime import datetime  # 确保导入datetime模块

# 加载数据
data = pd.read_csv('20220928.csv')

# 确保数据按照时间戳排序
data.sort_values(by='time', inplace=True)

# 时间差计算函数
def calculate_time_delta(time1, time2):
    # 将时间戳从字符串转换为datetime对象进行差值计算
    dt_format = '%Y%m%d%H%M%S'
    # 确保只包含符合格式的部分，排除小数点及其后的数字
    time1_str = str(time1).split('.')[0]
    time2_str = str(time2).split('.')[0]
    datetime1 = datetime.strptime(time1_str, dt_format)
    datetime2 = datetime.strptime(time2_str, dt_format)
    # 计算两个时间点之间的总秒数
    time_delta_seconds = (datetime2 - datetime1).total_seconds()
    return time_delta_seconds / 3600  # 返回小时单位的时间差


# 计算速度的函数
def calculate_speed(data):
    speeds = []
    for i in range(len(data) - 1):
        loc1 = (data.iloc[i]['latitude'], data.iloc[i]['longitude'])
        loc2 = (data.iloc[i + 1]['latitude'], data.iloc[i + 1]['longitude'])
        distance = haversine(loc1, loc2)  # 单位：公里
        time_delta_hours = calculate_time_delta(data.iloc[i]['time'], data.iloc[i + 1]['time'])  # 单位：小时
        speed = distance / time_delta_hours if time_delta_hours else 0  # 单位：公里/小时
        speeds.append(speed)
    speeds.append(0)  # 给最后一个点赋速度值0，因为没有下一个点进行计算
    return speeds

# 计算每个点的速度
data['speed'] = calculate_speed(data)

# 筛选出速度在2到20公里/小时之间的数据点
cleaned_data = data[(data['speed'] >= 2) & (data['speed'] <= 15)]


# 保存清洗后的数据
cleaned_data.to_csv('0928_cleaned.csv', index=False)

print(f"原始数据点数量: {len(data)}, 清洗后的数据点数量: {len(cleaned_data)}")
