import pandas as pd
import folium
import numpy as np

def calculate_distance(lat1, lon1, lat2, lon2):
    # 地球半径（公里）
    R = 6371.0

    # 将经纬度转换为弧度
    lat1_rad = np.radians(lat1)
    lon1_rad = np.radians(lon1)
    lat2_rad = np.radians(lat2)
    lon2_rad = np.radians(lon2)

    # 经纬度差值
    dlon = lon2_rad - lon1_rad
    dlat = lat2_rad - lat1_rad

    # 应用哈弗赛公式
    a = np.sin(dlat / 2)**2 + np.cos(lat1_rad) * np.cos(lat2_rad) * np.sin(dlon / 2)**2
    c = 2 * np.arctan2(np.sqrt(a), np.sqrt(1 - a))

    # 计算距离
    distance = R * c
    return distance

# 读取数据
file_path = '20221016.csv'
data = pd.read_csv(file_path)

# 确保数据包含经纬度和时间戳
if not all(x in data.columns for x in ['latitude', 'longitude', 'time']):
    raise ValueError("Data must include 'latitude', 'longitude', and 'time' columns")

# 转换时间戳
data['time'] = pd.to_datetime(data['time'])

# 计算作业时长
time_diff = data['time'].iloc[-1] - data['time'].iloc[0]
work_duration = time_diff.total_seconds() / 3600  # 小时

# 计算总轨迹距离
total_distance = 0.0  # 公里
for i in range(1, len(data)):
    total_distance += calculate_distance(
        data['latitude'].iloc[i-1], data['longitude'].iloc[i-1],
        data['latitude'].iloc[i], data['longitude'].iloc[i]
    )

# 计算平均速度
average_speed = total_distance / work_duration  # 公里/小时

# 创建轨迹图
map = folium.Map(location=[data['latitude'].mean(), data['longitude'].mean()], zoom_start=13)
folium.PolyLine(data[['latitude', 'longitude']].values, color="blue", weight=2.5, opacity=1).add_to(map)

# 添加起始和结束点
folium.Marker([data['latitude'].iloc[0], data['longitude'].iloc[0]], popup='Start').add_to(map)
folium.Marker([data['latitude'].iloc[-1], data['longitude'].iloc[-1]], popup='End').add_to(map)

# 保存轨迹图
map_file_path = 'tractor_trajectory_map.html'
map.save(map_file_path)

# 输出结果
results = {
    'Work Duration (hours)': work_duration,
    'Total Distance (km)': total_distance,
    'Average Speed (km/h)': average_speed
}

results, map_file_path
