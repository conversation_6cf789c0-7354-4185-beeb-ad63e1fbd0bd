import pandas as pd
import numpy as np
from sklearn.neighbors import NearestNeighbors
import folium
from folium import plugins
from haversine import haversine

# 加载数据
data = pd.read_csv('20221016.csv')

# 确保数据按照时间戳排序
data.sort_values(by='time', inplace=True)

# 计算所有相邻点对之间的距离
def calculate_distances(data):
    distances = []
    for i in range(len(data) - 1):
        loc1 = (data.iloc[i]['latitude'], data.iloc[i]['longitude'])
        loc2 = (data.iloc[i + 1]['latitude'], data.iloc[i + 1]['longitude'])
        distance = haversine(loc1, loc2)  # 使用haversine公式计算两点间的距离
        distances.append(distance)
    return distances

# 计算距离并计算总距离、平均距离
distances = calculate_distances(data)
total_distance_km = np.sum(distances)
average_distance_km = np.round(np.mean(distances), 3)

# 转换经纬度为弧度用于距离计算
coords = np.radians(data[['latitude', 'longitude']])

# 使用NearestNeighbors快速找到每个点的最近邻
avg_distance_meters = average_distance_km * 1000
n_neighbors = int(avg_distance_meters * 1)  # 需要是整数
neigh = NearestNeighbors(n_neighbors=n_neighbors, metric='haversine')
neigh.fit(coords)

# 计算最近邻的距离
distances, _ = neigh.kneighbors(coords)
# 计算平均距离并转换为公里
distances_km = np.mean(distances[:, 1:], axis=1) * 6371  # 6371是地球半径

# 设置阈值为平均距离的两倍
distance_threshold_km = average_distance_km * 2

# 标记状态
data['status'] = np.where(distances_km < distance_threshold_km, 'Work', 'Non-work')

# 初始化地图
map_center = data[['latitude', 'longitude']].mean().values
map = folium.Map(location=map_center, zoom_start=12)

# 创建轨迹
locations = data[['latitude', 'longitude']].values
# 使用AntPath添加带有方向的轨迹
plugins.AntPath(locations, color='blue', weight=2.5, opacity=1, delay=5000).add_to(map)

# 标记起点
folium.CircleMarker(location=locations[0],
                    radius=10, color='yellow', fill=True,
                    fill_color='green', fill_opacity=1).add_to(map)

# 标记终点
folium.CircleMarker(location=locations[-1],
                    radius=10, color='purple', fill=True,
                    fill_color='red', fill_opacity=1).add_to(map)

# 标记点
for _, row in data.iterrows():
    color = 'green' if row['status'] == 'Work' else 'red'
    folium.CircleMarker(location=(row['latitude'], row['longitude']),
                        radius=3, color=color, fill=True).add_to(map)

# 保存地图
map.save('map_with_status_and_trajectory.html')

# 打印信息
print(f"总距离: {total_distance_km:.3f} 公里")
print(f"平均每点距离: {average_distance_km:.3f} 公里")
print(data['status'].value_counts())

# 如果数据中包含时间戳，我们可以计算总时长和平均速度
if 'time' in data.columns:
    # 假设时间戳格式是YYYYMMDDHHMMSS
    data['time'] = pd.to_datetime(data['time'], format='%Y%m%d%H%M%S')
    total_time_seconds = (data['time'].iloc[-1] - data['time'].iloc[0]).total_seconds()
    total_time_hours = total_time_seconds / 3600
    average_speed_kmh = total_distance_km / total_time_hours if total_time_hours > 0 else 0
    print(f"总时长: {total_time_hours:.3f} 小时")
    print(f"平均速度: {average_speed_kmh:.3f} 公里/小时")
else:
    print("没有时间数据无法计算总时长和平均速度。")
