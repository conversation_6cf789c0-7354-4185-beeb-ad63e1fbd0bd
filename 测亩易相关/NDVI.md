# NDVI功能集成项目文档

## 目录
- [1. 前言](#1-前言)
- [2. 核心需求概述](#2-核心需求概述)
- [3. 项目背景与需求](#3-项目背景与需求)
  - [3.1 项目背景](#31-项目背景)
  - [3.2 当前基础设施](#32-当前基础设施)
  - [3.3 核心需求](#33-核心需求)
  - [3.4 功能定位](#34-功能定位)
- [4. 当前进展](#4-当前进展)
  - [4.1 NDVI服务调研（2025-02-18）](#41-ndvi服务调研2025-02-18)


## 1. 前言
因AI助手在长期任务中的记忆限制，本文档作为项目的"外部记忆载体"，记录了项目的完整需求、开发进展和关键决策。本文档将帮助我们：
- 准确完整的理解项目需求
- 追踪项目当前进展和决策过程
- 便于我和你也就是AI助手之间的沟通和理解
- 作为后续开发和维护的参考依据

## 2. 核心需求概述
为"测亩易APP"集成NDVI数据展示功能，通过直观的数据展示方式，帮助农户更好地监测农作物的生长状况。

## 3. 项目背景与需求
### 3.1 项目背景
- 测亩易APP是一个面向农业用户的数字农业工具
- 目前已重度使用卫星影像用于农用地块识别和管理
- 希望通过NDVI功能增强对农作物生长状况的监测能力

### 3.2 当前基础设施
1. **地图服务**
   - 主要使用Google Maps作为底图
   - APP支持多图层叠加显示
   - 已有地块边界数据管理能力

2. **使用环境限制**
   - 主要服务中国大陆用户
   - 需考虑国内网络访问限制
   - 数据存储需要考虑合规性
   - 可能需要本地化部署

### 3.3 核心需求
1. **目标**
   - 为"测亩易APP"集成NDVI数据展示功能
   - 提升农作物生长状况的监测能力
   - 使农户能更直观、简单地查看作物生长状态

2. **关键功能**
   - 在现有地块管理功能基础上集成NDVI数据展示
   - 数据展示应包括最近几天的NDVI数据，不要求实时
   - 简单直观的展示作物生长状况

3. **NDVI数据源需求**
   - 找到可以覆盖中国大陆地区的可靠NDVI数据服务提供商
   - 数据服务商需要提供标准API接口
   - 确保数据更新频率满足农业监测需求
   - 保证在中国大陆地区提供稳定的访问

### 3.4 功能定位
- 作为地块查看的扩展功能
- 可能单独设置为"遥感"或"作物生长监控"图层
- 主要服务于农作物生长监测需求

## 4. 当前进展
### 4.1 NDVI服务调研（2025-02-18）

1. **需求明确**
   - 需要找到可靠的NDVI数据服务提供商
   - 服务必须覆盖中国大陆地区
   - 能提供标准API接口
   - 数据更新频率满足农业监测需求

2. **可选服务商推荐**
   A. **Google Earth Engine**
      - 谷歌提供的地球观测平台
      - 优势：
        - 免费提供Sentinel-2和Landsat数据
        - 支持NDVI等多种植被指数计算
        - 提供完整的JavaScript和Python API
        - 强大的云计算能力
        - 数据更新及时
      - 注意事项：
        - 需要申请GEE账号
        - 在中国大陆访问可能不稳定
        - 商业使用需要单独申请
      - 网址：https://earthengine.google.com/

   B. **Sentinel Hub**
      - 欧空局提供的专业遥感服务平台
      - 优势：
        - 基于Sentinel-2卫星数据
        - 提供标准REST API
        - 支持NDVI等多种植被指数
        - 全球覆盖，包括中国区
        - 定价透明，可按需付费
      - 网址：https://www.sentinel-hub.com/

   C. **Microsoft Planetary Computer**
      - 微软提供的地球观测数据平台
      - 优势：
        - 免费提供Sentinel-2数据
        - 提供Python SDK
        - 支持NDVI计算
        - 服务稳定可靠
      - 网址：https://planetarycomputer.microsoft.com/

   D. **航天宏图 PIE-Engine**
      - 国内领先的遥感服务平台
      - 优势：
        - 服务器部署在国内，访问稳定
        - 支持多种卫星数据源（包括国产卫星）
        - 提供标准REST API
        - 支持NDVI等多种植被指数
        - 有完整的农业遥感解决方案
        - 商业化服务成熟
      - 网址：https://engine.piesat.cn/

   E. **中科星图 GEOVIS**
      - 中科院系的遥感服务平台
      - 优势：
        - 基于自主卫星数据
        - 提供完整API
        - 支持农业监测
        - 服务器在国内
      - 网址：http://geovis.com.cn/

   F. **千寻位置 青寻遥感**
      - 阿里系遥感服务平台
      - 优势：
        - 提供Sentinel-2等多源卫星数据
        - 支持NDVI等植被指数
        - 服务稳定，技术支持好
        - 有完整的开发文档
      - 网址：https://www.qianxin.com/








