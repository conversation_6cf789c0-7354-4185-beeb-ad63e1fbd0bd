<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字种植管理系统价值分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        .slide {
            display: none;
            animation: slideIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            height: 100vh;
        }

        .slide.active {
            display: flex;
            flex-direction: column;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.98);
            }

            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .card-elegant {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(20px);
        }

        .card-elegant:hover {
            transform: translateY(-4px) scale(1.01);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .icon-breathe {
            animation: breathe 4s ease-in-out infinite;
        }

        @keyframes breathe {

            0%,
            100% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.03);
            }
        }

        .gradient-apple {
            background: linear-gradient(135deg,
                    rgba(255, 255, 255, 0.15) 0%,
                    rgba(255, 255, 255, 0.08) 100%);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .nav-apple {
            background: rgba(0, 0, 0, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .text-shadow {
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .slide-header {
            height: 70px;
            flex-shrink: 0;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
        }

        .slide-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
    </style>
</head>

<body class="bg-black overflow-hidden">
    <div class="w-full">

        <!-- Slide 1 - Title -->
        <div class="slide active bg-gradient-to-br from-gray-900 via-black to-gray-800 text-white" id="slide-0">
            <div class="slide-header border-b border-white border-opacity-5">
                <div class="flex justify-between items-center px-8 py-4">
                    <h2 class="text-2xl font-medium tracking-wide">数字种植管理系统</h2>
                    <div class="bg-white bg-opacity-10 px-5 py-2 rounded-full border border-white border-opacity-10">
                        <span id="currentPage" class="text-lg">1</span> <span class="opacity-50">of</span> <span
                            id="totalPages" class="text-lg">7</span>
                    </div>
                </div>
            </div>
            <div class="slide-content justify-center items-center text-center px-8 py-8">
                <div>
                    <div class="text-9xl mb-8 icon-breathe">🌱</div>
                    <h1 class="text-8xl font-thin mb-8 tracking-tight text-shadow leading-tight">
                        数字种植管理<br>
                        <span
                            class="bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent font-light">
                            系统的价值
                        </span>
                    </h1>
                    <p class="text-4xl font-light opacity-80 mb-12 tracking-wide">智慧农业解决方案</p>
                    <div class="flex justify-center space-x-8">
                        <div
                            class="bg-gradient-to-r from-blue-500 to-blue-600 px-10 py-5 rounded-2xl text-2xl font-medium shadow-lg">
                            智能化管理</div>
                        <div
                            class="bg-gradient-to-r from-green-500 to-green-600 px-10 py-5 rounded-2xl text-2xl font-medium shadow-lg">
                            数据驱动决策</div>
                        <div
                            class="bg-gradient-to-r from-purple-500 to-purple-600 px-10 py-5 rounded-2xl text-2xl font-medium shadow-lg">
                            价值链协同</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 2 - 酒厂价值 -->
        <div class="slide bg-gradient-to-br from-blue-600 to-indigo-900 text-white" id="slide-1">
            <div class="slide-header border-b border-white border-opacity-5">
                <div class="flex justify-between items-center px-8 py-4">
                    <h2 class="text-2xl font-medium tracking-wide">数字种植管理系统</h2>
                    <div class="bg-white bg-opacity-10 px-5 py-2 rounded-full border border-white border-opacity-10">
                        <span class="current-page text-lg">2</span> <span class="opacity-50">of</span> <span
                            class="text-lg">7</span>
                    </div>
                </div>
            </div>
            <div class="slide-content p-8">
                <div class="flex items-center space-x-6 mb-10">
                    <div class="text-8xl icon-breathe">🏭</div>
                    <div>
                        <h1 class="text-6xl font-thin tracking-tight text-shadow">对上游酒厂的价值</h1>
                        <p class="text-2xl font-light opacity-80 mt-4">原料品质保障 + 供应链透明化</p>
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-6 flex-1">
                    <div class="card-elegant gradient-apple rounded-3xl p-8">
                        <div class="flex items-start space-x-6 mb-6">
                            <div class="bg-gradient-to-r from-blue-400 to-blue-600 p-4 rounded-2xl text-5xl shadow-lg">
                                📋</div>
                            <div>
                                <h3 class="text-3xl font-medium mb-4 tracking-tight">全程可追溯性</h3>
                                <p class="text-xl font-light opacity-90 leading-relaxed">
                                    通过数字田块管理，记录每块地的种植过程，包括种子来源、种植收获时间、施肥用药记录等，满足酒厂对原料溯源的需求</p>
                            </div>
                        </div>
                        <div class="bg-blue-500 bg-opacity-25 rounded-2xl px-8 py-4 text-center">
                            <span class="text-xl font-medium">数字田块管理</span>
                        </div>
                    </div>

                    <div class="card-elegant gradient-apple rounded-3xl p-8">
                        <div class="flex items-start space-x-6 mb-6">
                            <div
                                class="bg-gradient-to-r from-green-400 to-green-600 p-4 rounded-2xl text-5xl shadow-lg">
                                🎯</div>
                            <div>
                                <h3 class="text-3xl font-medium mb-4 tracking-tight">品质保证</h3>
                                <p class="text-xl font-light opacity-90 leading-relaxed">
                                    系统可以记录种植过程中的关键节点数据，如灌溉、施肥、病虫害防治等，帮助酒厂评估原料品质</p>
                            </div>
                        </div>
                        <div class="bg-green-500 bg-opacity-25 rounded-2xl px-8 py-4 text-center">
                            <span class="text-xl font-medium">关键节点记录</span>
                        </div>
                    </div>

                    <div class="card-elegant gradient-apple rounded-3xl p-8">
                        <div class="flex items-start space-x-6 mb-6">
                            <div
                                class="bg-gradient-to-r from-purple-400 to-purple-600 p-4 rounded-2xl text-5xl shadow-lg">
                                📈</div>
                            <div>
                                <h3 class="text-3xl font-medium mb-4 tracking-tight">供应稳定性</h3>
                                <p class="text-xl font-light opacity-90 leading-relaxed">
                                    通过数字化管理，可以更好地预测产量，提前做好生产计划，降低供应风险</p>
                            </div>
                        </div>
                        <div class="bg-purple-500 bg-opacity-25 rounded-2xl px-8 py-4 text-center">
                            <span class="text-xl font-medium">产量预测</span>
                        </div>
                    </div>

                    <div class="card-elegant gradient-apple rounded-3xl p-8">
                        <div class="flex items-start space-x-6 mb-6">
                            <div class="bg-gradient-to-r from-pink-400 to-pink-600 p-4 rounded-2xl text-5xl shadow-lg">
                                📊</div>
                            <div>
                                <h3 class="text-3xl font-medium mb-4 tracking-tight">数据可视化</h3>
                                <p class="text-xl font-light opacity-90 leading-relaxed">为酒厂提供直观的种植数据报表，增强对供应链的信任度</p>
                            </div>
                        </div>
                        <div class="bg-pink-500 bg-opacity-25 rounded-2xl px-8 py-4 text-center">
                            <span class="text-xl font-medium">数据报表</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 3 - 种植大户价值（一）-->
        <div class="slide bg-gradient-to-br from-green-600 to-emerald-900 text-white" id="slide-2">
            <div class="slide-header border-b border-white border-opacity-5">
                <div class="flex justify-between items-center px-8 py-4">
                    <h2 class="text-2xl font-medium tracking-wide">数字种植管理系统</h2>
                    <div class="bg-white bg-opacity-10 px-5 py-2 rounded-full border border-white border-opacity-10">
                        <span class="current-page text-lg">3</span> <span class="opacity-50">of</span> <span
                            class="text-lg">7</span>
                    </div>
                </div>
            </div>
            <div class="slide-content p-8">
                <div class="flex items-center space-x-6 mb-10">
                    <div class="text-8xl icon-breathe">🌾</div>
                    <div>
                        <h1 class="text-6xl font-thin tracking-tight text-shadow">对种植大户的价值</h1>
                        <p class="text-2xl font-light opacity-80 mt-4">解决痛点 · 提升效率</p>
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-6 flex-1">
                    <div class="card-elegant gradient-apple rounded-3xl p-8">
                        <div class="flex items-center space-x-6 mb-6">
                            <div class="bg-gradient-to-r from-red-400 to-red-600 p-4 rounded-2xl text-5xl shadow-lg">📝
                            </div>
                            <h3 class="text-3xl font-medium tracking-tight">种植记录痛点</h3>
                        </div>
                        <p class="text-xl font-light opacity-90 leading-relaxed">农事操作记录分散，缺乏系统性和标准性，难以追溯历史，经验难以传承</p>
                    </div>

                    <div class="card-elegant gradient-apple rounded-3xl p-8">
                        <div class="flex items-center space-x-6 mb-6">
                            <div
                                class="bg-gradient-to-r from-orange-400 to-orange-600 p-4 rounded-2xl text-5xl shadow-lg">
                                💰</div>
                            <h3 class="text-3xl font-medium tracking-tight">成本控制</h3>
                        </div>
                        <p class="text-xl font-light opacity-90 leading-relaxed">通过农资使用记录，监督和核算成本，帮助农户优化投入，降低生产成本</p>
                    </div>

                    <div class="card-elegant gradient-apple rounded-3xl p-8">
                        <div class="flex items-center space-x-6 mb-6">
                            <div class="bg-gradient-to-r from-blue-400 to-blue-600 p-4 rounded-2xl text-5xl shadow-lg">
                                🚜</div>
                            <h3 class="text-3xl font-medium tracking-tight">作业效率</h3>
                        </div>
                        <p class="text-xl font-light opacity-90 leading-relaxed">农机作业管理功能可以优化农机调度，提高作业效率，选择更好的设备和更优的农机手
                        </p>
                    </div>

                    <div class="card-elegant gradient-apple rounded-3xl p-8">
                        <div class="flex items-center space-x-6 mb-6">
                            <div
                                class="bg-gradient-to-r from-purple-400 to-purple-600 p-4 rounded-2xl text-5xl shadow-lg">
                                🛡️</div>
                            <h3 class="text-3xl font-medium tracking-tight">风险防控</h3>
                        </div>
                        <p class="text-xl font-light opacity-90 leading-relaxed">
                            通过数字化记录，可以更好、更快地应对自然灾害等风险，便于保险理赔（说得清，赔付快）</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 4 - 种植大户价值（二）-->
        <div class="slide bg-gradient-to-br from-emerald-600 to-teal-900 text-white" id="slide-3">
            <div class="slide-header border-b border-white border-opacity-5">
                <div class="flex justify-between items-center px-8 py-4">
                    <h2 class="text-2xl font-medium tracking-wide">数字种植管理系统</h2>
                    <div class="bg-white bg-opacity-10 px-5 py-2 rounded-full border border-white border-opacity-10">
                        <span class="current-page text-lg">4</span> <span class="opacity-50">of</span> <span
                            class="text-lg">7</span>
                    </div>
                </div>
            </div>
            <div class="slide-content p-8">
                <div class="flex items-center space-x-6 mb-10">
                    <div class="text-8xl icon-breathe">💎</div>
                    <div>
                        <h1 class="text-6xl font-thin tracking-tight text-shadow">种植大户核心收益</h1>
                        <p class="text-2xl font-light opacity-80 mt-4">全方位价值创造</p>
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-6 flex-1">
                    <div class="card-elegant gradient-apple rounded-3xl p-10 text-center">
                        <div
                            class="bg-gradient-to-r from-green-400 to-green-600 p-6 rounded-3xl w-24 h-24 flex items-center justify-center text-5xl mx-auto mb-8 shadow-lg">
                            📊</div>
                        <h3 class="text-3xl font-medium mb-6 tracking-tight">收益提升</h3>
                        <p class="text-xl font-light opacity-90 leading-relaxed">科学种植指导+成本控制，有助于提高产量和品质，增加收益</p>
                    </div>

                    <div class="card-elegant gradient-apple rounded-3xl p-10 text-center">
                        <div
                            class="bg-gradient-to-r from-blue-400 to-blue-600 p-6 rounded-3xl w-24 h-24 flex items-center justify-center text-5xl mx-auto mb-8 shadow-lg">
                            🏆</div>
                        <h3 class="text-3xl font-medium mb-6 tracking-tight">信用建设和生产能力证明</h3>
                        <p class="text-xl font-light opacity-90 leading-relaxed">通过数字化的全流程种植生产，可以向金融机构和客户证明自身的管理能力和盈利能力
                        </p>
                    </div>

                    <div class="card-elegant gradient-apple rounded-3xl p-10 text-center">
                        <div
                            class="bg-gradient-to-r from-purple-400 to-purple-600 p-6 rounded-3xl w-24 h-24 flex items-center justify-center text-5xl mx-auto mb-8 shadow-lg">
                            🚀</div>
                        <h3 class="text-3xl font-medium mb-6 tracking-tight">扩大生产规模</h3>
                        <p class="text-xl font-light opacity-90 leading-relaxed">用全流程的数字化生产内容，标准流程，突破管理瓶颈，扩大种植规模</p>
                    </div>

                    <div class="card-elegant gradient-apple rounded-3xl p-10 text-center">
                        <div
                            class="bg-gradient-to-r from-pink-400 to-pink-600 p-6 rounded-3xl w-24 h-24 flex items-center justify-center text-5xl mx-auto mb-8 shadow-lg">
                            🎖️</div>
                        <h3 class="text-3xl font-medium mb-6 tracking-tight">数字品牌</h3>
                        <p class="text-xl font-light opacity-90 leading-relaxed">节本增效的同时，更容易争取政府的项目扶持</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 5 - 供销社价值（一）-->
        <div class="slide bg-gradient-to-br from-indigo-600 to-purple-900 text-white" id="slide-4">
            <div class="slide-header border-b border-white border-opacity-5">
                <div class="flex justify-between items-center px-8 py-4">
                    <h2 class="text-2xl font-medium tracking-wide">数字种植管理系统</h2>
                    <div class="bg-white bg-opacity-10 px-5 py-2 rounded-full border border-white border-opacity-10">
                        <span class="current-page text-lg">5</span> <span class="opacity-50">of</span> <span
                            class="text-lg">7</span>
                    </div>
                </div>
            </div>
            <div class="slide-content p-8">
                <div class="flex items-center space-x-6 mb-10">
                    <div class="text-8xl icon-breathe">🏢</div>
                    <div>
                        <h1 class="text-6xl font-thin tracking-tight text-shadow">供销社核心价值</h1>
                        <p class="text-2xl font-light opacity-80 mt-4">数字化转型 · 竞争优势</p>
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-6 flex-1">
                    <div class="card-elegant gradient-apple rounded-3xl p-8">
                        <div class="flex items-start space-x-6 mb-6">
                            <div class="bg-gradient-to-r from-blue-400 to-blue-600 p-4 rounded-2xl text-5xl shadow-lg">
                                🔗</div>
                            <div>
                                <h3 class="text-3xl font-medium mb-4 tracking-tight">供应链管理</h3>
                                <p class="text-xl font-light opacity-90 leading-relaxed">可以实时掌握各个种植大户的种植情况，提前预判产量</p>
                            </div>
                        </div>
                        <div class="bg-blue-500 bg-opacity-25 rounded-2xl px-8 py-4 text-center">
                            <span class="text-xl font-medium">实时监控</span>
                        </div>
                    </div>

                    <div class="card-elegant gradient-apple rounded-3xl p-8">
                        <div class="flex items-start space-x-6 mb-6">
                            <div
                                class="bg-gradient-to-r from-green-400 to-green-600 p-4 rounded-2xl text-5xl shadow-lg">
                                ⚡</div>
                            <div>
                                <h3 class="text-3xl font-medium mb-4 tracking-tight">质量控制和提升</h3>
                                <p class="text-xl font-light opacity-90 leading-relaxed">
                                    通过系统记录的数据，可以更好地把控原料来源，通过数字化业务内容，对种植户进行合理化建议</p>
                            </div>
                        </div>
                        <div class="bg-green-500 bg-opacity-25 rounded-2xl px-8 py-4 text-center">
                            <span class="text-xl font-medium">数据驱动建议</span>
                        </div>
                    </div>

                    <div class="card-elegant gradient-apple rounded-3xl p-8">
                        <div class="flex items-start space-x-6 mb-6">
                            <div
                                class="bg-gradient-to-r from-purple-400 to-purple-600 p-4 rounded-2xl text-5xl shadow-lg">
                                🤝</div>
                            <div>
                                <h3 class="text-3xl font-medium mb-4 tracking-tight">供应商评估</h3>
                                <p class="text-xl font-light opacity-90 leading-relaxed">
                                    通过全面了解种植户的种植生产过程，有效评估单产效率，通过单产排名的模式筛选优质供应商，重点扶持，加强客户粘合</p>
                            </div>
                        </div>
                        <div class="bg-purple-500 bg-opacity-25 rounded-2xl px-8 py-4 text-center">
                            <span class="text-xl font-medium">单产排名</span>
                        </div>
                    </div>

                    <div class="card-elegant gradient-apple rounded-3xl p-8">
                        <div class="flex items-start space-x-6 mb-6">
                            <div class="bg-gradient-to-r from-orange-400 to-red-600 p-4 rounded-2xl text-5xl shadow-lg">
                                ⚠️</div>
                            <div>
                                <h3 class="text-3xl font-medium mb-4 tracking-tight">风险控制</h3>
                                <p class="text-xl font-light opacity-90 leading-relaxed">
                                    通过数字化管理，可以降低收储风险，确保供应稳定（通过对病虫害，气象灾害的记录，提早了解和统计对产量的影响）</p>
                            </div>
                        </div>
                        <div class="bg-orange-500 bg-opacity-25 rounded-2xl px-8 py-4 text-center">
                            <span class="text-xl font-medium">灾害预警</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 6 - 供销社价值（二）-->
        <div class="slide bg-gradient-to-br from-purple-600 to-pink-900 text-white" id="slide-5">
            <div class="slide-header border-b border-white border-opacity-5">
                <div class="flex justify-between items-center px-8 py-4">
                    <h2 class="text-2xl font-medium tracking-wide">数字种植管理系统</h2>
                    <div class="bg-white bg-opacity-10 px-5 py-2 rounded-full border border-white border-opacity-10">
                        <span class="current-page text-lg">6</span> <span class="opacity-50">of</span> <span
                            class="text-lg">7</span>
                    </div>
                </div>
            </div>
            <div class="slide-content p-8">
                <div class="flex items-center space-x-6 mb-10">
                    <div class="text-8xl icon-breathe">🌟</div>
                    <div>
                        <h1 class="text-6xl font-thin tracking-tight text-shadow">全面数字化转型</h1>
                        <p class="text-2xl font-light opacity-80 mt-4">效率 · 品牌 · 竞争力</p>
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-6 mb-8 flex-1">
                    <div class="card-elegant gradient-apple rounded-3xl p-10 text-center">
                        <div
                            class="bg-gradient-to-r from-yellow-400 to-orange-500 p-4 rounded-2xl text-5xl w-fit mx-auto mb-8 shadow-lg">
                            🏷️</div>
                        <h3 class="text-3xl font-medium mb-6 tracking-tight">品牌建设</h3>
                        <p class="text-xl font-light opacity-90 leading-relaxed">通过数字化管理，可以打造"智慧农业"品牌形象</p>
                    </div>

                    <div class="card-elegant gradient-apple rounded-3xl p-10 text-center">
                        <div
                            class="bg-gradient-to-r from-blue-400 to-cyan-500 p-4 rounded-2xl text-5xl w-fit mx-auto mb-8 shadow-lg">
                            💾</div>
                        <h3 class="text-3xl font-medium mb-6 tracking-tight">数据资产</h3>
                        <p class="text-xl font-light opacity-90 leading-relaxed">
                            积累的农业数据可以成为重要的数据资产（农机手，农机设备，数字田块，种植户能力的数据）</p>
                    </div>

                    <div class="card-elegant gradient-apple rounded-3xl p-10 text-center">
                        <div
                            class="bg-gradient-to-r from-green-400 to-emerald-500 p-4 rounded-2xl text-5xl w-fit mx-auto mb-8 shadow-lg">
                            ⚙️</div>
                        <h3 class="text-3xl font-medium mb-6 tracking-tight">管理效率</h3>
                        <p class="text-xl font-light opacity-90 leading-relaxed">
                            减少人工管理成本，提高管理效率。数字化的标准管理软件，方便供销社员工及时、准确对接上游酒厂和下游供应商</p>
                    </div>

                    <div class="card-elegant gradient-apple rounded-3xl p-10 text-center">
                        <div
                            class="bg-gradient-to-r from-emerald-400 to-teal-500 p-4 rounded-2xl text-5xl w-fit mx-auto mb-8 shadow-lg">
                            🌱</div>
                        <h3 class="text-3xl font-medium mb-6 tracking-tight">提高种子销售</h3>
                        <p class="text-xl font-light opacity-90 leading-relaxed">
                            作为一种增值产品，为种植户解决了生产管理系统缺乏的痛点（大户的雄心不止当前）；全面的种植记录有利于优化销售管理</p>
                    </div>
                </div>

                <div
                    class="card-elegant bg-gradient-to-r from-orange-500 to-red-600 rounded-3xl p-12 text-center shadow-2xl">
                    <div class="text-7xl mb-8 icon-breathe">🏅</div>
                    <h3 class="text-5xl font-light mb-8 tracking-tight">市场竞争力</h3>
                    <p class="text-2xl font-light opacity-95 leading-relaxed">通过数字化管理，可以提升在酒厂供应商中的竞争力，深化合作关系，提升合作价值</p>
                </div>
            </div>
        </div>

        <!-- Slide 7 - 实施建议 -->
        <div class="slide bg-gradient-to-br from-slate-800 to-gray-900 text-white" id="slide-6">
            <div class="slide-header border-b border-white border-opacity-5">
                <div class="flex justify-between items-center px-8 py-4">
                    <h2 class="text-2xl font-medium tracking-wide">数字种植管理系统</h2>
                    <div class="bg-white bg-opacity-10 px-5 py-2 rounded-full border border-white border-opacity-10">
                        <span class="current-page text-lg">7</span> <span class="opacity-50">of</span> <span
                            class="text-lg">7</span>
                    </div>
                </div>
            </div>
            <div class="slide-content p-8">
                <div class="flex items-center space-x-6 mb-10">
                    <div class="text-8xl icon-breathe">🎯</div>
                    <div>
                        <h1 class="text-6xl font-thin tracking-tight text-shadow">实施路径</h1>
                        <p class="text-2xl font-light opacity-80 mt-4">渐进式 · 可持续 · 高效果</p>
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-6 flex-1">
                    <div class="card-elegant gradient-apple rounded-3xl p-12 relative">
                        <div
                            class="absolute top-8 right-8 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full w-20 h-20 flex items-center justify-center text-3xl font-medium shadow-lg">
                            01</div>
                        <div class="text-center">
                            <div
                                class="bg-gradient-to-r from-blue-400 to-blue-600 p-6 rounded-3xl w-24 h-24 flex items-center justify-center text-5xl mx-auto mb-10 shadow-lg">
                                🚀</div>
                            <h3 class="text-3xl font-medium mb-8 tracking-tight">分步实施</h3>
                            <p class="text-xl font-light opacity-90">可以先从核心功能开始，逐步扩展</p>
                        </div>
                    </div>

                    <div class="card-elegant gradient-apple rounded-3xl p-12 relative">
                        <div
                            class="absolute top-8 right-8 bg-gradient-to-r from-green-400 to-green-600 rounded-full w-20 h-20 flex items-center justify-center text-3xl font-medium shadow-lg">
                            02</div>
                        <div class="text-center">
                            <div
                                class="bg-gradient-to-r from-green-400 to-green-600 p-6 rounded-3xl w-24 h-24 flex items-center justify-center text-5xl mx-auto mb-10 shadow-lg">
                                🎪</div>
                            <h3 class="text-3xl font-medium mb-8 tracking-tight">重点突破</h3>
                            <p class="text-xl font-light opacity-90">选择1-2个示范种植大户先行试点</p>
                        </div>
                    </div>

                    <div class="card-elegant gradient-apple rounded-3xl p-12 relative">
                        <div
                            class="absolute top-8 right-8 bg-gradient-to-r from-purple-400 to-purple-600 rounded-full w-20 h-20 flex items-center justify-center text-3xl font-medium shadow-lg">
                            03</div>
                        <div class="text-center">
                            <div
                                class="bg-gradient-to-r from-purple-400 to-purple-600 p-6 rounded-3xl w-24 h-24 flex items-center justify-center text-5xl mx-auto mb-10 shadow-lg">
                                📊</div>
                            <h3 class="text-3xl font-medium mb-8 tracking-tight">数据积累</h3>
                            <p class="text-xl font-light opacity-90">注重数据的收集和积累，为后续优化提供支持</p>
                        </div>
                    </div>

                    <div class="card-elegant gradient-apple rounded-3xl p-12 relative">
                        <div
                            class="absolute top-8 right-8 bg-gradient-to-r from-orange-400 to-red-500 rounded-full w-20 h-20 flex items-center justify-center text-3xl font-medium shadow-lg">
                            04</div>
                        <div class="text-center">
                            <div
                                class="bg-gradient-to-r from-orange-400 to-red-500 p-6 rounded-3xl w-24 h-24 flex items-center justify-center text-5xl mx-auto mb-10 shadow-lg">
                                🔄</div>
                            <h3 class="text-3xl font-medium mb-8 tracking-tight">持续优化</h3>
                            <p class="text-xl font-light opacity-90">根据实际使用情况，不断优化系统功能</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <div
            class="fixed bottom-6 left-1/2 transform -translate-x-1/2 nav-apple rounded-2xl px-6 py-3 flex items-center space-x-6 z-50">
            <button id="prevBtn" onclick="prevSlide()"
                class="px-5 py-2 bg-white bg-opacity-10 rounded-xl hover:bg-opacity-20 text-white text-sm font-medium transition-all border border-white border-opacity-10">
                ← 上一页
            </button>

            <div class="flex space-x-3" id="indicators">
                <div class="w-2 h-2 rounded-full bg-white indicator active cursor-pointer opacity-80" data-slide="0">
                </div>
                <div class="w-2 h-2 rounded-full bg-white indicator cursor-pointer opacity-30" data-slide="1"></div>
                <div class="w-2 h-2 rounded-full bg-white indicator cursor-pointer opacity-30" data-slide="2"></div>
                <div class="w-2 h-2 rounded-full bg-white indicator cursor-pointer opacity-30" data-slide="3"></div>
                <div class="w-2 h-2 rounded-full bg-white indicator cursor-pointer opacity-30" data-slide="4"></div>
                <div class="w-2 h-2 rounded-full bg-white indicator cursor-pointer opacity-30" data-slide="5"></div>
                <div class="w-2 h-2 rounded-full bg-white indicator cursor-pointer opacity-30" data-slide="6"></div>
            </div>

            <button id="nextBtn" onclick="nextSlide()"
                class="px-5 py-2 bg-white bg-opacity-10 rounded-xl hover:bg-opacity-20 text-white text-sm font-medium transition-all border border-white border-opacity-10">
                下一页 →
            </button>
        </div>
    </div>

    <script>
        let currentSlide = 0;
        const totalSlides = 7;

        function showSlide(n) {
            const slides = document.querySelectorAll('.slide');
            slides.forEach(slide => slide.classList.remove('active'));

            document.getElementById(`slide-${n}`).classList.add('active');

            const indicators = document.querySelectorAll('.indicator');
            indicators.forEach(indicator => {
                indicator.classList.remove('active');
                indicator.classList.add('opacity-30');
                indicator.classList.remove('opacity-80');
            });

            const activeIndicator = document.querySelector(`[data-slide="${n}"]`);
            activeIndicator.classList.add('active');
            activeIndicator.classList.remove('opacity-30');
            activeIndicator.classList.add('opacity-80');

            document.querySelectorAll('.current-page').forEach(el => {
                el.textContent = n + 1;
            });

            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');

            prevBtn.disabled = n === 0;
            nextBtn.disabled = n === totalSlides - 1;

            prevBtn.style.opacity = n === 0 ? '0.3' : '0.8';
            nextBtn.style.opacity = n === totalSlides - 1 ? '0.3' : '0.8';
        }

        function nextSlide() {
            if (currentSlide < totalSlides - 1) {
                currentSlide++;
                showSlide(currentSlide);
            }
        }

        function prevSlide() {
            if (currentSlide > 0) {
                currentSlide--;
                showSlide(currentSlide);
            }
        }

        document.querySelectorAll('.indicator').forEach((indicator, index) => {
            indicator.addEventListener('click', () => {
                currentSlide = index;
                showSlide(currentSlide);
            });
        });

        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowRight') nextSlide();
            if (e.key === 'ArrowLeft') prevSlide();
        });

        showSlide(0);
    </script>
</body>

</html>