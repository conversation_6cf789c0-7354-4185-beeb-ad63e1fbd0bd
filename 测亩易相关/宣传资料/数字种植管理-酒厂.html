<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字种植管理系统 | Digital Farming Management</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Framer Motion -->
    <script src="https://cdn.jsdelivr.net/npm/framer-motion@10.16.4/dist/framer-motion.js"></script>
    
    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- GSAP for animations -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#007AFF',
                        secondary: '#5AC8FA',
                        accent: '#34C759',
                        neutral: '#F2F2F7'
                    },
                    fontFamily: {
                        'sf': ['-apple-system', 'BlinkMacSystemFont', 'SF Pro Display', 'Helvetica Neue', 'sans-serif']
                    }
                }
            }
        }
    </script>
    
    <style>
        body {
            overflow: hidden;
        }
        
        .slide {
            width: 100vw;
            height: 100vh;
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0;
            visibility: hidden;
            transform: translateX(100%);
            transition: all 0.6s cubic-bezier(0.4, 0.0, 0.2, 1);
            z-index: 1;
        }
        
        .slide.active {
            opacity: 1;
            visibility: visible;
            transform: translateX(0);
            z-index: 10;
        }
        
        .slide.prev {
            transform: translateX(-100%);
            visibility: hidden;
        }
        
        .gradient-text {
            background: linear-gradient(135deg, #007AFF, #5AC8FA);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .bento-card {
            border-radius: 20px;
            transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
        }
        
        .bento-card:hover {
            transform: translateY(-8px);
        }
        
        .hero-number {
            font-size: clamp(4rem, 12vw, 12rem);
            font-weight: 800;
            line-height: 0.8;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .floating {
            animation: float 6s ease-in-out infinite;
        }
        
        .nav-controls {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 20px;
            background: rgba(0, 0, 0, 0.25);
            backdrop-filter: blur(10px);
            padding: 12px 20px;
            border-radius: 50px;
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .nav-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.15);
            border: none;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        
        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: scale(1.05);
        }
        
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .slide-indicator {
            display: flex;
            gap: 8px;
        }
        
        .dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.4);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .dot.active {
            background: rgba(255, 255, 255, 0.9);
            transform: scale(1.2);
        }
        
        .slide-counter {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            min-width: 50px;
            text-align: center;
        }
    </style>
</head>

<body class="bg-white font-sf">
    <!-- Slide 1: Hero -->
    <div class="slide active" id="slide1">
        <div class="h-screen flex items-center justify-center relative overflow-hidden">
            <div class="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-cyan-50"></div>
            
            <!-- Background Elements -->
            <div class="absolute top-20 left-20 w-64 h-64 bg-blue-200 rounded-full opacity-20 floating"></div>
            <div class="absolute bottom-20 right-20 w-48 h-48 bg-cyan-200 rounded-full opacity-20 floating" style="animation-delay: -2s;"></div>
            
            <div class="container mx-auto px-6 text-center relative z-10">
                <h1 class="hero-number gradient-text mb-6">数字种植</h1>
                <h2 class="text-4xl md:text-6xl font-bold text-gray-800 mb-4">管理系统</h2>
                <p class="text-lg md:text-xl text-gray-600 mb-2">Digital Farming Management System</p>
                <p class="text-base md:text-lg text-gray-500 max-w-3xl mx-auto leading-relaxed">
                    全链路赋能：上游酒厂、下游大户、供销体系，共建数字农业基础设施
                </p>
                
                <!-- Key Numbers -->
                <div class="grid grid-cols-3 gap-8 mt-16 max-w-2xl mx-auto">
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold gradient-text">3</div>
                        <div class="text-sm text-gray-500 mt-1">Core Stakeholders</div>
                        <div class="text-xs text-gray-400">核心参与方</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold gradient-text">100%</div>
                        <div class="text-sm text-gray-500 mt-1">Traceability</div>
                        <div class="text-xs text-gray-400">全程可追溯</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold gradient-text">∞</div>
                        <div class="text-sm text-gray-500 mt-1">Scalability</div>
                        <div class="text-xs text-gray-400">无限扩展</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 2: 上游酒厂 -->
    <div class="slide" id="slide2">
        <div class="h-screen flex items-center justify-center bg-gradient-to-br from-blue-500 to-blue-600 text-white p-8">
            <div class="container mx-auto max-w-5xl">
                <div class="text-center mb-12">
                    <i class="fas fa-industry text-6xl mb-6"></i>
                    <h2 class="text-5xl md:text-6xl font-bold mb-4">上游酒厂</h2>
                    <p class="text-xl text-blue-100">Upstream Distillery</p>
                </div>
                
                                <div class="grid md:grid-cols-2 gap-8">
                    <div class="bg-white/10 backdrop-blur-sm rounded-3xl p-8">
                        <div class="flex items-center mb-6">
                            <i class="fas fa-search-location text-3xl mr-4"></i>
                            <h3 class="text-2xl font-bold">全程可追溯性</h3>
                        </div>
                        <p class="text-blue-100 text-xl">通过数字田块管理，记录每块地的种植过程，包括种子来源、种植收获时间、施肥用药记录等，满足酒厂对原料溯源的需求</p>
                    </div>
                    
                    <div class="bg-white/10 backdrop-blur-sm rounded-3xl p-8">
                        <div class="flex items-center mb-6">
                            <i class="fas fa-certificate text-3xl mr-4"></i>
                            <h3 class="text-2xl font-bold">品质保证</h3>
                        </div>
                        <p class="text-blue-100 text-xl">系统记录种植过程中的关键节点数据，如灌溉、施肥、病虫害防治等，帮助酒厂评估原料品质</p>
                    </div>
                    
                    <div class="bg-white/10 backdrop-blur-sm rounded-3xl p-8">
                        <div class="flex items-center mb-6">
                            <i class="fas fa-chart-line text-3xl mr-4"></i>
                            <h3 class="text-2xl font-bold">供应稳定性</h3>
                        </div>
                        <p class="text-blue-100 text-xl">通过数字化管理，更好地预测产量，提前做好生产计划，降低供应风险</p>
                    </div>
                    
                    <div class="bg-white/10 backdrop-blur-sm rounded-3xl p-8">
                        <div class="flex items-center mb-6">
                            <i class="fas fa-chart-bar text-3xl mr-4"></i>
                            <h3 class="text-2xl font-bold">数据可视化</h3>
                        </div>
                        <p class="text-blue-100 text-xl">为酒厂提供直观的种植数据报表，增强对供应链的信任度</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 3: 下游种植大户（一） -->
    <div class="slide" id="slide3">
        <div class="h-screen flex items-center justify-center bg-gradient-to-br from-green-500 to-green-600 text-white p-8">
            <div class="container mx-auto max-w-4xl">
                <div class="text-center mb-12">
                    <i class="fas fa-seedling text-6xl mb-6"></i>
                    <h2 class="text-5xl md:text-6xl font-bold mb-4">下游种植大户</h2>
                    <p class="text-xl text-green-100">Large Farmers - Part 1</p>
                </div>
                
                                <div class="space-y-8">
                    <div class="bg-white/10 backdrop-blur-sm rounded-3xl p-8">
                        <h3 class="text-3xl font-bold mb-4 flex items-center">
                            <i class="fas fa-exclamation-circle text-4xl mr-4"></i>
                            种植记录痛点
                        </h3>
                        <p class="text-green-100 text-xl">农事操作记录分散，缺乏系统性和标准性，难以追溯历史，经验难以传承</p>
                    </div>
                    
                    <div class="bg-white/10 backdrop-blur-sm rounded-3xl p-8">
                        <h3 class="text-3xl font-bold mb-4 flex items-center">
                            <i class="fas fa-dollar-sign text-4xl mr-4"></i>
                            成本控制痛点
                        </h3>
                        <p class="text-green-100 text-xl">通过农资使用记录，监督和核算成本，帮助农户优化投入，降低生产成本</p>
                    </div>
                    
                    <div class="bg-white/10 backdrop-blur-sm rounded-3xl p-8">
                        <h3 class="text-3xl font-bold mb-4 flex items-center">
                            <i class="fas fa-tachometer-alt text-4xl mr-4"></i>
                            作业效率
                        </h3>
                        <p class="text-green-100 text-xl">农机作业管理功能可以优化农机调度，提高作业效率，选择更好的设备和更优的农机手</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 4: 下游种植大户（二） -->
    <div class="slide" id="slide4">
        <div class="h-screen flex items-center justify-center bg-gradient-to-br from-emerald-500 to-emerald-600 text-white p-8">
            <div class="container mx-auto max-w-5xl">
                <div class="text-center mb-12">
                    <i class="fas fa-leaf text-6xl mb-6"></i>
                    <h2 class="text-5xl md:text-6xl font-bold mb-4">更多价值</h2>
                    <p class="text-xl text-emerald-100">Extended Benefits</p>
                </div>
                
                                <div class="grid md:grid-cols-2 gap-6">
                    <div class="bg-white/10 backdrop-blur-sm rounded-3xl p-6">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-shield-alt text-2xl mr-3"></i>
                            <h3 class="text-xl font-bold">保险理赔便利</h3>
                        </div>
                        <p class="text-emerald-100 text-lg">有详细的自然灾害记录，便于保险公司理赔，减少纠纷</p>
                    </div>
                    
                    <div class="bg-white/10 backdrop-blur-sm rounded-3xl p-6">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-arrow-up text-2xl mr-3"></i>
                            <h3 class="text-xl font-bold">收益提升</h3>
                        </div>
                        <p class="text-emerald-100 text-lg">通过科学种植和成本控制，提高单位面积收入</p>
                    </div>
                    
                    <div class="bg-white/10 backdrop-blur-sm rounded-3xl p-6">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-handshake text-2xl mr-3"></i>
                            <h3 class="text-xl font-bold">信用建设</h3>
                        </div>
                        <p class="text-emerald-100 text-lg">数字化记录有助于建立种植大户的信用档案，便于银行融资</p>
                    </div>
                    
                    <div class="bg-white/10 backdrop-blur-sm rounded-3xl p-6">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-expand-arrows-alt text-2xl mr-3"></i>
                            <h3 class="text-xl font-bold">规模扩大</h3>
                        </div>
                        <p class="text-emerald-100 text-lg">标准化的种植流程有助于突破管理瓶颈，支持规模化扩张</p>
                    </div>
                    
                    <div class="bg-white/10 backdrop-blur-sm rounded-3xl p-6 md:col-span-2">
                        <div class="flex items-center mb-4 justify-center">
                            <i class="fas fa-award text-2xl mr-3"></i>
                            <h3 class="text-xl font-bold">数字化品牌形象</h3>
                        </div>
                        <p class="text-emerald-100 text-lg text-center">提升种植大户的数字化形象，增加政府扶持和项目申报的可能性</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

        <!-- Slide 5: 供销社价值（一） -->
    <div class="slide" id="slide5">
        <div class="h-screen flex items-center justify-center bg-gradient-to-br from-purple-500 to-purple-600 text-white p-8">
            <div class="container mx-auto max-w-5xl">
                <div class="text-center mb-12">
                    <i class="fas fa-building text-6xl mb-6"></i>
                    <h2 class="text-5xl md:text-6xl font-bold mb-4">供销社价值</h2>
                    <p class="text-xl text-purple-100">Supply & Marketing Cooperative - Part 1</p>
                </div>
                
                <div class="grid md:grid-cols-2 gap-8">
                    <div class="bg-white/10 backdrop-blur-sm rounded-3xl p-8">
                        <div class="flex items-center mb-6">
                            <i class="fas fa-link text-3xl mr-4"></i>
                            <h3 class="text-2xl font-bold">供应链管理</h3>
                        </div>
                        <p class="text-purple-100 text-xl">通过系统实时掌握种植情况，预判产量，提前做好收购和仓储准备</p>
                    </div>
                    
                    <div class="bg-white/10 backdrop-blur-sm rounded-3xl p-8">
                        <div class="flex items-center mb-6">
                            <i class="fas fa-star text-3xl mr-4"></i>
                            <h3 class="text-2xl font-bold">原料质量提升</h3>
                        </div>
                        <p class="text-purple-100 text-xl">基于数据分析，向种植户提供更精准的种植建议，提升原料品质</p>
                    </div>
                    
                    <div class="bg-white/10 backdrop-blur-sm rounded-3xl p-8">
                        <div class="flex items-center mb-6">
                            <i class="fas fa-users text-3xl mr-4"></i>
                            <h3 class="text-2xl font-bold">供应商评估</h3>
                        </div>
                        <p class="text-purple-100 text-xl">通过单产排名等数据筛选优质种植户，建立长期合作关系</p>
                    </div>
                    
                    <div class="bg-white/10 backdrop-blur-sm rounded-3xl p-8">
                        <div class="flex items-center mb-6">
                            <i class="fas fa-exclamation-triangle text-3xl mr-4"></i>
                            <h3 class="text-2xl font-bold">风险控制</h3>
                        </div>
                        <p class="text-purple-100 text-xl">通过气象、病虫害等记录，及时预警和响应，降低供应风险</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 6: 供销社价值（二） -->
    <div class="slide" id="slide6">
        <div class="h-screen flex items-center justify-center bg-gradient-to-br from-indigo-500 to-purple-600 text-white p-8">
            <div class="container mx-auto max-w-6xl">
                <div class="text-center mb-12">
                    <i class="fas fa-chart-line text-6xl mb-6"></i>
                    <h2 class="text-5xl md:text-6xl font-bold mb-4">更多价值</h2>
                    <p class="text-xl text-indigo-100">Extended Benefits</p>
                </div>
                
                <div class="grid md:grid-cols-3 gap-6">
                    <div class="bg-white/10 backdrop-blur-sm rounded-3xl p-6">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-rocket text-2xl mr-3"></i>
                            <h3 class="text-xl font-bold">品牌建设</h3>
                        </div>
                        <p class="text-indigo-100 text-lg">打造"智慧农业"服务形象，提升供销社在农业产业链中的地位</p>
                    </div>
                    
                    <div class="bg-white/10 backdrop-blur-sm rounded-3xl p-6">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-database text-2xl mr-3"></i>
                            <h3 class="text-xl font-bold">数据资产积累</h3>
                        </div>
                        <p class="text-indigo-100 text-lg">积累农机、种植户、田块等数据资产，为未来业务拓展奠定基础</p>
                    </div>
                    
                    <div class="bg-white/10 backdrop-blur-sm rounded-3xl p-6">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-tachometer-alt text-2xl mr-3"></i>
                            <h3 class="text-xl font-bold">减少人工管理成本</h3>
                        </div>
                        <p class="text-indigo-100 text-lg">数字化管理降低人工成本，提升服务响应速度和准确性</p>
                    </div>
                    
                    <div class="bg-white/10 backdrop-blur-sm rounded-3xl p-6">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-shopping-cart text-2xl mr-3"></i>
                            <h3 class="text-xl font-bold">促进种子销售</h3>
                        </div>
                        <p class="text-indigo-100 text-lg">通过系统解决种植大户缺乏管理软件的问题，增强客户粘性</p>
                    </div>
                    
                    <div class="bg-white/10 backdrop-blur-sm rounded-3xl p-6 md:col-start-2">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-trophy text-2xl mr-3"></i>
                            <h3 class="text-xl font-bold">提升市场话语权</h3>
                        </div>
                        <p class="text-indigo-100 text-lg">通过数字化服务，提升在与酒厂等下游企业合作中的话语权</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

        <!-- Slide 7: 实施建议 -->
    <div class="slide" id="slide7">
        <div class="h-screen flex items-center justify-center bg-gradient-to-br from-orange-500 to-red-500 text-white p-8">
            <div class="container mx-auto max-w-5xl">
                <div class="text-center mb-12">
                    <i class="fas fa-rocket text-6xl mb-6"></i>
                    <h2 class="text-5xl md:text-6xl font-bold mb-4">实施建议</h2>
                    <p class="text-xl text-orange-100">Implementation Roadmap</p>
                </div>
                
                <div class="grid md:grid-cols-2 gap-8">
                    <div class="bg-white/10 backdrop-blur-sm rounded-3xl p-8">
                        <div class="flex items-center mb-6">
                            <div class="flex-shrink-0 w-12 h-12 bg-white/20 rounded-full flex items-center justify-center text-xl font-bold mr-4">1</div>
                            <h3 class="text-2xl font-bold">分步推进</h3>
                        </div>
                        <p class="text-orange-100 text-lg mb-2">Step-by-step Implementation</p>
                        <p class="text-orange-200 text-xl">从核心功能开始，逐步扩展到更多功能模块</p>
                    </div>
                    
                    <div class="bg-white/10 backdrop-blur-sm rounded-3xl p-8">
                        <div class="flex items-center mb-6">
                            <div class="flex-shrink-0 w-12 h-12 bg-white/20 rounded-full flex items-center justify-center text-xl font-bold mr-4">2</div>
                            <h3 class="text-2xl font-bold">示范试点</h3>
                        </div>
                        <p class="text-orange-100 text-lg mb-2">Pilot Program</p>
                        <p class="text-orange-200 text-xl">选择1-2个种植大户进行试点，验证系统效果</p>
                    </div>
                    
                    <div class="bg-white/10 backdrop-blur-sm rounded-3xl p-8">
                        <div class="flex items-center mb-6">
                            <div class="flex-shrink-0 w-12 h-12 bg-white/20 rounded-full flex items-center justify-center text-xl font-bold mr-4">3</div>
                            <h3 class="text-2xl font-bold">数据积累</h3>
                        </div>
                        <p class="text-orange-100 text-lg mb-2">Data Accumulation</p>
                        <p class="text-orange-200 text-xl">通过试点收集真实数据，为系统优化提供依据</p>
                    </div>
                    
                    <div class="bg-white/10 backdrop-blur-sm rounded-3xl p-8">
                        <div class="flex items-center mb-6">
                            <div class="flex-shrink-0 w-12 h-12 bg-white/20 rounded-full flex items-center justify-center text-xl font-bold mr-4">4</div>
                            <h3 class="text-2xl font-bold">持续优化</h3>
                        </div>
                        <p class="text-orange-100 text-lg mb-2">Continuous Optimization</p>
                        <p class="text-orange-200 text-xl">根据用户反馈和数据分析，持续完善系统功能</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide 8: 结尾 -->
    <div class="slide" id="slide8">
        <div class="h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 to-black text-white">
            <div class="container mx-auto px-6 text-center">
                <h2 class="text-6xl md:text-8xl font-bold mb-6 gradient-text">谢谢观看</h2>
                <p class="text-2xl text-gray-300 mb-12">Thank you for watching</p>
                <div class="flex items-center justify-center space-x-6 text-gray-400">
                    <i class="fas fa-seedling text-4xl"></i>
                    <span class="text-2xl">数字农业，正在路上</span>
                    <i class="fas fa-rocket text-4xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Controls -->
    <div class="nav-controls">
        <button class="nav-btn" id="prevBtn" onclick="previousSlide()">
            <i class="fas fa-chevron-left"></i>
        </button>
        
        <div class="slide-counter">
            <span id="currentSlide">1</span> / <span id="totalSlides">8</span>
        </div>
        
        <div class="slide-indicator" id="slideIndicator"></div>
        
        <button class="nav-btn" id="nextBtn" onclick="nextSlide()">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <script>
        let currentSlideIndex = 0;
        const totalSlides = 8;
        
                 // Initialize
         document.addEventListener('DOMContentLoaded', function() {
             // Hide all slides except first
             for (let i = 2; i <= totalSlides; i++) {
                 document.getElementById(`slide${i}`).style.display = 'none';
             }
             
             createSlideIndicators();
             updateSlideCounter();
             updateNavButtons();
         });
        
        // Create slide indicators
        function createSlideIndicators() {
            const indicator = document.getElementById('slideIndicator');
            for (let i = 0; i < totalSlides; i++) {
                const dot = document.createElement('div');
                dot.className = 'dot';
                if (i === 0) dot.classList.add('active');
                dot.onclick = () => goToSlide(i);
                indicator.appendChild(dot);
            }
        }
        
                 // Go to specific slide
         function goToSlide(index) {
             if (index < 0 || index >= totalSlides) return;
             
             // Hide all slides first
             for (let i = 1; i <= totalSlides; i++) {
                 const slide = document.getElementById(`slide${i}`);
                 slide.classList.remove('active');
                 slide.style.display = 'none';
             }
             
             // Remove active class from all dots
             document.querySelectorAll('.dot').forEach(dot => dot.classList.remove('active'));
             
             // Update index
             currentSlideIndex = index;
             
             // Show and activate new slide
             const newSlide = document.getElementById(`slide${currentSlideIndex + 1}`);
             newSlide.style.display = 'block';
             setTimeout(() => {
                 newSlide.classList.add('active');
             }, 50);
             
             // Activate corresponding dot
             document.querySelectorAll('.dot')[currentSlideIndex].classList.add('active');
             
             updateSlideCounter();
             updateNavButtons();
         }
        
        // Next slide
        function nextSlide() {
            if (currentSlideIndex < totalSlides - 1) {
                goToSlide(currentSlideIndex + 1);
            }
        }
        
        // Previous slide
        function previousSlide() {
            if (currentSlideIndex > 0) {
                goToSlide(currentSlideIndex - 1);
            }
        }
        
        // Update slide counter
        function updateSlideCounter() {
            document.getElementById('currentSlide').textContent = currentSlideIndex + 1;
        }
        
        // Update navigation buttons
        function updateNavButtons() {
            document.getElementById('prevBtn').disabled = currentSlideIndex === 0;
            document.getElementById('nextBtn').disabled = currentSlideIndex === totalSlides - 1;
        }
        
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                e.preventDefault();
                nextSlide();
            } else if (e.key === 'ArrowLeft') {
                e.preventDefault();
                previousSlide();
            } else if (e.key === 'Home') {
                e.preventDefault();
                goToSlide(0);
            } else if (e.key === 'End') {
                e.preventDefault();
                goToSlide(totalSlides - 1);
            }
        });
        
        // Touch/Swipe support for mobile
        let startX = 0;
        let endX = 0;
        
        document.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
        });
        
        document.addEventListener('touchend', function(e) {
            endX = e.changedTouches[0].clientX;
            handleSwipe();
        });
        
        function handleSwipe() {
            const threshold = 50;
            const diff = startX - endX;
            
            if (Math.abs(diff) > threshold) {
                if (diff > 0) {
                    nextSlide(); // Swipe left - next slide
                } else {
                    previousSlide(); // Swipe right - previous slide
                }
            }
        }
    </script>
</body>
</html>
