graph TB
    %% 核心问题
    Problem["❌ 核心问题<br/>涉农数据失真导致：<br/>• 政策偏离实际<br/>• 资金使用低效<br/>• 政策公信力下降"]
    
    %% 数据采集层
    subgraph DataLayer["🔍 数据采集层"]
        DL["🗺️ 数字地块<br/>• 基础属性（坐标、面积、权属、土壤）<br/>• 动态属性（作物、生长、病虫害）<br/>• 多源数据（遥感、传感器、人工）"]
        DM["🚜 数字农机<br/>• 资产属性（型号、归属、补贴）<br/>• 作业数据（位置、轨迹、效率）<br/>• 车载终端（北斗、传感器）"]
    end

    %% 数据融合层
    GIS["🗾 GIS一张图平台<br/>• 地块+农机数据叠加<br/>• 空间关联分析<br/>• 时间关联分析<br/>• 可视化数据看板"]

    %% 监控分析层
    subgraph Analysis["📊 监控分析层"]
        MM["🚜 农机作业监控<br/>• 闲置率计算<br/>• 资源错配识别<br/>• 供需失衡预警"]
        FM["🌱 地块生长监控<br/>• 墒情病害监测<br/>• 生长周期偏差<br/>• 风险预警机制"]
    end

    %% 决策支持层
    Decision["🎯 政策资金动态调整<br/>• 农机补贴政策调整<br/>• 应急资金精准投放<br/>• 资源跨区调配<br/>• 政策效果评估"]

    %% 三级管理体系
    subgraph Management["🏛️ 三级监控指挥体系"]
        PC["省级：宏观监控+战略指导"]
        MC["市级：区域协调+资源联动"]
        CC["县级：精细监控+即时响应"]
    end

    %% 最终目标
    Solution["✅ 系统价值<br/>• 提高资金使用效率30%+<br/>• 增强政策适应性<br/>• 推动农业现代化<br/>• 保障农业生产稳定"]

    %% 主要数据流向
    Problem ==> DataLayer
    DL --> GIS
    DM --> GIS
    GIS --> MM
    GIS --> FM
    MM --> Decision
    FM --> Decision
    Decision --> PC
    Decision --> MC
    Decision --> CC
    Management ==> Solution

    %% 反馈闭环
    CC -.->|实施反馈| Decision
    MC -.->|区域反馈| Decision
    PC -.->|宏观反馈| Decision
    Decision -.->|政策调整| DataLayer