:root {
    --primary-color: rgb(212,67,60);
    --primary-gradient: linear-gradient(135deg, rgb(212,67,60) 0%, rgb(232,87,80) 100%);
    --text-primary: #1d1d1f;
    --text-secondary: #86868b;
    --text-light: #FFFFFF;
    --background-light: #FFFFFF;
    --background-dark: #f5f5f7;
    --card-shadow: 0 20px 40px rgba(0, 0, 0, 0.08);
    --card-hover-shadow: 0 30px 60px rgba(0, 0, 0, 0.12);
    --subtle-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    color: var(--text-primary);
    background-color: var(--background-light);
    line-height: 1.4;
    -webkit-font-smoothing: antialiased;
}

.container {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

/* Hero Section */
.hero {
    height: 35vh;
    min-height: 280px;
    max-height: 320px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: var(--primary-gradient);
    color: var(--text-light);
    position: relative;
    overflow: hidden;
}

.hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
    pointer-events: none;
}

.hero-title {
    font-size: 4.2rem;
    font-weight: 700;
    margin-bottom: 0.8rem;
    letter-spacing: -0.03em;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hero-subtitle {
    font-size: 1.4rem;
    font-weight: 400;
    max-width: 600px;
    margin: 0 auto;
    opacity: 0.95;
    letter-spacing: 0.02em;
}

/* Section Styles */
.section {
    padding: 60px 0;
    position: relative;
}

.section:nth-child(even) {
    background-color: var(--background-dark);
}

.section-title {
    font-size: 2.8rem;
    text-align: center;
    margin-bottom: 40px;
    font-weight: 700;
    letter-spacing: -0.03em;
    color: var(--text-primary);
}

.section-description {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 50px;
    font-size: 1.3rem;
    color: var(--text-secondary);
    line-height: 1.6;
    letter-spacing: 0.01em;
}

/* Bento Grid Layout */
.bento-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
    margin: 20px 0;
}

/* Cards Grid */
.cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
}

.cards-grid.two-columns {
    grid-template-columns: repeat(4, 1fr);
}

.card {
    background-color: var(--background-light);
    border-radius: 20px;
    padding: 32px;
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.04);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-hover-shadow);
}

.card-icon {
    font-size: 2.8rem;
    margin-bottom: 20px;
    display: inline-block;
    position: relative;
}

.card-icon::after {
    content: '';
    position: absolute;
    width: 40px;
    height: 40px;
    background: var(--primary-gradient);
    border-radius: 50%;
    opacity: 0.1;
    z-index: -1;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.card-title {
    font-size: 1.5rem;
    margin-bottom: 16px;
    font-weight: 600;
    letter-spacing: -0.02em;
    color: var(--text-primary);
}

.card-description {
    color: var(--text-secondary);
    font-size: 1.1rem;
    line-height: 1.6;
    letter-spacing: 0.01em;
}

/* Statistics Section */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 24px;
}

.stat-card {
    text-align: center;
    padding: 32px 24px;
    background-color: var(--background-light);
    border-radius: 20px;
    box-shadow: var(--card-shadow);
    transition: transform 0.3s ease;
    border: 1px solid rgba(0,0,0,0.04);
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 2.4rem;
    margin-bottom: 16px;
    position: relative;
    display: inline-block;
}

.stat-icon::after {
    content: '';
    position: absolute;
    width: 48px;
    height: 48px;
    background: var(--primary-gradient);
    border-radius: 50%;
    opacity: 0.1;
    z-index: -1;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.stat-value {
    font-size: 2.4rem;
    font-weight: 700;
    margin-bottom: 8px;
    letter-spacing: -0.02em;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 1rem;
    font-weight: 500;
    letter-spacing: 0.02em;
}

/* Timeline */
.timeline {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
    max-width: 1200px;
    margin: 40px auto 0;
}

.timeline-item {
    background-color: var(--background-light);
    border-radius: 20px;
    padding: 32px;
    box-shadow: var(--card-shadow);
    transition: transform 0.3s ease;
    border: 1px solid rgba(0,0,0,0.04);
}

.timeline-item:hover {
    transform: translateY(-5px);
}

.timeline-number {
    font-size: 2.4rem;
    font-weight: 700;
    margin-bottom: 20px;
    letter-spacing: -0.02em;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.timeline-title {
    font-size: 1.5rem;
    margin-bottom: 20px;
    font-weight: 600;
    letter-spacing: -0.02em;
    color: var(--text-primary);
}

.timeline-list {
    list-style-type: none;
}

.timeline-list li {
    margin-bottom: 16px;
    padding-left: 24px;
    position: relative;
    font-size: 1.1rem;
    color: var(--text-secondary);
    line-height: 1.6;
}

.timeline-list li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 12px;
    width: 6px;
    height: 6px;
    background: var(--primary-gradient);
    border-radius: 50%;
}

.timeline-list li strong {
    color: var(--text-primary);
    font-weight: 500;
}

/* Conclusion Section */
.conclusion-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
    font-size: 1.2rem;
    line-height: 1.4;
    color: var(--text-secondary);
    padding: 30px 0;
}

.conclusion-content p {
    margin-bottom: 16px;
}

.conclusion-content p:last-child {
    margin-bottom: 0;
    font-size: 1.4rem;
    color: var(--text-primary);
    font-weight: 500;
}

/* Footer */
.footer {
    background: var(--primary-gradient);
    color: var(--text-light);
    padding: 40px 0;
    text-align: center;
}

.footer-content p {
    font-size: 1rem;
    opacity: 0.95;
    letter-spacing: 0.02em;
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .container {
        padding: 0 30px;
    }
    
    .bento-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .timeline {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .cards-grid.two-columns {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 992px) {
    .container {
        padding: 0 24px;
    }
    
    .hero-title {
        font-size: 3.6rem;
    }
    
    .bento-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .cards-grid.two-columns {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .hero {
        height: 40vh;
    }
    
    .hero-title {
        font-size: 3rem;
    }
    
    .hero-subtitle {
        font-size: 1.2rem;
    }
    
    .section-title {
        font-size: 2.4rem;
    }
    
    .timeline {
        grid-template-columns: 1fr;
    }
    
    .card, .stat-card, .timeline-item {
        padding: 24px;
    }
    
    .cards-grid.two-columns {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    .container {
        padding: 0 20px;
    }
    
    .hero-title {
        font-size: 2.6rem;
    }
    
    .bento-grid {
        grid-template-columns: 1fr;
    }
    
    .section-title {
        font-size: 2rem;
    }
} 