<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>农机监控器快速指南</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            line-height: 1.5;
            color: #333;
            background: #f5f5f5;
            min-height: 100vh;
            padding: 5px;
        }
        
        .container {
            max-width: 210mm;
            margin: 0 auto;
            background: white;
            border-radius: 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: white;
            color: #333;
            padding: 3mm;
            text-align: center;
            border-bottom: 3px solid #4CAF50;
        }
        
        .header h1 {
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 0;
        }
        
        .content {
            padding: 3mm;
        }
        
        /* 主要布局：上下分层 */
        .main-layout {
            display: grid;
            grid-template-rows: auto 1fr;
            gap: 3mm;
            height: 285mm;
        }
        
        /* 上层：硬件安装区域 - 扩大边界 */
        .hardware-section {
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 4mm;
            background: #fafafa;
            display: grid;
            grid-template-columns: auto 1fr auto;
            grid-template-rows: 1fr auto;
            gap: 3mm;
            position: relative;
            height: 100mm;
        }
        
        .hardware-section::before {
            content: "1";
            position: absolute;
            top: 4px;
            left: 4px;
            width: 28px;
            height: 28px;
            background: #4CAF50;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }
        
        .hardware-title-vertical {
            writing-mode: vertical-rl;
            text-orientation: upright;
            font-size: 1.4em;
            color: #2c3e50;
            font-weight: bold;
            letter-spacing: 2px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 25mm;
        }
        
        .hardware-image-container {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .hardware-image {
            width: 100%;
            max-width: 110mm;
            height: auto;
            border-radius: 6px;
            border: 1px solid #ddd;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .hardware-tips {
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 6px;
            padding: 0;
            background: transparent;
            border: none;
            min-width: 40mm;
            grid-column: 3;
            grid-row: 1;
        }
        
        .tip-header {
            font-size: 1.1em;
            font-weight: bold;
            color: #d32f2f;
            text-align: center;
            margin-bottom: 8px;
        }
        
        .tip-main {
            font-size: 0.9em;
            color: #2c3e50;
            font-weight: bold;
            text-align: center;
            margin-bottom: 6px;
        }
        
        .tip-warning {
            font-size: 0.9em;
            color: #555;
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .tip-item {
            display: flex;
            align-items: center;
            gap: 4px;
            margin-left: 8px;
        }
        
        .tip-icon {
            font-size: 10px;
        }
        
        .tip-text {
            font-size: 0.85em;
            color: #555;
            font-weight: bold;
            line-height: 1.2;
        }
        
        .tip-footer {
            font-size: 0.85em;
            color: #4CAF50;
            font-weight: bold;
            text-align: center;
            margin-top: 6px;
        }
        
        /* 下层：软件配置区域 - 减少高度为底部提示留空间 */
        .software-section {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 3mm;
            height: 145mm;
        }
        
        .software-item {
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 4mm;
            background: #fafafa;
            display: grid;
            grid-template-rows: auto auto auto 1fr;
            gap: 4mm;
            position: relative;
        }
        
        .software-item::before {
            content: attr(data-step);
            position: absolute;
            top: 4px;
            left: 4px;
            width: 28px;
            height: 28px;
            background: #4CAF50;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }
        
        .software-title {
            font-size: 1.4em;
            color: #2c3e50;
            font-weight: bold;
            margin-top: 15px;
            text-align: center;
            line-height: 1.2;
        }
        
        .software-description {
            font-size: 1em;
            color: #666;
            line-height: 1.3;
            text-align: center;
        }
        
        .software-description p {
            margin-bottom: 4px;
        }
        
        .software-image-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            min-height: 60mm;
        }
        
        .software-image {
            width: 100%;
            max-width: 50mm;
            height: auto;
            border-radius: 4px;
            border: 1px solid #ddd;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            object-fit: contain;
        }
        
        .app-path {
            background: #e3f2fd;
            padding: 3px 6px;
            border-radius: 3px;
            color: #1976d2;
            font-weight: bold;
            font-size: 1em;
            display: inline-block;
            margin: 1px;
        }
        
        .success-message {
            background: #e8f5e8;
            padding: 6px;
            border-radius: 4px;
            color: #2e7d32;
            font-weight: bold;
            margin-top: 6px;
            border: 2px solid #4CAF50;
            font-size: 1em;
            text-align: center;
            line-height: 1.2;
        }
        
        /* 底部配置完成提示 */
        .completion-notice {
            margin-top: 1mm;
            padding: 0 4mm;
            display: flex;
            justify-content: flex-start;
        }
        
        .completion-message {
            padding: 4px 0;
            color: #333;
            font-weight: normal;
            font-size: 0.9em;
            text-align: left;
            line-height: 1.3;
            width: 100%;
            background: transparent;
            border: none;
            box-shadow: none;
        }
        
        .usage-item {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 2px;
        }
        
        .usage-icon {
            font-size: 1em;
            min-width: 20px;
            text-align: center;
        }
        
        .usage-text {
            flex: 1;
            color: #333;
            font-size: 0.9em;
            line-height: 1.3;
        }
        
        /* 打印样式 */
        @media print {
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }
            
            body {
                background: white !important;
                padding: 0;
                font-size: 16pt;
                line-height: 1.4;
            }
            
            .container {
                max-width: none;
                width: 100%;
                box-shadow: none;
                border-radius: 0;
                margin: 0;
                background: white !important;
            }
            
            .header {
                background: white !important;
                color: #333 !important;
                padding: 2mm;
                border-bottom: 2pt solid #4CAF50;
            }
            
            .header h1 {
                font-size: 20pt;
                font-weight: bold;
                color: #2c3e50 !important;
            }
            
            .content {
                padding: 2mm;
            }
            
            .main-layout {
                height: 285mm;
                gap: 2mm;
            }
            
            .hardware-section {
                background: #fafafa !important;
                border: 1.5pt solid #e0e0e0;
                padding: 3mm;
                height: 95mm;
                display: grid !important;
                grid-template-columns: auto 1fr auto !important;
                grid-template-rows: 1fr auto !important;
                gap: 3mm !important;
            }
            
            .hardware-section::before {
                background: #4CAF50 !important;
                color: white !important;
                font-size: 12pt;
            }
            
            .hardware-title-vertical {
                writing-mode: vertical-rl !important;
                text-orientation: upright !important;
                font-size: 13pt;
                color: #2c3e50 !important;
                font-weight: bold;
                letter-spacing: 1pt;
                min-width: 20mm !important;
            }
            
            .hardware-image-container {
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }
            
            .hardware-image {
                max-width: 85mm;
                border: 1pt solid #ddd;
            }
            
            .hardware-tips {
                display: flex !important;
                flex-direction: column !important;
                justify-content: center !important;
                gap: 4px !important;
                padding: 0 !important;
                background: transparent !important;
                border: none !important;
                min-width: 35mm !important;
                grid-column: 3 !important;
                grid-row: 1 !important;
            }
            
            .tip-header {
                font-size: 10pt !important;
                font-weight: bold !important;
                color: #d32f2f !important;
                text-align: center !important;
                margin-bottom: 5px !important;
            }
            
            .tip-main {
                font-size: 9pt !important;
                color: #2c3e50 !important;
                font-weight: bold !important;
                text-align: center !important;
                margin-bottom: 4px !important;
            }
            
            .tip-warning {
                font-size: 9pt !important;
                color: #555 !important;
                font-weight: bold !important;
                margin-bottom: 3px !important;
            }
            
            .tip-text {
                font-size: 8pt !important;
                color: #555 !important;
                font-weight: bold !important;
                line-height: 1.2 !important;
            }
            
            .tip-footer {
                font-size: 8pt !important;
                color: #4CAF50 !important;
                font-weight: bold !important;
                text-align: center !important;
                margin-top: 4px !important;
            }
            
            .software-section {
                height: 145mm;
                gap: 2mm;
            }
            
            .software-item {
                background: #fafafa !important;
                border: 1.5pt solid #e0e0e0;
                padding: 3mm;
                display: grid !important;
                grid-template-rows: auto auto auto 1fr !important;
                gap: 3mm !important;
            }
            
            .software-item::before {
                background: #4CAF50 !important;
                color: white !important;
                font-size: 12pt;
            }
            
            .software-title {
                font-size: 13pt;
                color: #2c3e50 !important;
            }
            
            .software-image-container {
                display: flex !important;
                flex-direction: column !important;
                align-items: center !important;
                justify-content: flex-start !important;
                min-height: 70mm !important;
            }
            
            .software-image {
                max-width: 45mm;
                border: 1pt solid #ddd;
            }
            
            .software-description {
                font-size: 10pt;
                color: #333 !important;
            }
            
            .app-path {
                background: #e3f2fd !important;
                color: #1976d2 !important;
                font-size: 10pt;
            }
            
            .success-message {
                background: #e8f5e8 !important;
                color: #2e7d32 !important;
                border: 1.5pt solid #4CAF50;
                font-size: 10pt;
            }
            
            .completion-notice {
                margin-top: 1mm !important;
                padding: 0 3mm !important;
            }
            
            .completion-message {
                color: #333 !important;
                font-size: 10pt;
                font-weight: normal !important;
                padding: 3px 0 !important;
                width: 100% !important;
                line-height: 1.2 !important;
                text-align: left !important;
                background: transparent !important;
                border: none !important;
                box-shadow: none !important;
            }
            
            .usage-item {
                display: flex !important;
                align-items: center !important;
                gap: 6px !important;
                margin-bottom: 2px !important;
            }
            
            .usage-item:last-child {
                margin-bottom: 0 !important;
            }
            
            .usage-icon {
                font-size: 9pt !important;
                min-width: 18px !important;
                text-align: center !important;
            }
            
            .usage-text {
                flex: 1 !important;
                color: #333 !important;
                font-size: 9pt !important;
                line-height: 1.2 !important;
            }
            
            @page {
                margin: 5mm;
                size: A4;
            }
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-layout {
                grid-template-rows: auto auto;
                height: auto;
            }
            
            .hardware-section {
                grid-template-columns: 1fr;
                text-align: center;
                height: auto;
            }
            
            .software-section {
                grid-template-columns: 1fr;
                height: auto;
            }
            
            .container {
                max-width: 95%;
                margin: 5px auto;
            }
            
            .content {
                padding: 5mm;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>农机监控器快速指南</h1>
        </div>
        
        <div class="content">
            <div class="main-layout">
                <!-- 上层：硬件安装区域 -->
                <div class="hardware-section">
                    <div class="hardware-title-vertical">硬件安装与连接</div>
                    <div class="hardware-image-container">
                        <img src="monitor images/anzhuang.jpg" alt="监控器安装指南" class="hardware-image">
                    </div>
                    <div class="hardware-tips">
                        <div class="tip-header">⚠️ 安全提醒 ⚠️</div>
                        <div class="tip-main">请务必固定好监控器与线缆</div>
                        <div class="tip-warning">🔒 避免因松动导致：</div>
                        <div class="tip-item">
                            <div class="tip-icon">🔌</div>
                            <div class="tip-text">线缆破损</div>
                        </div>
                        <div class="tip-item">
                            <div class="tip-icon">⚡</div>
                            <div class="tip-text">漏电隐患</div>
                        </div>
                        <div class="tip-item">
                            <div class="tip-icon">🔥</div>
                            <div class="tip-text">其他安全风险</div>
                        </div>
                        <div class="tip-footer">✅ 固定牢靠，安全无忧！</div>
                    </div>
                </div>
                
                <!-- 下层：软件配置区域 -->
                <div class="software-section">
                    <!-- 第二步：管理菜单 -->
                    <div class="software-item" data-step="2">
                        <h4 class="software-title">进入管理菜单</h4>
                        <div class="software-description">
                            <p>打开 <span class="app-path">测亩易 APP</span></p>
                            <p>路径：</p>
                            <p><span class="app-path">首页 → 作业队管理 → 管理</span></p>
                        </div>
                        <div class="software-image-container">
                            <img src="monitor images/caidan.jpg" alt="管理菜单" class="software-image">
                        </div>
                    </div>
                    
                    <!-- 第三步：添加终端 -->
                    <div class="software-item" data-step="3">
                        <h4 class="software-title">添加终端设备</h4>
                        <div class="software-description">
                            <p>进入<span class="app-path">终端</span>页面</p>
                            <p>点击<span class="app-path">+</span>按钮</p>
                            <p>输入监控器编号并命名</p>
                        </div>
                        <div class="software-image-container">
                            <img src="monitor images/jiankongqi.jpg" alt="监控器录入页面" class="software-image">
                        </div>
                    </div>
                    
                    <!-- 第四步：绑定农机 -->
                    <div class="software-item" data-step="4">
                        <h4 class="software-title">绑定农机设备</h4>
                        <div class="software-description">
                            <p>从<span class="app-path">管理</span>进入<span class="app-path">农机</span>页面</p>
                            <p>在新建或编辑农机页面</p>
                            <p>从<span class="app-path">终端</span>中选择录入的终端</p>
                        </div>
                        <div class="software-image-container">
                            <img src="monitor images/nongji.jpg" alt="农机绑定页面" class="software-image">
                        </div>
                    </div>
                </div>
                
                <!-- 配置完成提示 -->
                <div class="completion-notice">
                    <div class="completion-message">
                        <div class="usage-item">
                            <span class="usage-icon">📶</span>
                            <span class="usage-text">安装后等待一段时间，可在农机列表中查看该农机的在线状态</span>
                        </div>
                        <div class="usage-item">
                            <span class="usage-icon">🤖</span>
                            <span class="usage-text">农机作业数据自动采集、自动计算，无需任何操作</span>
                        </div>
                        <div class="usage-item">
                            <span class="usage-icon">🔄</span>
                            <span class="usage-text">农机状态每1小时自动刷新</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

