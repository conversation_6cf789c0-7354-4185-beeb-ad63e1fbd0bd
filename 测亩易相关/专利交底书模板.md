专利申请技术交底书


1、发明创造名称：
		 一种基于BLE技术的体育数据智能采集装置

2、所属领域：
体能训练、体质数据测试、体育考试和体能考试中的智能硬件数据传输和采集，本发明涉及到低功耗蓝牙（BLE），蓝牙5.0及数据穿出，广播等方法。

3、现有技术：
现有的体能训练、测试考核过程中的数据大部分是通过笔+纸介质记录，然后手动录入到信息化系统，此过程效率较低，而且数据记录需要大量的人工记录以及后期核对校验。数据容易出错，丢失或损坏，对数据持久化和安全性也有很大的挑战。

4、发明目的：简化体能训练过程中的数据采集，汇总流程，提高数据传输过中的效率以及安全性。

5、客观地指出现有技术中存在的问题，阐明本发明创造所要解决的问题，从而归纳出本发明创造的发明目的。
现有的数据采集常常通过纸介质记录的方式，或者传统硬件测量+人眼观察或者人工判读的方式来记录和获取受测者的数据信息。这样的方式不尽效率地下，对数据的保护不够眼睛，更依赖于环境因素以及硬件数据等影响，人为因素也会较大的影响数据的录入以及数据的准确性。
本发明所有硬件均为自研，所有数据都加密通过私钥传输，数据通过BLE低功耗的数据收集方式，可以极大程度的提高数据采集效率以及准确性，排除环境、认为等因素影响，数据直接入库持计划，相抵数据丢失的风险和数据传输的安全性。
本发明采用蓝牙广播包和蓝牙响应包的方式，对数据加密后，通过移动基站数据采集以及串口通信到上层的结构，解决了蓝牙单个连接的瓶颈，将同一时间的硬件数据采集个数提升到600+，极大程度的解决了多人训练、测试的数据传输瓶颈和场景，大大提升了数据采集效率。

6、发明内容：
	  本发明相比较传统蓝牙数据采集模型，通过中间基站模块的方式，屏蔽了上层硬件（体育教学使用的Android PAD，Android大瓶硬件等）蓝牙性能和层次不齐的缺点，对齐上层应用和多种、多个智能硬件的对接差异。
		 便携式的数据基站通过USB、MicroUSB、Type-C供电，用智能硬件串口的方式传输数据，硬件本身体积小巧容易携带，可以插在多种Android智能硬件想，还可以通过USB转接Hub，适配了USB、MicroUSB、Type-c等多种硬件接口的差异，极大的降低了使用过程的成本。

8、工作原理：
将移动数据采集基站插在Android智能硬件上，移动数据基站会通过usb、typec、microUsb供电启动，移动基站通过私有协议采集多种智能硬件的数据，将数据通过串口传递到上层智能硬件，智能硬件上的应用通过对数据的解密+解析，得到受测者的测试数据。这些测试数据自动存储汇总到数据库，供下一步业务场景使用。
安装性和操作性，便携式小巧型移动基站安装步骤简单。操作性也非常简单便捷，只需通过拔插就可以实现，通过多层数据加密和寄存器级别的安全开关，在使用过程中对数据的安全性和硬件的安全性有极强的保护，大大减低了传统模式下考、教人员的工作强度和数据安全隐患。

9、发明效果：
		工作效率相比传统模式得到了数百倍的提升，数据安全相比传统纸介质的模式得到了极大的提升。传统模式考、教人员观察、测量测试结果、记录测试结果数据。本发明通过解决单个智能硬件连接的方式，提供数据传输效率，解决数据传输瓶颈。切实将数据传输效率提升百倍以上。

10、附件
附图及附图的简单说明。 







附件一：


