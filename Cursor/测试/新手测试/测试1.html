<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测亩易APP - 一站式数字农事解决方案</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            line-height: 1.4;
            color: #333;
            background-color: #f4f4f9;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(135deg, #2E7D32 0%, #81C784 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
        }

        .header p {
            font-size: 1.1em;
            max-width: 800px;
            margin: 0 auto;
        }

        .stats {
            background: #fff;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin: -10px 20px 10px;
            border-radius: 15px;
        }

        .stats-container {
            display: flex;
            justify-content: center;
            gap: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .stat-item {
            flex: 1;
            padding: 15px;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-bottom: 3px solid #4CAF50;
        }

        .stat-item i {
            font-size: 1.5em;
            color: #2E7D32;
        }

        .stat-item h2 {
            font-size: 1.8em;
            color: #2E7D32;
            margin: 5px 0;
        }

        .features {
            padding: 10px 10px;
            max-width: 1380px;
            margin: 0 auto;
            flex: 1;
        }

        .section-title {
            text-align: center;
            margin-bottom: 20px;
            font-size: 1.8em;
            color: #2E7D32;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 14px;
        }

        .feature-item {
            padding: 14px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .feature-item:nth-child(1) { border-top: 3px solid #FF5722; }
        .feature-item:nth-child(2) { border-top: 3px solid #2196F3; }
        .feature-item:nth-child(3) { border-top: 3px solid #9C27B0; }
        .feature-item:nth-child(4) { border-top: 3px solid #FF9800; }
        .feature-item:nth-child(5) { border-top: 3px solid #E91E63; }
        .feature-item:nth-child(6) { border-top: 3px solid #00BCD4; }
        .feature-item:nth-child(7) { border-top: 3px solid #607D8B; }
        .feature-item:nth-child(8) { border-top: 3px solid #795548; }
        .feature-item:nth-child(9) { border-top: 3px solid #009688; }
        .feature-item:nth-child(10) { border-top: 3px solid #673AB7; }

        .feature-item i {
            font-size: 1.5em;
            margin-bottom: 10px;
        }

        .feature-item:nth-child(1) i { color: #FF5722; }
        .feature-item:nth-child(2) i { color: #2196F3; }
        .feature-item:nth-child(3) i { color: #9C27B0; }
        .feature-item:nth-child(4) i { color: #FF9800; }
        .feature-item:nth-child(5) i { color: #E91E63; }
        .feature-item:nth-child(6) i { color: #00BCD4; }
        .feature-item:nth-child(7) i { color: #607D8B; }
        .feature-item:nth-child(8) i { color: #795548; }
        .feature-item:nth-child(9) i { color: #009688; }
        .feature-item:nth-child(10) i { color: #673AB7; }

        .feature-item h3 {
            font-size: 1.15em;
            margin-bottom: 6px;
        }

        .feature-item p {
            font-size: 0.95em;
            line-height: 1.3;
        }

        .cta {
            background: linear-gradient(135deg, #2E7D32 0%, #81C784 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .cta h2 {
            font-size: 1.5em;
            margin-bottom: 10px;
        }

        .cta p {
            font-size: 1em;
            margin-bottom: 15px;
        }

        .cta-button {
            display: inline-block;
            padding: 10px 25px;
            background: #FF5722;
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .cta-button:hover {
            transform: translateY(-2px);
            background: #F4511E;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        @media (max-width: 768px) {
            .feature-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        .feature-item:hover {
            transform: translateY(-5px);
        }

        /* 修改标题区域的样式 */
        .header-content {
            display: flex;
            align-items: center;
            justify-content: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .header-logo {
            height: 50px;  /* 固定高度 */
            width: auto;
            margin-right: 20px;
        }

        .header-text {
            text-align: left;  /* 文字左对齐 */
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="header-logo">🌾</div>
            <div class="header-text">
                <h1>测亩易APP</h1>
                <p>一站式数字农事解决方案，让农业生产更智能、更高效</p>
            </div>
        </div>
    </header>

    <section class="stats">
        <div class="stats-container">
            <div class="stat-item">
                <i class="fas fa-users"></i>
                <h2>900万+</h2>
                <p>活跃用户</p>
            </div>
            <div class="stat-item">
                <i class="fas fa-download"></i>
                <h2>1亿+</h2>
                <p>下载次数</p>
            </div>
            <div class="stat-item">
                <i class="fas fa-chart-line"></i>
                <h2>6-10万</h2>
                <p>日活跃用户</p>
            </div>
        </div>
    </section>

    <section class="features">
        <h2 class="section-title">核心功能</h2>
        <div class="feature-grid">
            <div class="feature-item">
                <i class="fas fa-map-marked-alt"></i>
                <h3>多种测亩方式</h3>
                <p>画地块、走一圈、智能画地等多种测量方式</p>
            </div>
            <div class="feature-item">
                <i class="fas fa-layer-group"></i>
                <h3>地块管理</h3>
                <p>自动保存地块，支持分组管理与地图查看</p>
            </div>
            <div class="feature-item">
                <i class="fas fa-bullhorn"></i>
                <h3>农事信息发布</h3>
                <p>发布农活信息，打造农业信息交流平台</p>
            </div>
            <div class="feature-item">
                <i class="fas fa-tasks"></i>
                <h3>作业队管理</h3>
                <p>管理人员、机器、土地等农业生产要素</p>
            </div>
            <div class="feature-item">
                <i class="fas fa-store"></i>
                <h3>农资商城</h3>
                <p>一站式购买农药化肥，品质保证</p>
            </div>
            <div class="feature-item">
                <i class="fas fa-tractor"></i>
                <h3>农机世界</h3>
                <p>了解各类农机信息，补贴与经销商资讯</p>
            </div>
            <div class="feature-item">
                <i class="fas fa-camera"></i>
                <h3>水印相机</h3>
                <p>地块拍照带水印和经纬度，提供可信实地记录</p>
            </div>
            <div class="feature-item">
                <i class="fas fa-book"></i>
                <h3>农活记录</h3>
                <p>记录作业信息，方便跨区机手管理服务</p>
            </div>
            <div class="feature-item">
                <i class="fas fa-calculator"></i>
                <h3>农事记账</h3>
                <p>记录农事活动相关收支明细</p>
            </div>
            <div class="feature-item">
                <i class="fas fa-video"></i>
                <h3>农业视频</h3>
                <p>查看先进农机作业视频，开阔眼界</p>
            </div>
        </div>
    </section>

    <section class="cta">
        <h2>立即开启智慧农业新时代</h2>
        <p>下载测亩易APP，体验全方位数字农事解决方案</p>
        <a href="#" class="cta-button">
            <i class="fas fa-download"></i>
            立即下载
        </a>
    </section>
</body>
</html>
