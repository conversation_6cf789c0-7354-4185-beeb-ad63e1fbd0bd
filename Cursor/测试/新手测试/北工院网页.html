<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>北京工业职业技术学院</title>
    <style>
        :root {
            --primary-color: #1a1a2e;    /* 深色背景 */
            --secondary-color: #00fff5;   /* 赛博朋克青色 */
            --accent-color: #ff2a6d;      /* 赛博朋克粉色 */
            --neon-purple: #b537f2;       /* 霓虹紫 */
            --neon-yellow: #ffff00;       /* 霓虹黄 */
            --card-bg: rgba(255, 255, 255, 0.05);
        }

        body {
            background-color: var(--primary-color);
            background-image: 
                linear-gradient(135deg, rgba(0, 255, 245, 0.05) 0%, transparent 100%),
                linear-gradient(45deg, rgba(255, 42, 109, 0.05) 0%, transparent 100%);
            color: #fff;
            font-family: "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif;
        }

        /* Bento Grid 布局 */
        .info-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1.5rem;
            padding: 1rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        /* Bento 卡片基础样式 */
        .info-card {
            background: rgba(20, 20, 35, 0.5);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(0, 255, 245, 0.1);
            transition: all 0.3s ease;
        }

        /* 卡片悬浮效果 */
        .info-card:hover {
            transform: translateY(-5px);
            border-color: var(--secondary-color);
            box-shadow: 0 0 20px rgba(0, 255, 245, 0.2);
        }

        /* 卡片尺寸变化 */
        .info-card.large {
            grid-column: span 2;
        }

        .info-card.tall {
            grid-row: span 2;
        }

        /* 赛博朋克装饰元素 */
        .info-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg,
                transparent 0%,
                rgba(0, 255, 245, 0.05) 50%,
                transparent 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .info-card:hover::before {
            opacity: 1;
        }

        /* 霓虹文字效果 */
        .info-card h3 {
            color: var(--secondary-color);
            font-size: 1.2rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-shadow: 0 0 10px rgba(0, 255, 245, 0.5);
        }

        /* 装饰性图标 */
        .iot-decoration {
            position: absolute;
            right: 1rem;
            top: 1rem;
            font-size: 1.5rem;
            opacity: 0.5;
            color: var(--accent-color);
            text-shadow: 0 0 10px var(--accent-color);
        }

        /* 列表样式 */
        .feature-list li {
            margin-bottom: 0.8rem;
            padding-left: 1.5rem;
            position: relative;
            color: rgba(255, 255, 255, 0.9);
        }

        .feature-list li i {
            position: absolute;
            left: 0;
            color: var(--accent-color);
            text-shadow: 0 0 5px var(--accent-color);
        }

        /* 添加网格背景 */
        .grid-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(90deg, rgba(0, 255, 245, 0.05) 1px, transparent 1px),
                linear-gradient(0deg, rgba(0, 255, 245, 0.05) 1px, transparent 1px);
            background-size: 30px 30px;
            pointer-events: none;
            z-index: -1;
        }

        /* 响应式调整 */
        @media (max-width: 1024px) {
            .info-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .info-grid {
                grid-template-columns: 1fr;
            }
            .info-card.large {
                grid-column: span 1;
            }
        }
    </style>

    <!-- 添加字体图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css">
    <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
</head>
<body>
    <div class="hero">
        <h1>北京工业职业技术学院</h1>
        <p>创新引领 • 技能成就未来</p>
    </div>

    <div class="container">
        <section class="section">
            <h2>学院概况</h2>
            <div class="info-grid">
                <div class="info-card large intro">
                    <i class="ri-building-2-line iot-decoration"></i>
                    <h3><i class="ri-school-line"></i> 学院简介</h3>
                    <p>北京工业职业技术学院创建于1960年，是北京市属全日制普通高等院校，是国家示范性高等职业院校。学院坚持"立足北京、服务首都"的办学方针，培养高素质技术技能人才。</p>
                </div>
                <div class="info-card feature">
                    <i class="ri-medal-line iot-decoration"></i>
                    <h3>办学特色</h3>
                    <ul class="feature-list">
                        <li>国家示范性高等职业院校</li>
                        <li>北京市重点建设职业院校</li>
                        <li>产教融合、校企合作的典范</li>
                        <li>高素质技术技能人才培养基地</li>
                    </ul>
                </div>
            </div>
        </section>

        <section class="section">
            <h2>优势专业</h2>
            <div class="info-grid">
                <div class="info-card mechanical">
                    <i class="ri-robot-line iot-decoration"></i>
                    <h3><i class="ri-tools-line"></i> 机械制造</h3>
                    <ul class="feature-list">
                        <li><i class="ri-cpu-line"></i> 数控技术</li>
                        <li><i class="ri-shape-2-line"></i> 模具设计与制造</li>
                        <li><i class="ri-settings-5-line"></i> 机械设计与制造</li>
                    </ul>
                </div>
                <div class="info-card electronic">
                    <i class="ri-circuit-board-line iot-decoration"></i>
                    <h3><i class="ri-chip-line"></i> 电子信息</h3>
                    <ul class="feature-list">
                        <li><i class="ri-dashboard-3-line"></i> 电气自动化技术</li>
                        <li><i class="ri-cloud-line"></i> 计算机网络技术</li>
                        <li><i class="ri-code-box-line"></i> 软件技术</li>
                    </ul>
                </div>
                <div class="info-card service">
                    <i class="ri-store-line iot-decoration"></i>
                    <h3><i class="ri-store-line"></i> 现代服务</h3>
                    <ul class="feature-list">
                        <li>会计</li>
                        <li>物流管理</li>
                        <li>市场营销</li>
                    </ul>
                </div>
            </div>
        </section>

        <section class="section">
            <h2>校园生活</h2>
            <div class="info-grid">
                <div class="info-card">
                    <h3>文化活动</h3>
                    <ul class="feature-list">
                        <li>社团活动丰富多彩</li>
                        <li>校园文化节</li>
                        <li>技能竞赛</li>
                    </ul>
                </div>
                <div class="info-card">
                    <h3>实践基地</h3>
                    <ul class="feature-list">
                        <li>校内实训中心</li>
                        <li>校外实习基地</li>
                        <li>创新创业中心</li>
                    </ul>
                </div>
            </div>
        </section>
    </div>

    <script>
        // 监听滚动，添加动画效果
        const sections = document.querySelectorAll('.section');
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, { threshold: 0.1 });

        sections.forEach(section => observer.observe(section));
    </script>

    <!-- 添加浮动粒子 -->
    <script>
        function createParticles() {
            const particles = document.createElement('div');
            particles.className = 'particles';
            document.body.appendChild(particles);

            for (let i = 0; i < 50; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.width = Math.random() * 3 + 'px';
                particle.style.height = particle.style.width;
                particle.style.left = Math.random() * 100 + 'vw';
                particle.style.animationDelay = Math.random() * 20 + 's';
                particles.appendChild(particle);
            }
        }

        document.addEventListener('DOMContentLoaded', createParticles);
    </script>

    <!-- 添加连接线动画 -->
    <script>
        function createConnectionLines() {
            const container = document.createElement('div');
            container.className = 'connection-lines';
            document.body.appendChild(container);

            for (let i = 0; i < 10; i++) {
                const line = document.createElement('div');
                line.className = 'connection-line';
                line.style.width = Math.random() * 100 + 50 + 'px';
                line.style.top = Math.random() * 100 + '%';
                line.style.left = Math.random() * 100 + '%';
                line.style.transform = `rotate(${Math.random() * 360}deg)`;
                line.style.animationDelay = Math.random() * 2 + 's';
                container.appendChild(line);
            }
        }

        document.addEventListener('DOMContentLoaded', createConnectionLines);
    </script>

    <!-- 添加网格背景 -->
    <div class="grid-overlay"></div>
</body>
</html>

