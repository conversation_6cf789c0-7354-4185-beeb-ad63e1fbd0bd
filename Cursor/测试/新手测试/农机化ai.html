<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>农机化AI核心应用方向</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@24,400,0,0" />
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f7fa;
        }

        .header {
            background: linear-gradient(135deg, #1e88e5 0%, #64b5f6 100%);
            color: white;
            padding: 40px 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
        }

        .section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s;
        }

        .section:hover {
            transform: translateY(-5px);
        }

        .section h2 {
            color: #1e88e5;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e3f2fd;
        }

        .section h3 {
            color: #2196f3;
            margin: 25px 0 15px;
            padding-left: 12px;
            font-size: 1.2em;
        }

        .feature-list {
            margin-top: 20px;
        }

        .feature-item {
            display: grid;
            grid-template-columns: 40px 1fr;
            align-items: start;
            gap: 15px;
            margin-bottom: 15px;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            transition: transform 0.2s;
        }

        .feature-item:hover {
            transform: translateY(-2px);
            background: #e3f2fd;
        }

        .emoji {
            font-size: 1.5em;
            width: 40px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
        }

        .feature-item p {
            margin: 0;
            padding: 2px 0;
            line-height: 1.4;
        }

        @media (max-width: 1024px) {
            .container {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
            }
        }

        .icon-wrapper {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.04);
        }

        .material-symbols-rounded {
            font-size: 24px;
            color: #1e88e5;
        }

        .section:nth-child(1) .material-symbols-rounded { color: #2196F3; }
        .section:nth-child(2) .material-symbols-rounded { color: #4CAF50; }
        .section:nth-child(3) .material-symbols-rounded { color: #FF5722; }
    </style>
</head>
<body>
    <header class="header">
        <h1>农机化AI核心应用方向</h1>
    </header>

    <div class="container">
        <div class="section">
            <h2>
                <div class="icon-wrapper">
                    <span class="material-symbols-rounded">public</span>
                </div>
                辨识耕地边界
            </h2>
            <p><strong>目标：</strong>利用高清卫星影像、遥感和无人机影像技术，描绘全国耕地范围。</p>
            
            <div class="feature-list">
                <div class="feature-item">
                    <div class="icon-wrapper">
                        <span class="material-symbols-rounded">smart_toy</span>
                    </div>
                    <p>通过AI模型对影像数据进行分割与识别，确定耕地边界</p>
                </div>
                <div class="feature-item">
                    <div class="icon-wrapper">
                        <span class="material-symbols-rounded">map</span>
                    </div>
                    <p>生成全国范围内的地块地图，形成地块数据库</p>
                </div>
                <div class="feature-item">
                    <div class="icon-wrapper">
                        <span class="material-symbols-rounded">policy</span>
                    </div>
                    <p>为耕地保护政策制定与监管提供科学依据</p>
                </div>
                <div class="feature-item">
                    <div class="icon-wrapper">
                        <span class="material-symbols-rounded">analytics</span>
                    </div>
                    <p>准确掌握耕地资源分布，避免重复统计</p>
                </div>
                <div class="feature-item">
                    <div class="icon-wrapper">
                        <span class="material-symbols-rounded">monitoring</span>
                    </div>
                    <p>监测耕地变化，预警非法占地或耕地流失</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>
                <div class="icon-wrapper">
                    <span class="material-symbols-rounded">route</span>
                </div>
                自动统计农机作业
            </h2>
            <p><strong>目标：</strong>基于轨迹与运行数据，实现对全国农机作业的精准统计。</p>
            
            <div class="feature-list">
                <div class="feature-item">
                    <div class="icon-wrapper">
                        <span class="material-symbols-rounded">route</span>
                    </div>
                    <p>分析机具的作业轨迹与状态，自动计算作业面积、时长和油耗</p>
                </div>
                <div class="feature-item">
                    <div class="icon-wrapper">
                        <span class="material-symbols-rounded">description</span>
                    </div>
                    <p>实时生成区域化作业统计报表，辅助政策核算</p>
                </div>
                <div class="feature-item">
                    <div class="icon-wrapper">
                        <span class="material-symbols-rounded">admin_panel_settings</span>
                    </div>
                    <p>辅助地方农业部门监督作业完成情况</p>
                </div>
                <div class="feature-item">
                    <div class="icon-wrapper">
                        <span class="material-symbols-rounded">payments</span>
                    </div>
                    <p>提供透明、客观的作业量依据，助力补贴资金的精准发放</p>
                </div>
                <div class="feature-item">
                    <div class="icon-wrapper">
                        <span class="material-symbols-rounded">speed</span>
                    </div>
                    <p>减少人工统计误差，提升工作效率</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>
                <div class="icon-wrapper">
                    <span class="material-symbols-rounded">database</span>
                </div>
                一体化数据管理
            </h2>
            <p><strong>目标：</strong>构建全国统一的农机化大数据平台，整合多源数据，提升管理效率。</p>
            
            <div class="feature-list">
                <div class="feature-item">
                    <div class="icon-wrapper">
                        <span class="material-symbols-rounded">map</span>
                    </div>
                    <p>汇总各地耕地信息、农机轨迹数据与作业统计</p>
                </div>
                <div class="feature-item">
                    <div class="icon-wrapper">
                        <span class="material-symbols-rounded">query_stats</span>
                    </div>
                    <p>利用大模型对不同区域的农机化推广效果进行分析与对比</p>
                </div>
                <div class="feature-item">
                    <div class="icon-wrapper">
                        <span class="material-symbols-rounded">share</span>
                    </div>
                    <p>实现跨区域数据共享，支持全国范围调度与监控</p>
                </div>
                <div class="feature-item">
                    <div class="icon-wrapper">
                        <span class="material-symbols-rounded">visibility</span>
                    </div>
                    <p>提供全局视角，帮助农业主管部门掌握全国农机化进展</p>
                </div>
                <div class="feature-item">
                    <div class="icon-wrapper">
                        <span class="material-symbols-rounded">psychology</span>
                    </div>
                    <p>形成科学化、数据化的决策依据，优化资源配置</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
