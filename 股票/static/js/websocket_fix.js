// WebSocket初始化和管理
function initWebSocket() {
    if (!websocketEnabled) {
        console.log('WebSocket功能已禁用');
        return;
    }
    
    try {
        // 创建Socket.IO连接
        socket = io.connect(window.location.origin, {
            reconnection: true,
            reconnectionDelay: 1000,
            reconnectionDelayMax: 5000,
            reconnectionAttempts: Infinity
        });
        
        // 连接成功事件
        socket.on('connect', function() {
            console.log('WebSocket连接成功');
            socketConnected = true;
            
            // 显示连接成功消息
            const statusSpan = document.getElementById('websocketStatus');
            if (statusSpan) statusSpan.innerHTML = 'WebSocket已连接 <i class="bi bi-check-circle-fill text-success"></i>';
            
            // 如果当前有股票且是分时图模式，则自动订阅
            if (currentSymbol && currentChartType === 'timeshare') {
                subscribeToRealtimeData(currentSymbol);
            }
        });
        
        // 连接断开事件
        socket.on('disconnect', function() {
            console.log('WebSocket连接断开');
            socketConnected = false;
            currentSubscription = null;
            
            // 显示连接断开消息
            const statusSpan = document.getElementById('websocketStatus');
            if (statusSpan) statusSpan.innerHTML = 'WebSocket已断开 <i class="bi bi-x-circle-fill text-danger"></i>';
        });
        
        // 连接错误事件
        socket.on('connect_error', function(error) {
            console.error('WebSocket连接错误:', error);
            socketConnected = false;
            
            // 显示连接错误消息
            const statusSpan = document.getElementById('websocketStatus');
            if (statusSpan) statusSpan.innerHTML = 'WebSocket连接错误 <i class="bi bi-exclamation-triangle-fill text-warning"></i>';
        });
        
        // 监听实时数据更新
        socket.on('realtime_update', function(data) {
            console.log('收到实时数据:', data);
            latestRealtimeData = data;
            
            // 更新股票信息
            updateStockInfo(data);
            
            // 如果是分时图模式，则更新图表
            if (currentChartType === 'timeshare' && data.symbol === currentSymbol) {
                updateTimeShareChart(data);
            }
            
            // 显示数据来源提示
            if (data.source === 'websocket_mock') {
                showWebSocketTestResult(`收到模拟数据: ${data.symbol} - ${data.last_done}`, 'success');
            }
        });
        
        // 监听测试响应
        socket.on('test_response', function(data) {
            console.log('收到测试响应:', data);
        });
        
        // 监听连接响应
        socket.on('connect_response', function(data) {
            console.log('收到连接响应:', data);
            if (data.status === 'connected') {
                showWebSocketTestResult('服务器确认WebSocket连接成功', 'success');
            }
        });
        
        // 监听订阅响应
        socket.on('subscribe_response', function(data) {
            console.log('收到订阅响应:', data);
            if (data.status === 'subscribed') {
                showWebSocketTestResult(`成功订阅 ${data.symbol} 的实时数据`, 'success');
            }
        });
        
        // 监听错误消息
        socket.on('error', function(data) {
            console.error('WebSocket错误:', data);
            showWebSocketTestResult(`WebSocket错误: ${data.message || '未知错误'}`, 'danger');
        });
        
        console.log('WebSocket初始化完成');
    } catch (error) {
        console.error('初始化WebSocket时出错:', error);
        websocketEnabled = false;
        showWebSocketTestResult(`WebSocket初始化失败: ${error.message || '未知错误'}`, 'danger');
    }
}

// 订阅实时数据
function subscribeToRealtimeData(symbol) {
    if (!socketConnected || !websocketEnabled) {
        console.log('WebSocket未连接或已禁用，无法订阅数据');
        return;
    }
    
    // 如果已经订阅了其他股票，先取消订阅
    if (currentSubscription && currentSubscription !== symbol) {
        socket.emit('unsubscribe_realtime', { symbol: currentSubscription });
        console.log(`取消订阅 ${currentSubscription} 的实时数据`);
    }
    
    // 订阅新的股票
    socket.emit('subscribe_realtime', { symbol: symbol });
    currentSubscription = symbol;
    console.log(`订阅 ${symbol} 的实时数据`);
}

// 取消订阅实时数据
function unsubscribeFromRealtimeData(symbol) {
    if (!socketConnected || !currentSubscription) return;
    
    socket.emit('unsubscribe_realtime', { symbol: symbol || currentSubscription });
    console.log(`取消订阅 ${symbol || currentSubscription} 的实时数据`);
    currentSubscription = null;
}

// 更新分时图
function updateTimeShareChart(data) {
    if (!timeShareLineSeries || !data) return;
    
    const timestamp = data.time;
    const price = data.last_done;
    const volume = data.volume;
    
    // 添加/更新分时线数据点
    timeShareLineSeries.update({
        time: timestamp,
        value: price
    });
    
    // 更新成交量数据
    if (timeShareVolumeSeries) {
        timeShareVolumeSeries.update({
            time: timestamp,
            value: volume
        });
    }
} 