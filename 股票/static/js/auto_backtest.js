/**
 * 自动回测功能模块
 * 
 * 这个模块为系统添加实时自动回测功能，
 * 当K线数据更新时自动触发回测。
 */

// 全局变量
window.autoBacktestEnabled = false;
window.lastBacktestTime = 0;
window.klineTriggerControlLoaded = false;

// 动态加载K线触发控制脚本
function loadKlineTriggerControl() {
    if (window.klineTriggerControlLoaded) return;
    
    console.log('加载K线触发控制模块...');
    const script = document.createElement('script');
    script.src = '/static/js/kline_trigger_control.js';
    script.onload = function() {
        console.log('K线触发控制模块加载成功');
        window.klineTriggerControlLoaded = true;
    };
    script.onerror = function() {
        console.error('K线触发控制模块加载失败');
    };
    document.head.appendChild(script);
}

// 自动回测开关初始化函数
function initAutoBacktestSwitch() {
    console.log('初始化自动回测开关...');
    // 获取自动回测开关元素
    const autoBacktestSwitch = document.getElementById('autoBacktestSwitch');
    const autoBacktestStatus = document.getElementById('autoBacktestStatus');
    
    // 根据开关状态设置初始值
    if (autoBacktestSwitch) {
        window.autoBacktestEnabled = autoBacktestSwitch.checked;
        
        // 添加事件监听
        autoBacktestSwitch.addEventListener('change', function() {
            window.autoBacktestEnabled = this.checked;
            
            if (window.autoBacktestEnabled) {
                console.log('自动回测功能已启用');
                if (autoBacktestStatus) autoBacktestStatus.innerHTML = '自动回测 <i class="bi bi-check-circle-fill text-success"></i>';
                
                // 确保K线触发控制模块已加载
                if (!window.klineTriggerControlLoaded) {
                    loadKlineTriggerControl();
                }
                
                // 立即执行一次回测，不用等待下一次K线更新
                if (window.currentSymbol) {
                    console.log('立即执行初始自动回测');
                    // 调用回测函数
                    if (typeof window.runBacktest === 'function') {
                        window.runBacktest({
                            useStandardMarkers: true,
                            isAutoBacktest: true
                        });
                        window.lastBacktestTime = Date.now();
                    } else {
                        console.error('runBacktest函数不可用');
                    }
                }
            } else {
                console.log('自动回测功能已禁用');
                if (autoBacktestStatus) autoBacktestStatus.innerHTML = '自动回测';
            }
        });
        
        // 自动创建完整K线触发控制开关
        if (!document.getElementById('completeKlineOnlySwitch')) {
            createCompleteKlineTriggerSwitch(autoBacktestSwitch);
        }
        
        console.log('自动回测开关初始化完成');
    } else {
        console.warn('自动回测开关元素未找到');
    }
}

/**
 * 创建完整K线触发控制开关（如果K线触发控制模块未加载）
 */
function createCompleteKlineTriggerSwitch(autoBacktestSwitch) {
    if (window.klineTriggerControlLoaded) return;
    
    // 创建控制开关元素
    const switchDiv = document.createElement('div');
    switchDiv.className = 'form-check form-switch mt-2';
    switchDiv.innerHTML = `
        <input class="form-check-input" type="checkbox" id="completeKlineOnlySwitch" checked>
        <label class="form-check-label" for="completeKlineOnlySwitch">
            <span id="completeKlineOnlyStatus">仅完整K线触发</span>
        </label>
        <div class="small text-muted">只在完整K线数据到达时触发回测，提高信号稳定性</div>
    `;
    
    // 插入到自动回测开关之后
    autoBacktestSwitch.parentNode.parentNode.insertBefore(switchDiv, autoBacktestSwitch.parentNode.nextSibling);
    
    // 添加事件监听器
    const completeKlineOnlySwitch = document.getElementById('completeKlineOnlySwitch');
    if (completeKlineOnlySwitch) {
        window.completeKlineOnlyMode = completeKlineOnlySwitch.checked;
        
        completeKlineOnlySwitch.addEventListener('change', function() {
            window.completeKlineOnlyMode = this.checked;
            console.log(`仅完整K线触发模式 ${window.completeKlineOnlyMode ? '已启用' : '已禁用'}`);
            
            // 更新显示
            const statusSpan = document.getElementById('completeKlineOnlyStatus');
            if (statusSpan) {
                if (window.completeKlineOnlyMode) {
                    statusSpan.innerHTML = '仅完整K线触发 <span class="badge bg-success">已启用</span>';
                } else {
                    statusSpan.innerHTML = '仅完整K线触发';
                }
            }
        });
        
        console.log('完整K线触发控制开关已添加');
    }
}

// K线数据更新处理函数 (用于扩展原有的处理函数)
function handleKlineUpdateWithAutoBacktest(data) {
    // 检查是否有数据
    if (!data || !data.symbol) return;
    
    // 在具有完整K线触发控制时，检查数据是否应该被处理
    if (window.completeKlineOnlyMode !== undefined) {
        // 检查数据是否包含完成标志
        const isCompleted = data.isCompleted || data.completed || false;
        
        // 如果未完成且启用了完整K线模式，则不处理
        if (!isCompleted && window.completeKlineOnlyMode) {
            console.log('收到未完成的K线数据，不触发自动回测');
            return;
        }
    }
    
    // 自动回测功能
    if (window.autoBacktestEnabled && data.symbol === window.currentSymbol) {
        const currentTime = Date.now();
        // 检查距离上次回测的时间间隔，防止过于频繁的回测
        if (!window.lastBacktestTime || (currentTime - window.lastBacktestTime) > 1000) {
            console.log('触发自动回测');
            // 运行回测，并标记为自动回测
            if (typeof window.runBacktest === 'function') {
                window.runBacktest({
                    useStandardMarkers: true,
                    isAutoBacktest: true
                });
                window.lastBacktestTime = currentTime;
            } else {
                console.error('runBacktest函数不可用');
            }
        }
    }
}

// 在文档加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('auto_backtest.js: 文档已加载，等待初始化...');
    
    // 尝试加载K线触发控制模块
    loadKlineTriggerControl();
    
    // 延迟初始化以确保其他组件已加载
    setTimeout(function() {
        console.log('auto_backtest.js: 开始初始化自动回测功能');
        initAutoBacktestSwitch();
        
        // 如果RealtimeKlineService存在，则挂钩到它的K线更新处理
        if (window.RealtimeKlineService) {
            console.log('auto_backtest.js: 检测到RealtimeKlineService，正在注册自动回测处理函数');
            // 订阅K线数据更新事件
            window.RealtimeKlineService.subscribe('*', function(data) {
                handleKlineUpdateWithAutoBacktest(data);
            });
        } else {
            console.warn('auto_backtest.js: 未检测到RealtimeKlineService，自动回测可能无法正常工作');
        }
    }, 2000); // 延迟2秒初始化
});

// 导出模块函数
window.AutoBacktest = {
    init: initAutoBacktestSwitch,
    handleKlineUpdate: handleKlineUpdateWithAutoBacktest,
    loadKlineTriggerControl: loadKlineTriggerControl
}; 