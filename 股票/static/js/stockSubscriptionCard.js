class StockSubscriptionCard {
    constructor(container) {
        this.container = container;
        this.subscribedStocks = new Set();
        this.init();
    }

    init() {
        this.render();
        this.bindEvents();
    }

    render() {
        const html = `
        <div class="card mb-3">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">股票订阅管理</h5>
                <button class="btn btn-primary btn-sm" id="addStockBtn">
                    <i class="fas fa-plus"></i> 添加股票
                </button>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="input-group">
                        <select class="form-select" id="marketSelect">
                            <option value="HK">港股市场</option>
                            <option value="CN">A股市场</option>
                            <option value="US">美股市场</option>
                        </select>
                        <input type="text" class="form-control" id="stockSearchInput" 
                               placeholder="输入股票代码或名称搜索">
                        <button class="btn btn-outline-secondary" id="searchStockBtn">
                            搜索
                        </button>
                    </div>
                </div>
                <div id="searchResults" class="mb-3" style="display:none;">
                    <!-- 搜索结果将在这里显示 -->
                </div>
                <div id="subscribedStocksList">
                    <!-- 已订阅的股票列表将在这里显示 -->
                </div>
            </div>
        </div>`;

        this.container.innerHTML = html;
    }

    bindEvents() {
        const searchInput = document.getElementById('stockSearchInput');
        const searchBtn = document.getElementById('searchStockBtn');
        const marketSelect = document.getElementById('marketSelect');

        searchBtn.addEventListener('click', () => this.searchStocks());
        searchInput.addEventListener('keyup', (e) => {
            if (e.key === 'Enter') this.searchStocks();
        });

        // 智能模式：根据输入自动判断市场
        searchInput.addEventListener('input', () => this.autoDetectMarket(searchInput.value));
    }

    autoDetectMarket(input) {
        const marketSelect = document.getElementById('marketSelect');
        if (/^\d{6}$/.test(input)) {
            // 6位数字，可能是A股
            marketSelect.value = 'CN';
        } else if (/^\d{5}$/.test(input)) {
            // 5位数字，可能是港股
            marketSelect.value = 'HK';
        } else if (/^[A-Za-z]+$/.test(input)) {
            // 纯字母，可能是美股
            marketSelect.value = 'US';
        }
    }

    async searchStocks() {
        const input = document.getElementById('stockSearchInput').value;
        const market = document.getElementById('marketSelect').value;
        const resultsDiv = document.getElementById('searchResults');

        try {
            const response = await fetch(`/api/search_stocks?query=${input}&market=${market}`);
            const data = await response.json();

            if (data.stocks && data.stocks.length > 0) {
                this.displaySearchResults(data.stocks);
            } else {
                resultsDiv.innerHTML = '<div class="alert alert-info">未找到匹配的股票</div>';
            }
            resultsDiv.style.display = 'block';
        } catch (error) {
            console.error('搜索股票时出错:', error);
            resultsDiv.innerHTML = '<div class="alert alert-danger">搜索时发生错误</div>';
        }
    }

    displaySearchResults(stocks) {
        const resultsDiv = document.getElementById('searchResults');
        let html = '<div class="list-group">';
        
        stocks.forEach(stock => {
            html += `
            <div class="list-group-item d-flex justify-content-between align-items-center">
                <div>
                    <strong>${stock.code}</strong> - ${stock.name}
                </div>
                <button class="btn btn-success btn-sm" 
                        onclick="subscriptionCard.addStock('${stock.code}', '${stock.name}')">
                    添加
                </button>
            </div>`;
        });
        
        html += '</div>';
        resultsDiv.innerHTML = html;
    }

    addStock(code, name) {
        if (this.subscribedStocks.has(code)) return;

        this.subscribedStocks.add(code);
        this.updateSubscribedStocksList();
        
        // 自动关联到控制面板
        if (window.controlPanel) {
            window.controlPanel.setStock(code);
        }
    }

    removeStock(code) {
        this.subscribedStocks.delete(code);
        this.updateSubscribedStocksList();
        
        // 如果有订阅，取消订阅
        if (window.socket) {
            window.socket.emit('unsubscribe_realtime', { symbol: code });
        }
    }

    updateSubscribedStocksList() {
        const listDiv = document.getElementById('subscribedStocksList');
        let html = '';

        this.subscribedStocks.forEach(code => {
            html += `
            <div class="alert alert-secondary d-flex justify-content-between align-items-center">
                <div>
                    <strong>${code}</strong>
                </div>
                <div>
                    <button class="btn btn-primary btn-sm me-2" 
                            onclick="subscriptionCard.toggleSubscription('${code}')">
                        订阅
                    </button>
                    <button class="btn btn-danger btn-sm" 
                            onclick="subscriptionCard.removeStock('${code}')">
                        删除
                    </button>
                </div>
            </div>`;
        });

        listDiv.innerHTML = html;
    }

    toggleSubscription(code) {
        // 实现WebSocket订阅/取消订阅逻辑
        if (window.socket) {
            window.socket.emit('subscribe_realtime', { symbol: code });
            
            // 自动关联到控制面板
            if (window.controlPanel) {
                window.controlPanel.setStock(code);
            }
        }
    }
}

// 全局实例
window.subscriptionCard = null;

// 初始化函数
function initStockSubscriptionCard() {
    const container = document.getElementById('subscription-card');
    if (container) {
        window.subscriptionCard = new StockSubscriptionCard(container);
    }
}

// 当DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', initStockSubscriptionCard); 