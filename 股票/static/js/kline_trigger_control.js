/**
 * K线触发控制模块
 * 用于控制自动回测的触发条件，确保只在完整K线数据到达时触发回测
 */

// 全局变量
let completeKlineOnlyMode = true; // 默认启用仅完整K线触发模式
let pendingKlineData = null; // 存储未完成K线的数据
let lastCompletedKlineTime = 0; // 最后完成K线的时间戳

/**
 * 初始化K线触发控制开关
 * 在页面加载后调用此函数
 */
function initKlineTriggerControl() {
    console.log('初始化K线触发控制...');
    
    // 创建控制开关元素并添加到页面
    createKlineTriggerSwitch();
    
    // 监听原始WebSocket事件
    if (typeof socket !== 'undefined' && socket) {
        const originalKlineUpdateHandler = socket._callbacks['$kline_update'] ? 
            socket._callbacks['$kline_update'][0] : null;
        
        if (originalKlineUpdateHandler) {
            // 移除原始处理程序
            socket.off('kline_update', originalKlineUpdateHandler);
            
            // 添加新的处理程序
            socket.on('kline_update', (data) => {
                // 先通过我们的过滤器
                if (handleKlineUpdateWithTriggerControl(data)) {
                    // 如果通过过滤器，则调用原始处理程序
                    originalKlineUpdateHandler(data);
                }
            });
            
            console.log('已替换K线更新处理程序，增加触发控制');
        } else {
            console.warn('未找到原始K线更新处理程序，仅使用新的处理程序');
            socket.on('kline_update', handleKlineUpdateWithTriggerControl);
        }
    }
}

/**
 * 创建并添加K线触发控制开关
 */
function createKlineTriggerSwitch() {
    // 寻找插入点（自动回测开关之后）
    const autoBacktestSwitchDiv = document.querySelector('.form-check.form-switch:has(#autoBacktestSwitch)');
    if (!autoBacktestSwitchDiv) {
        console.warn('未找到自动回测开关，无法添加完整K线触发控制开关');
        return;
    }
    
    // 创建控制开关元素
    const switchDiv = document.createElement('div');
    switchDiv.className = 'form-check form-switch mt-2';
    switchDiv.innerHTML = `
        <input class="form-check-input" type="checkbox" id="completeKlineOnlySwitch" checked>
        <label class="form-check-label" for="completeKlineOnlySwitch">
            <span id="completeKlineOnlyStatus">仅完整K线触发</span>
        </label>
        <div class="small text-muted">只在完整K线数据到达时触发回测，提高信号稳定性</div>
    `;
    
    // 插入到自动回测开关之后
    autoBacktestSwitchDiv.parentNode.insertBefore(switchDiv, autoBacktestSwitchDiv.nextSibling);
    
    // 添加事件监听器
    const completeKlineOnlySwitch = document.getElementById('completeKlineOnlySwitch');
    if (completeKlineOnlySwitch) {
        completeKlineOnlySwitch.checked = completeKlineOnlyMode;
        
        completeKlineOnlySwitch.addEventListener('change', function() {
            completeKlineOnlyMode = this.checked;
            console.log(`仅完整K线触发模式 ${completeKlineOnlyMode ? '已启用' : '已禁用'}`);
            
            // 更新显示
            const statusSpan = document.getElementById('completeKlineOnlyStatus');
            if (statusSpan) {
                if (completeKlineOnlyMode) {
                    statusSpan.innerHTML = '仅完整K线触发 <span class="badge bg-success">已启用</span>';
                } else {
                    statusSpan.innerHTML = '仅完整K线触发';
                }
            }
            
            // 如果关闭完整K线模式，且有待处理的数据，立即处理
            if (!completeKlineOnlyMode && pendingKlineData) {
                console.log('处理待处理的K线数据');
                const tempData = pendingKlineData;
                pendingKlineData = null;
                
                // 使用RealtimeKlineService的处理函数
                if (window.RealtimeKlineService && typeof window.RealtimeKlineService.handleKlineUpdate === 'function') {
                    window.RealtimeKlineService.handleKlineUpdate(tempData);
                }
            }
        });
        
        console.log('完整K线触发控制开关已添加');
    }
}

/**
 * 带触发控制的K线数据更新处理
 * @param {Object} data K线数据
 * @returns {boolean} 是否继续处理此数据
 */
function handleKlineUpdateWithTriggerControl(data) {
    if (!data || !data.symbol) return false;
    
    console.log('接收到K线数据更新', data);
    
    // 如果启用了仅完整K线触发模式，需要判断数据是否是完整K线
    if (completeKlineOnlyMode) {
        // 检查数据是否包含完成标志
        const isCompleted = data.isCompleted || data.completed || false;
        
        // 检查时间戳是否是新的完整K线
        const currentTime = data.time || (data.data && data.data.time) || 0;
        const isNewTime = currentTime > lastCompletedKlineTime;
        
        if (!isCompleted && !isNewTime) {
            console.log('收到未完成的K线数据，暂存但不触发回测');
            pendingKlineData = data;
            return false;
        }
        
        // 更新最后完成K线时间
        if (isCompleted || isNewTime) {
            lastCompletedKlineTime = currentTime;
            console.log(`收到完整K线数据，时间戳: ${currentTime}, 允许触发回测`);
        }
    } else {
        // 非完整K线触发模式
        // 1. 信号一旦产生，就不可撤销
        // 2. 同一根K线内，只产生一个信号
        // 3. 设置信号优先级
        // 4. 信号一旦产生，立即执行
        // 5. 按照设置的滑点价执行
        // 6. 不需要等待K线完整形成
        // 7. 允许二次叠加信号
        const currentTime = data.time || (data.data && data.data.time) || 0;
        
        // 检查是否是新的K线
        if (currentTime > lastCompletedKlineTime) {
            lastCompletedKlineTime = currentTime;
            console.log(`收到新的K线数据，时间戳: ${currentTime}, 允许触发回测`);
        }
        
        // 允许继续处理
        return true;
    }
    
    // 清除待处理数据
    pendingKlineData = null;
    
    // 允许继续处理
    return true;
}

/**
 * 判断K线数据是否已完成
 * @param {Object} data K线数据
 * @returns {boolean} 是否是完整K线
 */
function isCompletedKline(data) {
    // 检查数据中的直接标志
    if (data.isCompleted || data.completed) return true;
    
    // 检查时间戳是否表示已完成的K线
    // 例如，对于1分钟K线，如果当前时间比K线时间戳晚一分钟以上，可以认为是完成的
    const currentTime = Math.floor(Date.now() / 1000);
    const klineTime = data.time || (data.data && data.data.time) || 0;
    
    // 获取K线周期（秒）
    let periodSeconds = 60; // 默认1分钟
    const periodSelect = document.getElementById('periodSelect');
    if (periodSelect) {
        const period = periodSelect.value;
        if (period === 'day') {
            periodSeconds = 86400; // 日线
        } else {
            periodSeconds = parseInt(period) * 60; // 分钟转秒
        }
    }
    
    // 如果当前时间已经超过K线时间加一个周期，认为K线已完成
    return currentTime >= (klineTime + periodSeconds);
}

// 在文档加载完成后初始化控制
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保原始处理程序已加载
    setTimeout(initKlineTriggerControl, 1000);
});

// 导出函数供外部使用
window.KlineTriggerControl = {
    initKlineTriggerControl,
    isCompletedKline,
    setCompleteKlineOnlyMode: function(enabled) {
        completeKlineOnlyMode = enabled;
        const switchElem = document.getElementById('completeKlineOnlySwitch');
        if (switchElem) switchElem.checked = enabled;
    }
}; 