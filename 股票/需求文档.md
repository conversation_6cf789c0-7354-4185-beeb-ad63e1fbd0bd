---
markmap:
  colorFreezeLevel: 2
  maxWidth: 450
---

# **股票交易信号系统BUG修复需求文档**

## **1. 项目背景**

**系统功能**：股票量化交易回测系统
- 用户选择交易策略，系统分析历史K线数据
- 在图表上显示买入/卖出信号标记
- 帮助用户验证策略有效性

**技术架构**：Python Flask后端 + JavaScript前端图表

## **2. 核心问题**

### **问题现象**
用户在图表上看到交易信号**出现后又消失**：
1. 13:41时刻，图表显示买入信号
2. 13:42新K线到达，触发自动回测
3. 13:41的买入信号从图表消失

### **业务影响**
- 用户无法信任系统结果
- 策略回测数据不一致
- 影响投资决策准确性

## **3. 技术原因**

### **根本原因**
**后端重新计算 + 前端全量替换**

1. **后端问题**：每次回测都重新计算整个历史数据，新数据会影响历史指标值
2. **前端问题**：收到新结果后，用`setMarkers([])`清空所有旧信号，然后显示新信号

### **具体流程**
```
T时刻: 数据[1-100] → 计算结果A → 显示信号X ✅
T+1时刻: 数据[1-101] → 计算结果B → 清空所有信号 → 显示新信号 ❌信号X消失
```

## **4. 解决方案**

### **核心思路**
**信号锁定机制**：信号一旦出现就永久保存，不允许删除

### **技术实现**
1. **前端增加信号保险箱**：`lockedSignals = new Map()`
2. **修改信号处理逻辑**：
   - 新信号检查是否已存在，不存在则锁定
   - 显示时使用保险箱中的所有信号
   - 移除`setMarkers([])`清空操作
3. **清空时机**：只在切换股票时清空保险箱

### **代码修改点**
- 文件：`templates/online.html`
- 函数：`updateBacktestResults`, `processAndLockSignals`
- 关键：移除所有`candleSeries.setMarkers([])`调用

## **5. 验收标准**

### **测试场景**
1. 开启自动回测
2. 观察13:41出现的买入信号
3. 等待13:42、13:43新K线到达
4. **预期**：13:41信号始终存在，新信号累加显示

### **成功标准**
- 任何时刻出现的信号永不消失
- 新旧信号可以共存
- 只有切换股票时才清空所有信号

## **6. 风险控制**

### **最小化改动**
- 只修改前端信号显示逻辑
- 不改动后端回测算法
- 不改动UI界面布局

### **回滚方案**
保留原始代码备份，如有问题可快速回滚

---

**修复目标**：让用户看到的交易信号保持稳定一致，提升系统可信度。

---

## **第一部分：强制协作协议**
- ### **1. 核心工作原则**
  - **1.1 逻辑严密**: 实现必须严谨可靠，可被验证，绝不编造。
  - **1.2 聚焦实现**: 沟通聚焦于代码实现本身，不解释技术，只通过代码和日志展现。
  - **1.3 代码纯粹**: 最终交付的代码**禁止包含任何注释**。
  - **1.4 代码质量**: 代码必须一次性跑通，结构紧凑，行数最少。
  - **1.5 确认优先**: 需求不明确时**必须先与我确认**，禁止基于假设开发。
- ### **2. 代码修改规范**
  - **固定格式**:
    - `### 🔍 问题诊断`
    - `### 🧱 原代码`
    - `### ✨ 新代码`
    - `### ✅ 验证方法`
  - **注意**: 只提供可直接替换的代码片段，代码块内无解释文字。
- ### **3. 沟通与排版风格**
  - **3.1 简洁直接**: 每句话清晰有力，禁止客套、废话。
  - **3.2 视觉友好**: 为阅读障碍定制，排版紧凑，多用短句、Emoji、符号、列表。
- ### **4. 用户特性与AI职责**
  - **4.1 用户能力**: 我代码零基础，只能复制和执行。
  - **4.2 AI职责**: 必须洞悉我表达背后的本质需求。
- ### **5. 输出与算法规范**
  - **5.1 逻辑统一**: 所有输出（JSON、日志等）的字段和计算结果，逻辑必须统一。
  - **5.2 禁止擅改**: 除非我明确要求，**禁止擅自修改任何核心处理方式、算法或公式**。

---

## **第二部分：本次具体需求与问题剖析 (V11.0)**
- ### **1. 需求背景**
  - **系统概述**: 基于 Python Flask + WebSocket 的量化交易系统。
  - **核心功能**: 实时数据推送, K线处理, 回测引擎, 前端图表。
  - **文件路径**:
    - **后端**: `F:\Python\不反复（优先性）\app.py`
    - **前端**: `F:\Python\不反复（优先性）\templates\online.html`
- ### **2. 问题描述：交易信号的"重绘"**
  - **核心现象**: 一个在`T`时刻产生的信号，会在`T+1`时刻新K线触发"自动回测"后，从图表上消失。
  - **问题定性**: **未来函数**的一种高级表现形式。`T+1`的数据，反过来推翻了`T`时刻的确定性决策。
- ### **3. 技术根源深度剖析**
  - **3.1. 单一计算模式：全局批量计算**
    - **代码事实**: 系统中所有信号的**唯一来源**是后端的`run_backtest`函数。
    - **工作模式**: 该函数每次都获取一个**完整的历史K线数据块**（`pandas DataFrame`），并对此数据块进行整体运算。
  - **3.2. "自我否定"的逻辑循环**
    - **`T+1`时刻 (自动回测 #1)** -> 输入数据集**A** (截止到`T`) -> 输出结论**X** (T时刻**有**信号) -> **信号出现** ✅
    - **`T+2`时刻 (自动回测 #2)** -> 输入数据集**B** (截止到`T+1`) -> 输出结论**Y** (T时刻**没有**信号) -> **信号消失** ❌
  - **3.3. 技术本质：指标计算的"连锁反应"**
    - **根本原因**: 信号所依赖的技术指标（如**EMA, MACD, RSI, TSI**）具有"记忆性"，在`pandas`的批量计算中，其历史值会因新数据的加入而发生**微调**。
    - **依赖链**: `EMA(t)`的计算结果，直接依赖于`EMA(t-1)`的结果。
    - **批量计算副作用**: 当输入的数据集增加一根新K线后，整个依赖链的计算会从头开始微调，最终导致`T`时刻的EMA值也发生改变，使得原先成立的条件不再成立。
- ### **4. 核心需求与验收标准**
  - **4.1. 信号的不可变性 (核心)**: 信号一旦出现，必须被**永久锁定**，绝不能因后续计算而消失。
  - **4.2. 决策的唯一性**: 对`T`时刻K线的信号判断，在首次计算得出后即为**最终裁决**。
  - **4.3. 验收标准**: `13:41`产生的信号，在`13:42`及之后永不消失。新信号可与旧信号共存。
- ### **5. 解决方案：前端"信号锁定"机制**
  - **5.1. 核心思路**:
    - **职责分离**: 后端`run_backtest`继续做"信号提案生成器"。前端增加"信号仲裁与锁定"模块。
    - **建立缓冲区**: 在前端设立一个"只进不出"的信号保险箱。
  - **5.2. 技术实现要点 (`online.html`)**:
    1.  **定义"保险箱"**: 创建全局`Map`对象 `lockedMarkers`。
    2.  **实现"仲裁官"**: 创建`displayAndLockSignals`函数，负责检查新信号并存入`lockedMarkers`。
    3.  **改造"信使"**: 修改`updateBacktestResults`函数，移除`setMarkers([])`，并将信号交给"仲裁官"。
    4.  **设置"清空"时机**: 在`loadData`函数开头执行`lockedMarkers.clear()`。
- ### **6. 【强制】本次修复的范围与限制**
  - **6.1. 最小化改动**: **唯一目标**是解决"信号重绘"，不重构、不删除无关代码。
  - **6.2. 尊重现有逻辑**: **完整保留**现有的回测引擎、API接口、线程模型。
  - **6.3. 保持UI不变**: **完整保留**前端HTML的全部UI布局、组件和样式。

---

## **第三部分：历史经验与重大澄清**
- ### **7. 已知的失败尝试**
  - **7.1 前端信号分离**: 失败，因为错误地假设存在独立的"实时信号源"。
  - **7.2 后端信号锁定**: 失败，因为后端的`run_backtest`是无状态的，无法实现"记忆"。
- ### **8. 关键经验教训**
  - **8.1 问题本质**: "旧的回测结果"被"新的回测结果"覆盖。
  - **8.2 技术根源**: **后端批量计算** + **前端毁灭性刷新** (`setMarkers([])`)。
  - **8.3 修复方向**: **重心必须在前端**。逻辑必须从"全量覆盖"变为"**增量锁定与状态同步**"。
- ### **8.4 🚨 重大认知澄清：不存在独立的"实时信号"**
  - **事实陈述**: 经过深入分析和确认，本系统**不存在**一个独立于回测功能的、真正的"实时信号引擎"。
  - **唯一来源**: **所有**你看到的买卖信号，其**唯一来源**都是后端的`run_backtest`函数，通过"自动回测"或"手动回测"被触发。
  - **避免误解**: 未来所有的讨论和开发，都必须基于"**信号全部来自回测**"这一核心事实，避免再错误地引入"实时信号"这一概念，从而导致方向性错误。

---

## **第四部分：AI重启指导**
- ### **9. AI重启时的分析要点**
  - **9.1 问题确认**: 信号重绘。**最终认知**：后续的自动回测用**不一致**的新结果覆盖了旧结果。
  - **9.2 技术定位**:
    - **文件**: `app.py`, `templates/online.html`。
    - **核心原因**: 后端`run_backtest`的**批量计算**导致历史指标值变化（隐式未来函数），前端`updateBacktestResults`函数中的 **`candleSeries.setMarkers([])`** 无差别清空了所有历史标记。
  - **9.3 修复策略**:
    - **核心思路**: 前端信号锁定机制。
    - **技术要点**:
        - 在`online.html`中建立一个`Map`对象作为全局的"信号保险箱" (`lockedMarkers`)。
        - 修改`updateBacktestResults`，移除`setMarkers([])`，并将新信号存入"保险箱"。
        - 使用一个统一的函数，每次都用"保险箱"里的全部内容来重绘图表。
- ### **10. AI执行标准**
  - **10.1 修复原则**: 严格按照协作协议的代码修改规范执行。
  - **10.2 成功验证**: 任何回测产生的信号，一旦出现，就**永不消失**，且能与后续新信号**共存**。
  - **10.3 失败处理**: 如果修复失败，立即停止，询问用户确认方向，并记录失败原因。