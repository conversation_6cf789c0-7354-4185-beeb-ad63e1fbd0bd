def get_config():
    from longport.openapi import Config
    app_key = '390577b962234523dec646bd2edf2a9e'
    app_secret = '475f886731258138253f18f0e66355a55b1760c396f0e28a512525f7cecd8cb4'
    access_token = 'm_eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJsb25nYnJpZGdlIiwic3ViIjoiYWNjZXNzX3Rva2VuIiwiZXhwIjoxNzUzNTg1MTM4LCJpYXQiOjE3NDU4MDkxMzksImFrIjoiMzkwNTc3Yjk2MjIzNDUyM2RlYzY0NmJkMmVkZjJhOWUiLCJhYWlkIjoyMDU1NzQzNywiYWMiOiJsYiIsIm1pZCI6MTcxOTgxNDAsInNpZCI6ImI3RjBPeTVIdkRxdEhiZWVQQy93aWc9PSIsImJsIjozLCJ1bCI6MCwiaWsiOiJsYl8yMDU1NzQzNyJ9.bkCpAJPTS_HIAzSG9eAq4egM2nsvYgnzXjiwFPTUyHdwg3_nCuNwMyfOsLwkbpjZwia7Guztri6dG6VIFlaTHl8L1FrY2LXfr89y81f4Oh4U39C98a_cLJO7uhBtRfqTzYsvqIpAmC8WGUnC3qVnafW82VxrDSCRAmWRfw2cHYhy8YcQbjK3NMZlSfae-euqt9O_yXjhJFlrI5u1cm7qdupQaWFM2bjqItS1yKNX3AgguxJCh3uX17Yl6qgcWyjapD2mEPzTZjmTBPrq8PIjT_nraeahFgE8yGyCclMF30sZCNwo5XTfczWn3CBTkfMKzScjPk5ViN1lxqNEFPz8FqGzTGBKop_1T3WYcfWZUFQoT0sKAhjgGOI5pv5FEby_NtKiA-h8u0gGMNi-UEstGCm3QD9hu17f7oWdKyHpATNNne4OEqsq6_8cvR0s4WBNRF3gvBg8mtPQzCJ7bGO2_W_UK_oJqHEq5p5ecZvQOSVLxf09uGbnuQRRpUY9Rpibg4ky7sDr8W_-Agy1dYSEV6w5sjUcGx_4n6ZeULNje5oe0g1um9wyTidZGzIXNF9CHx9hL25pFqjVf9F5xBbDho2UrKz4BDkuulHg0Tk65XY7a6MohMpmp4WHlNH9qI2BZ3bxSJBGo6_7J72_K2_t-L5O-bnRlq3nygmZ6L5oH5g'
    # config = Config.from_env()
    config = Config(app_key=app_key, app_secret=app_secret, access_token=access_token)
    return config
def get_info(config):
    # 获取标的基础信息
    # https://open.longportapp.com/docs/quote/pull/static
    # 运行前请访问“开发者中心”确保账户有正确的行情权限。
    # 如没有开通行情权限，可以通过“LongPort”手机客户端，并进入“我的 - 我的行情 - 行情商城”购买开通行情权限。
    from longport.openapi import QuoteContext
    # print(config)
    ctx = QuoteContext(config)
    resp = ctx.static_info(["700.HK", "AAPL.US", "TSLA.US", "NFLX.US"])
    print(resp)
def get_stock_quote(config):
    # 获取标的实时行情
    # https://open.longportapp.com/docs/quote/pull/quote
    # 运行前请访问“开发者中心”确保账户有正确的行情权限。
    # 如没有开通行情权限，可以通过“LongPort”手机客户端，并进入“我的 - 我的行情 - 行情商城”购买开通行情权限。
    from longport.openapi import QuoteContext
    ctx = QuoteContext(config)
    resp = ctx.quote(["700.HK", "AAPL.US", "TSLA.US", "NFLX.US"])
    print(resp)

def get_stock_k(config):
    # 获取标的 k 线
    # https://open.longportapp.com/docs/quote/pull/candlestick
    # 运行前请访问“开发者中心”确保账户有正确的行情权限。
    # 如没有开通行情权限，可以通过“LongPort”手机客户端，并进入“我的 - 我的行情 - 行情商城”购买开通行情权限。
    from longport.openapi import QuoteContext,  Period, AdjustType
    ctx = QuoteContext(config)
    resp = ctx.candlesticks("700.HK", Period.Day, 10, AdjustType.NoAdjust)
    print(resp)
if __name__ == '__main__':
    config = get_config()
    # get_info(config)
    get_stock_quote(config)
    get_stock_k(config)
