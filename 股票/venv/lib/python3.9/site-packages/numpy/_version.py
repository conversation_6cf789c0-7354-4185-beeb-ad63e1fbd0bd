
# This file was generated by 'versioneer.py' (0.26) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2023-06-25T20:31:30-0600",
 "dirty": false,
 "error": null,
 "full-revisionid": "9315a9072b2636f75c831b4eca9f42a5f67ca2fb",
 "version": "1.24.4"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
