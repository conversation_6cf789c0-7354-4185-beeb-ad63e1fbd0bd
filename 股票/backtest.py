import logging
from datetime import datetime, timedelta
import traceback
import time
import json
import os
import pandas as pd
import numpy as np

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('backtest.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def is_entry_signal(df, index):
    """
    判断是否是入场信号K线
    参数:
        df: DataFrame, 包含OHLCV数据的DataFrame
        index: int, 当前K线的索引
    返回:
        bool: 是否是入场信号K线
    """
    try:
        if index < 2:  # 需要至少3根K线来判断趋势
            return False
            
        prev = df.iloc[index-1]  # Kn-1（信号K线）
        prev2 = df.iloc[index-2]  # Kn-2
        prev3 = df.iloc[index-3]  # Kn-3
        
        # 1. 确认上升趋势：连续三根K线收盘价递增
        trend_up = (prev['close'] > prev2['close'] > prev3['close'])
        
        # 2. 确认信号强度：Kn-1的实体大于前两根K线实体
        body_size_prev = abs(prev['close'] - prev['open'])
        body_size_prev2 = abs(prev2['close'] - prev2['open'])
        body_size_prev3 = abs(prev3['close'] - prev3['open'])
        
        # 3. 确认价格动量：Kn-1的收盘价高于开盘价
        price_momentum = prev['close'] > prev['open']
        
        # 4. 确认波动范围：Kn-1的实体占整体波动范围的比例
        total_range = prev['high'] - prev['low']
        body_ratio = body_size_prev / total_range if total_range > 0 else 0
        
        # 入场信号：趋势向上 + 实体增大 + 价格动量 + 实体占比合理
        if (trend_up and 
            body_size_prev > body_size_prev2 and 
            body_size_prev > body_size_prev3 and
            price_momentum and
            0.3 <= body_ratio <= 0.8):  # 实体占比在30%-80%之间
            return True
            
        return False
    except Exception as e:
        logger.error(f"判断入场信号K线失败: {str(e)}")
        return False

def is_exit_signal(df, index):
    """
    判断是否是出场信号K线
    参数:
        df: DataFrame, 包含OHLCV数据的DataFrame
        index: int, 当前K线的索引
    返回:
        bool: 是否是出场信号K线
    """
    try:
        if index < 1:  # 需要至少2根K线来判断
            return False
            
        prev = df.iloc[index-1]  # Kn-1（信号K线）
        prev2 = df.iloc[index-2]  # Kn-2
        
        # 确认下跌趋势：连续两根K线收盘价递减
        trend_down = (prev['close'] < prev2['close'])
        
        # 出场信号：趋势向下
        if trend_down:
            return True
            
        return False
    except Exception as e:
        logger.error(f"判断出场信号K线失败: {str(e)}")
        return False

def get_backtest_data(start_time, end_time):
    """获取回测数据"""
    try:
        # TODO: 实现数据获取逻辑
        # 这里应该实现从数据源获取历史数据的逻辑
        # 示例数据结构
        data = {
            'timestamp': [],
            'open': [],
            'high': [],
            'low': [],
            'close': [],
            'volume': []
        }
        
        # 将数据转换为DataFrame
        df = pd.DataFrame(data)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df.set_index('timestamp', inplace=True)
        
        return df
    except Exception as e:
        logger.error(f"获取回测数据失败: {str(e)}")
        return None

def execute_backtest(data):
    """执行回测"""
    try:
        trades = []
        position = None
        entry_price = None
        
        for i in range(3, len(data)):  # 从第4根K线开始，因为需要至少3根K线判断趋势
            current_candle = data.iloc[i]  # Kn
            signal_candle = data.iloc[i-1]  # Kn-1
            
            # 如果没有持仓，检查入场信号
            if position is None:
                if is_entry_signal(data, i):
                    # 使用Kn的开盘价作为执行价格
                    execution_price = current_candle['open']
                    position = 'long'
                    entry_price = execution_price
                    trades.append({
                        'type': 'entry',
                        'timestamp': data.index[i],
                        'price': execution_price,
                        'position': position,
                        'signal_candle': {  # Kn-1的信息
                            'open': signal_candle['open'],
                            'high': signal_candle['high'],
                            'low': signal_candle['low'],
                            'close': signal_candle['close'],
                            'time': data.index[i-1].strftime('%H:%M:%S')
                        },
                        'execution_candle': {  # Kn的信息
                            'open': current_candle['open'],
                            'high': current_candle['high'],
                            'low': current_candle['low'],
                            'close': current_candle['close'],
                            'time': data.index[i].strftime('%H:%M:%S')
                        }
                    })
                    logger.info(f"开仓信号: 信号时间={data.index[i-1]}, 执行时间={data.index[i]}, 价格={execution_price}")
            
            # 如果已有持仓，检查出场信号
            elif position == 'long':
                if is_exit_signal(data, i):
                    # 使用Kn的开盘价作为执行价格
                    execution_price = current_candle['open']
                    position = None
                    exit_price = execution_price
                    profit = exit_price - entry_price
                    trades.append({
                        'type': 'exit',
                        'timestamp': data.index[i],
                        'price': execution_price,
                        'profit': profit,
                        'signal_candle': {  # Kn-1的信息
                            'open': signal_candle['open'],
                            'high': signal_candle['high'],
                            'low': signal_candle['low'],
                            'close': signal_candle['close'],
                            'time': data.index[i-1].strftime('%H:%M:%S')
                        },
                        'execution_candle': {  # Kn的信息
                            'open': current_candle['open'],
                            'high': current_candle['high'],
                            'low': current_candle['low'],
                            'close': current_candle['close'],
                            'time': data.index[i].strftime('%H:%M:%S')
                        }
                    })
                    logger.info(f"平仓信号: 信号时间={data.index[i-1]}, 执行时间={data.index[i]}, 价格={execution_price}, 盈亏={profit}")
        
        return {
            'trades': trades,
            'performance': calculate_performance(trades)
        }
    except Exception as e:
        logger.error(f"执行回测失败: {str(e)}")
        return None

def calculate_performance(trades):
    """计算回测表现"""
    try:
        if not trades:
            return {
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'win_rate': 0,
                'total_profit': 0,
                'average_profit': 0
            }
            
        total_trades = len([t for t in trades if t['type'] == 'exit'])
        profits = [t['profit'] for t in trades if t['type'] == 'exit']
        winning_trades = len([p for p in profits if p > 0])
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': total_trades - winning_trades,
            'win_rate': winning_trades / total_trades if total_trades > 0 else 0,
            'total_profit': sum(profits),
            'average_profit': sum(profits) / len(profits) if profits else 0
        }
    except Exception as e:
        logger.error(f"计算回测表现失败: {str(e)}")
        return None

def analyze_results(results):
    """分析回测结果"""
    try:
        # TODO: 实现结果分析逻辑
        # 这里应该实现回测结果的分析
        return {
            'metrics': {},
            'summary': {}
        }
    except Exception as e:
        logger.error(f"分析结果失败: {str(e)}")
        return None

def save_results(results, analysis):
    """保存回测结果"""
    try:
        # 创建结果目录
        os.makedirs('results', exist_ok=True)
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'results/backtest_{timestamp}.json'
        
        # 保存结果
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump({
                'results': results,
                'analysis': analysis,
                'timestamp': timestamp
            }, f, ensure_ascii=False, indent=2)
            
        logger.info(f"结果已保存到: {filename}")
    except Exception as e:
        logger.error(f"保存结果失败: {str(e)}")

def run_backtest():
    """执行回测"""
    try:
        # 检查是否在整点时间
        current_time = datetime.now()
        if current_time.minute != 0:
            logger.info(f"当前时间 {current_time.strftime('%H:%M:%S')} 不是整点，跳过回测")
            return

        logger.info("开始执行回测...")
        
        # 获取当前时间作为回测结束时间
        end_time = datetime.now()
        # 回测开始时间设置为24小时前
        start_time = end_time - timedelta(days=1)
        
        # 获取回测数据
        backtest_data = get_backtest_data(start_time, end_time)
        if not backtest_data:
            logger.error("获取回测数据失败")
            return
            
        # 执行回测
        results = execute_backtest(backtest_data)
        if not results:
            logger.error("回测执行失败")
            return
            
        # 分析回测结果
        analysis = analyze_results(results)
        if not analysis:
            logger.error("回测结果分析失败")
            return
            
        # 保存回测结果
        save_results(results, analysis)
        
        logger.info("回测完成")
        
    except Exception as e:
        logger.error(f"回测执行出错: {str(e)}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    logger.info("回测程序启动")
    
    while True:
        try:
            # 检查是否在整点时间
            current_time = datetime.now()
            if current_time.minute == 0:
                run_backtest()
            
            # 等待1分钟
            time.sleep(60)
            
        except KeyboardInterrupt:
            logger.info("程序被用户中断")
            break
        except Exception as e:
            logger.error(f"程序运行出错: {str(e)}")
            logger.error(traceback.format_exc())
            time.sleep(60)  # 发生错误时等待1分钟后继续 