import json
import os

def load_logs(log_file):
    with open(log_file, 'r', encoding='utf-8') as f:
        content = f.read()
    logs = []
    for idx, block in enumerate(content.strip().split('\n\n')):
        if block.strip():
            try:
                logs.append(json.loads(block))
            except json.JSONDecodeError as e:
                print(f"警告：第{idx+1}条日志不是合法JSON，已跳过。错误信息: {e}\n内容片段: {block[:100]}...")
    return logs

def get_kline_times(kline_data):
    # 支持list或dict结构
    if isinstance(kline_data, dict):
        kline_data = kline_data.get('kline', [])
    return [item['time'] for item in kline_data if 'time' in item]

def check_signal_future_leak(log):
    kline_data = log.get('K线数据-响应') or log.get('K线数据-请求')
    if not kline_data:
        return []
    kline_times = get_kline_times(kline_data)
    if not kline_times:
        return []
    max_time = max(kline_times)
    min_time = min(kline_times)
    results = []
    signals = log.get('信号数据', {})
    for signal_type in ['buySignals', 'sellSignals']:
        for sig in signals.get(signal_type, []):
            sig_time = sig.get('time')
            # 检查信号是否出现在K线时间范围之外
            if sig_time is None:
                continue
            if sig_time > max_time:
                results.append(f"{signal_type} 信号 {sig} 用到了未来K线（信号时间大于K线最大时间）")
            if sig_time < min_time:
                results.append(f"{signal_type} 信号 {sig} 用到了历史区间之外的K线（信号时间小于K线最小时间）")
            # 检查信号是否有used_kline_end字段
            if 'used_kline_end' in sig and sig['used_kline_end'] > sig_time:
                results.append(f"{signal_type} 信号 {sig} 用到了未来数据（used_kline_end > 信号时间）")
    return results

def main():
    log_file = '信号.txt'
    if not os.path.exists(log_file):
        print(f"未找到日志文件 {log_file}")
        return
    logs = load_logs(log_file)
    total_leaks = 0
    for idx, log in enumerate(logs):
        leaks = check_signal_future_leak(log)
        if leaks:
            print(f"\n日志第{idx+1}条发现未来数据干扰：")
            for leak in leaks:
                print(leak)
            total_leaks += len(leaks)
    if total_leaks == 0:
        print("未检测到未来数据干扰问题。")
    else:
        print(f"\n共检测到{total_leaks}处未来数据干扰问题，请检查信号生成逻辑！")

if __name__ == '__main__':
    main() 