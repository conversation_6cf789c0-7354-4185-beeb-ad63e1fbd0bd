<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会员升级海报</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .poster {
            width: 100%;
            aspect-ratio: 2/1;
            max-width: 800px;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            position: relative;
            display: flex;
            overflow: hidden;
        }
        
        /* 左侧：主视觉区 */
        .left-section {
            flex: 1;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px;
            position: relative;
        }
        
        .big-number {
            font-size: 180px;
            font-weight: 900;
            color: #ffffff;
            line-height: 0.8;
            text-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        
        .main-text {
            margin-top: 20px;
            text-align: center;
        }
        
        .title {
            font-size: 42px;
            font-weight: 800;
            color: #ffffff;
            margin-bottom: 15px;
            letter-spacing: 0.1em;
        }
        
        .subtitle {
            font-size: 32px;
            color: #ffffff;
            font-weight: 600;
            line-height: 1.3;
        }
        
        .limit-text {
            color: #ffeb3b;
            font-weight: 800;
            font-size: 36px;
            text-shadow: 0 2px 8px rgba(0,0,0,0.3);
        }
        
        /* 右侧：权益区 */
        .right-section {
            flex: 1;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 30px 25px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .benefits-title {
            font-size: 36px;
            font-weight: 800;
            color: #2e7d32;
            margin-bottom: 35px;
            text-align: center;
        }
        
        .benefits-list {
            width: 100%;
            max-width: 300px;
        }
        
        .benefit-item {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            margin-bottom: 30px;
            padding: 0;
        }
        
        .benefit-item:last-child {
            margin-bottom: 0;
        }
        
        .benefit-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4caf50, #2e7d32);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 25px;
            flex-shrink: 0;
        }
        
        .benefit-icon-text {
            color: #ffffff;
            font-size: 28px;
            font-weight: 700;
        }
        
        .benefit-text {
            font-size: 36px;
            font-weight: 700;
            color: #2e7d32;
            line-height: 1.1;
        }
        
        /* 响应式调整 */
        @media (max-width: 800px) {
            .big-number {
                font-size: 120px;
            }
            .title {
                font-size: 34px;
            }
            .subtitle {
                font-size: 26px;
            }
            .limit-text {
                font-size: 30px;
            }
            .benefits-title {
                font-size: 30px;
            }
            .benefit-text {
                font-size: 30px;
            }
            .benefit-icon {
                width: 50px;
                height: 50px;
            }
            .benefit-icon-text {
                font-size: 24px;
            }
            .right-section {
                padding: 25px 20px;
            }
        }
        
        @media (max-width: 600px) {
            .big-number {
                font-size: 100px;
            }
            .title {
                font-size: 28px;
            }
            .subtitle {
                font-size: 22px;
            }
            .limit-text {
                font-size: 26px;
            }
            .benefits-title {
                font-size: 26px;
            }
            .benefit-text {
                font-size: 26px;
            }
            .benefit-icon {
                width: 45px;
                height: 45px;
            }
            .benefit-icon-text {
                font-size: 20px;
            }
            .right-section {
                padding: 20px 15px;
            }
            .left-section {
                padding: 15px;
            }
        }
    </style>
</head>

<body style="margin: 0; padding: 20px; background: #f5f5f5; display: flex; align-items: center; justify-content: center; min-height: 100vh;">
    
    <div class="poster">
        
        <!-- 左侧主视觉 -->
        <div class="left-section">
            <div class="big-number">3</div>
            <div class="main-text">
                <div class="title">解锁会员</div>
                <div class="subtitle">
                    免费用户<br><span class="limit-text">可保存3个</span>地块
                </div>
            </div>
        </div>
        
        <!-- 右侧权益 -->
        <div class="right-section">
            <div class="benefits-title">会员特权</div>
            
            <div class="benefits-list">
                <div class="benefit-item">
                    <div class="benefit-icon">
                        <div class="benefit-icon-text">∞</div>
                    </div>
                    <div class="benefit-text">无限存储</div>
                </div>
                
                <div class="benefit-item">
                    <div class="benefit-icon">
                        <div class="benefit-icon-text">✓</div>
                    </div>
                    <div class="benefit-text">零广告体验</div>
                </div>
            </div>
        </div>
        
    </div>
    
</body>

</html>
