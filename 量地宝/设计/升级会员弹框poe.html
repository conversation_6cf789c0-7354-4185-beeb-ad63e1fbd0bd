<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测亩易会员升级海报</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@400;600;700;900&family=PingFang+SC:wght@400;600;700;900&display=swap');

        .poster-container {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'PingFang SC', sans-serif;
            background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
            position: relative;
            overflow: hidden;
            aspect-ratio: 16/9;
            width: 100%;
            max-width: 800px;
            height: 450px;
            border-radius: 0;
            box-shadow:
                0 0 0 1px rgba(0, 0, 0, 0.04),
                0 2px 4px rgba(0, 0, 0, 0.02),
                0 8px 24px rgba(0, 0, 0, 0.06);
        }

        .poster-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(0, 122, 255, 0.02) 0%, transparent 70%);
            pointer-events: none;
        }

        .warning-banner {
            background: linear-gradient(90deg, #ff3b30 0%, #ff6347 100%);
            position: relative;
            overflow: hidden;
            height: 28%;
            border-radius: 0;
        }

        .warning-banner::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
        }

        .warning-icon {
            width: 3.5rem;
            height: 3.5rem;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow:
                0 0 0 1px rgba(0, 0, 0, 0.04),
                0 1px 3px rgba(0, 0, 0, 0.12),
                0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .main-title-section {
            height: 40%;
            background: linear-gradient(180deg, transparent 0%, rgba(0, 122, 255, 0.01) 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 16px 20px;
        }

        .main-title {
            font-size: 5.2rem;
            font-weight: 700;
            background: linear-gradient(180deg, #1d1d1f 0%, #424245 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
            line-height: 1;
            letter-spacing: -0.02em;
            margin-bottom: 16px;
        }

        .title-underline {
            width: 90px;
            height: 3px;
            background: linear-gradient(90deg, transparent 0%, #007aff 50%, transparent 100%);
        }

        .benefits-section {
            height: 32%;
            padding: 0 30px 20px 30px;
        }

        .benefit-item {
            background: transparent;
            border: none;
            border-radius: 0;
            padding: 0;
            box-shadow: none;
            position: relative;
            overflow: hidden;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .check-icon {
            width: 2.8rem;
            height: 2.8rem;
            background: linear-gradient(135deg, #34c759 0%, #30d158 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: none;
            flex-shrink: 0;
        }

        .benefit-text {
            font-size: 2.8rem;
            font-weight: 600;
            color: #1d1d1f;
            line-height: 1.2;
            letter-spacing: -0.01em;
        }

        .warning-text {
            font-size: 3.4rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.95);
            letter-spacing: -0.01em;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        @media (max-width: 640px) {
            .poster-container {
                max-width: 600px;
                height: 337px;
            }

            .main-title {
                font-size: 4rem;
                margin-bottom: 12px;
            }

            .benefit-text {
                font-size: 2.2rem;
            }

            .warning-text {
                font-size: 2.8rem;
            }

            .warning-icon {
                width: 3rem;
                height: 3rem;
            }

            .check-icon {
                width: 2.4rem;
                height: 2.4rem;
            }

            .main-title-section {
                padding: 12px 16px;
            }

            .benefits-section {
                padding: 0 24px 16px 24px;
            }
        }

        @media (max-width: 480px) {
            .poster-container {
                max-width: 400px;
                height: 225px;
            }

            .main-title {
                font-size: 3.2rem;
                margin-bottom: 8px;
            }

            .benefit-text {
                font-size: 1.8rem;
            }

            .warning-text {
                font-size: 2.2rem;
            }

            .warning-icon {
                width: 2.6rem;
                height: 2.6rem;
            }

            .check-icon {
                width: 2rem;
                height: 2rem;
            }

            .benefits-section {
                padding: 0 20px 12px 20px;
            }

            .main-title-section {
                padding: 8px 12px;
            }
        }

        @media (max-width: 360px) {
            .poster-container {
                max-width: 320px;
                height: 180px;
            }

            .main-title {
                font-size: 2.6rem;
                margin-bottom: 6px;
            }

            .benefit-text {
                font-size: 1.5rem;
            }

            .warning-text {
                font-size: 1.8rem;
            }

            .warning-icon {
                width: 2.2rem;
                height: 2.2rem;
            }

            .check-icon {
                width: 1.8rem;
                height: 1.8rem;
            }

            .title-underline {
                width: 70px;
                height: 2px;
            }
        }
    </style>
</head>

<body class="bg-gray-50 p-4">
    <div class="flex justify-center items-center min-h-screen">
        <div class="poster-container flex flex-col relative">

            <!-- 顶部警告横幅 -->
            <div class="warning-banner flex items-center justify-center px-6 relative z-10">
                <div class="warning-icon mr-5">
                    <svg class="w-9 h-9 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd"
                            d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                            clip-rule="evenodd"></path>
                    </svg>
                </div>
                <span class="warning-text">免费用户可保存10个地块</span>
            </div>

            <!-- 中部主标题区域 -->
            <div class="main-title-section">
                <h1 class="main-title text-center">解锁会员</h1>
                <div class="title-underline"></div>
            </div>

            <!-- 底部权益区域 -->
            <div class="benefits-section grid grid-cols-2">

                <!-- 无限地块 -->
                <div class="benefit-item">
                    <div class="check-icon mr-4">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                            stroke-width="3">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div class="benefit-text">无限地块</div>
                </div>

                <!-- 无广告烦扰 -->
                <div class="benefit-item">
                    <div class="check-icon mr-4">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                            stroke-width="3">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div class="benefit-text">无广告烦扰</div>
                </div>

            </div>

        </div>
    </div>
</body>

</html>