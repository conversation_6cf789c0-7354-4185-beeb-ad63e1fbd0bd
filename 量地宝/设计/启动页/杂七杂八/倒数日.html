<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>41天倒数日 | 加油冲刺</title>
    
    <!-- TailwindCSS 3.0+ -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Noto+Sans+SC:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Inter', 'Noto Sans SC', sans-serif;
        }
        
        body {
            width: 360px;
            height: 640px;
            margin: 0 auto;
            padding: 0;
        }
        
        .tesla-red {
            color: #E31937;
        }
        
        .tesla-red-bg {
            background-color: #E31937;
        }
        
        .tesla-red-gradient {
            background: linear-gradient(135deg, rgba(227, 25, 55, 0.9) 0%, rgba(227, 25, 55, 0.3) 100%);
        }
        
        .poster-card {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(227, 25, 55, 0.2);
            backdrop-filter: blur(20px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .number-display {
            font-size: clamp(6rem, 15vw, 12rem);
            font-weight: 900;
            line-height: 0.8;
            background: linear-gradient(135deg, #E31937 0%, #ff4757 50%, #E31937 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(227, 25, 55, 0.3);
        }
        
        .motivational-text {
            background: linear-gradient(135deg, #ffffff 0%, #E31937 50%, #ffffff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .floating {
            /* 移除动画 */
        }
        
        .pulse-ring {
            /* 移除动画 */
        }
        
        .countdown-enter {
            /* 移除动画 */
        }
    </style>
</head>
<body class="bg-black text-white min-h-screen flex items-center justify-center overflow-hidden">
    
    <!-- Background Effects -->
    <div class="absolute inset-0">
        <div class="absolute top-1/4 left-1/4 w-64 h-64 tesla-red-gradient rounded-full opacity-20 blur-3xl"></div>
        <div class="absolute bottom-1/4 right-1/4 w-96 h-96 tesla-red-gradient rounded-full opacity-10 blur-3xl"></div>
    </div>
    
    <!-- Main Poster -->
    <div class="relative z-10 w-full h-full p-3">
        <div class="poster-card rounded-3xl p-6 text-center relative overflow-hidden h-full flex flex-col justify-between">
            
            <!-- Pulse Ring Effect -->
            <div class="absolute inset-0 border-2 border-red-500 rounded-3xl pulse-ring"></div>
            
            <!-- Header -->
            <div>
                <div class="mb-3">
                    <img src="images/小鸟游六花.jpg" alt="小鸟游六花" class="w-24 h-24 rounded-full mx-auto object-contain border-2 border-red-500 bg-white">
                </div>
                <h1 class="text-lg font-bold text-gray-300">COUNTDOWN JOURNEY</h1>
                <div class="w-16 h-1 tesla-red-bg mx-auto rounded-full mt-2"></div>
            </div>
            
            <!-- Main Number -->
            <div class="flex-1 flex flex-col justify-center">
                <div class="number-display" id="mainNumber">41</div>
                <h2 class="text-4xl font-black mb-2 tesla-red">天</h2>
                <p class="text-base text-gray-400 uppercase tracking-widest">DAYS TO GO</p>
            </div>
            
            <!-- Bottom Content -->
            <div>
                <!-- Motivational Quote -->
                <div class="mb-4 space-y-2">
                    <h3 class="text-2xl font-black motivational-text">
                        支楞起来！
                    </h3>
                    <p class="text-lg font-bold tesla-red">
                        冲刺这41天！
                    </p>
                </div>
                
                <!-- Date Range -->
                <div class="mb-4 text-center">
                    <div class="text-sm text-gray-400">
                        📅 2025.5.27 - 2025.7.8
                    </div>
                </div>
                
                <!-- Progress Bar -->
                <div class="mb-4">
                    <div class="flex justify-between items-center mb-1">
                        <span class="text-xs text-gray-400">进度</span>
                        <span class="text-xs tesla-red font-bold">2.3%</span>
                    </div>
                    <div class="w-full bg-gray-800 rounded-full h-1.5">
                        <div class="tesla-red-bg h-1.5 rounded-full transition-all duration-2000 ease-out" id="progressBar" style="width: 0%"></div>
                    </div>
                </div>
                
                <!-- Footer Message -->
                <div class="border-t border-gray-700 pt-3">
                    <div class="flex items-center justify-center space-x-2 mb-1">
                        <span class="text-lg">⭐</span>
                        <span class="text-sm font-semibold">坚持就是胜利</span>
                        <span class="text-lg">🏆</span>
                    </div>
                    <div class="text-xs text-gray-400">
                        ✨ 每天都要支楞起来！✨
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 直接设置数字和进度，无动画
            const mainNumber = document.getElementById('mainNumber');
            const progressBar = document.getElementById('progressBar');
            
            mainNumber.textContent = 41;
            progressBar.style.width = '2.3%';
        });
    </script>
</body>
</html>
