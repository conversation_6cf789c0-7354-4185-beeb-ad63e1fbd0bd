<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>测亩易宣传页</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    html, body {
      width: 100%;
      height: 100%;
      font-family: "PingFang SC", "Helvetica Neue", sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #006432;
    }
    
    .container {
      width: 100%;
      max-width: 1200px;
      position: relative;
      aspect-ratio: 4/3;
      overflow: hidden;
      box-shadow: 0 10px 40px rgba(0,0,0,0.3);
    }
    
    .bg-image {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      z-index: 0;
      filter: brightness(1.05) contrast(1.1) saturate(1.2);
    }
    
    .overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to bottom, rgba(0, 80, 48, 0.1) 0%, rgba(0, 100, 60, 0.6) 100%);
      z-index: 1;
    }
    
    .content {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      z-index: 2;
      padding: 50px 60px 0;
    }
    
    .header-section {
      text-align: center;
      margin-bottom: 40px;
      width: 100%;
    }
    
    .title {
      font-size: 5.5rem;
      font-weight: 900;
      color: white;
      letter-spacing: 4px;
      text-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);
      margin-bottom: 15px;
      display: inline-block;
    }
    
    .subtitle {
      font-size: 2.2rem;
      color: white;
      letter-spacing: 2px;
      text-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
      padding-bottom: 10px;
      position: relative;
      display: inline-block;
    }
    
    .subtitle::after {
      content: '';
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: 0;
      width: 180px;
      height: 3px;
      background: linear-gradient(to right, #34D399, transparent 80%);
    }
    
    .features-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      flex-grow: 1;
      justify-content: center;
      margin-top: -40px;
      padding-bottom: 80px;
      width: 100%;
    }
    
    .features {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 25px 35px;
      max-width: 800px;
      width: 85%;
    }
    
    .feature {
      background: rgba(0, 80, 48, 0.8);
      border-radius: 50px;
      padding: 18px 15px;
      text-align: center;
      font-size: 1.6rem;
      font-weight: 600;
      color: white;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      backdrop-filter: blur(3px);
      border: 1px solid rgba(52, 211, 153, 0.4);
    }
    
    .highlight {
      position: absolute;
      bottom: 90px;
      right: 60px;
      font-size: 2rem;
      font-weight: bold;
      color: #34D399;
      text-align: center;
      background: rgba(0, 80, 48, 0.8);
      padding: 15px 35px;
      border-radius: 50px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      backdrop-filter: blur(3px);
      border: 1px solid rgba(52, 211, 153, 0.4);
      white-space: nowrap;
    }
    
    .footer {
      background: rgba(0, 60, 40, 0.9);
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 15px 0;
      width: 100%;
      border-top: 1px solid rgba(52, 211, 153, 0.3);
      backdrop-filter: blur(3px);
      position: absolute;
      bottom: 0;
      left: 0;
      z-index: 3;
    }
    
    .logo {
      width: 40px;
      height: 40px;
      margin-right: 12px;
      border-radius: 8px;
      object-fit: cover;
    }
    
    .company {
      color: white;
      font-size: 1.1rem;
      letter-spacing: 1px;
      font-weight: 500;
    }
    
    @media (max-width: 1200px) {
      .content {
        padding: 40px 50px 0;
      }
      .title {
        font-size: 4.5rem;
      }
      .subtitle {
        font-size: 2rem;
      }
      .features {
        max-width: 700px;
        gap: 20px 30px;
      }
      .feature {
        font-size: 1.4rem;
        padding: 15px 10px;
      }
      .highlight {
        font-size: 1.8rem;
        padding: 12px 30px;
        bottom: 80px;
        right: 50px;
      }
    }

    @media (max-width: 992px) {
      .features-container {
        margin-top: -30px;
        padding-bottom: 70px;
      }
      .features {
        grid-template-columns: repeat(2, 1fr);
        max-width: 550px;
        width: 90%;
        gap: 20px 25px;
      }
      .highlight {
         bottom: 70px;
         right: 40px;
         font-size: 1.6rem;
      }
    }
    
    @media (max-width: 768px) {
      .container {
        aspect-ratio: auto;
        height: 100vh;
        max-width: 500px;
      }
      .content {
        padding: 30px 40px 0;
      }
      .title {
        font-size: 3.5rem;
      }
      .subtitle {
        font-size: 1.6rem;
      }
       .features-container {
        margin-top: -20px;
        padding-bottom: 60px;
      }
      .features {
        grid-template-columns: 1fr;
        max-width: 300px;
        width: 90%;
        gap: 15px;
      }
      .feature {
        font-size: 1.4rem;
      }
      .highlight {
        position: relative;
        bottom: auto;
        right: auto;
        margin-top: 20px;
        align-self: center;
        font-size: 1.5rem;
        padding: 10px 25px;
      }
       .footer {
         padding: 12px 0;
       }
    }
  </style>
</head>
<body>
  <div class="container">
    <img class="bg-image" src="backgroud.jpg" alt="农田背景">
    <div class="overlay"></div>
    
    <div class="content">
      <div class="header-section">
        <h1 class="title">测亩易</h1>
        <p class="subtitle">数字农服新起点</p>
      </div>
      
      <div class="features-container">
        <div class="features">
          <div class="feature">测亩量地</div>
          <div class="feature">农资电商</div>
          <div class="feature">农机咨询</div>
          <div class="feature">信息发布</div>
          <div class="feature">作业管理</div>
          <div class="feature">智能顾问</div>
        </div>
      </div>
      
      <div class="highlight">统统只要一个APP！</div>
      
      <div class="footer">
        <img class="logo" src="logo.jpg" alt="logo">
        <div class="company">易联农业荣誉出品</div>
      </div>
    </div>
  </div>
</body>
</html>