<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>测亩易宣传页</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    html, body {
      width: 100%;
      height: 100%;
      font-family: "PingFang SC", "Helvetica Neue", sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #005030;
    }
    
    .container {
      width: 100%;
      max-width: 500px;
      position: relative;
      background: rgba(0, 60, 40, 0.65);
      border-radius: 0;
      padding: 22px 20px 15px;
      box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
      border: 1px solid rgba(0, 255, 150, 0.3);
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 10px;
      overflow: hidden;
      aspect-ratio: 3 / 4;
    }
    
    .bg-image {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      opacity: 0.6;
      filter: brightness(1.1) contrast(1.1) saturate(1.2);
      z-index: 0;
      border-radius: 0;
    }
    
    .grid-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image: linear-gradient(0deg, rgba(0,255,150,0.03) 1px, transparent 1px),
                        linear-gradient(90deg, rgba(0,255,150,0.03) 1px, transparent 1px);
      background-size: 40px 40px;
      background-position: center center;
      opacity: 0.5;
      border-radius: 0;
      z-index: 0;
    }
    
    .content {
      position: relative;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      z-index: 1;
    }
    
    .header {
      text-align: center;
      margin-bottom: 28px;
      position: relative;
      width: 100%;
    }
    
    .header::after {
      content: '';
      position: absolute;
      bottom: -12px;
      left: 50%;
      transform: translateX(-50%);
      width: 140px;
      height: 2px;
      background: rgba(255, 255, 255, 0.7);
    }
    
    .title {
      font-size: 4.8rem;
      font-weight: 800;
      color: white;
      margin-bottom: 20px;
      letter-spacing: 2.5px;
      position: relative;
      display: inline-block;
      margin-top: 8px;
    }
    
    .title::before,
    .title::after {
      display: none;
    }
    
    .subtitle {
      font-size: 1.9rem;
      color: white;
      font-weight: 500;
      letter-spacing: 1.5px;
      position: relative;
      display: inline-block;
      padding: 6px 12px;
      margin-top: 3px;
    }
    
    .features {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 18px;
      width: 100%;
      margin-bottom: 32px;
    }
    
    .feature {
      background: rgba(0, 40, 25, 0.6);
      border: 1px solid rgba(0, 255, 150, 0.25);
      border-radius: 20px;
      padding: 16px 10px;
      text-align: center;
      font-size: 1.3rem;
      font-weight: 600;
      color: white;
    }
    
    .highlight {
      font-size: 1.7rem;
      font-weight: bold;
      color: #00FF96;
      margin-bottom: 28px;
      text-align: center;
      background: rgba(0, 40, 25, 0.6);
      padding: 14px 28px;
      border-radius: 28px;
      border: 1px solid rgba(0, 255, 150, 0.25);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      text-shadow: 0 2px 8px rgba(0, 0, 0, 0.6);
    }
    
    .footer {
      width: 100%;
      /* margin-top: auto; */
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(0, 40, 25, 0.7); /* 设置为70%不透明度 */
      padding: 10px 20px;
      border-top: 1px solid rgba(0, 255, 150, 0.2);
      flex-shrink: 0;
      position: relative;
      z-index: 2;
    }
    
    .logo {
      width: 42px;
      height: 42px;
      margin-right: 12px;
      border-radius: 9px;
      object-fit: cover;
    }
    
    .company {
      color: #FFFFFF;
      font-size: 1.1rem;
      letter-spacing: 0.8px;
      font-weight: 500;
    }
    
    @media (max-width: 768px) {
      .container {
        padding: 30px 20px 60px;
      }
      
      .title {
        font-size: 4.5rem;
        margin-bottom: 20px;
      }
      
      .subtitle {
        font-size: 1.8rem;
        padding: 8px 20px;
      }
      
      .features {
        gap: 15px;
      }
      
      .feature {
        padding: 15px 10px;
        font-size: 1.2rem;
      }
      
      .highlight {
        font-size: 1.6rem;
        padding: 15px 30px;
      }
    }
    
    @media (max-width: 480px) {
      .title {
        font-size: 3.8rem;
        margin-bottom: 15px;
      }
      
      .subtitle {
        font-size: 1.5rem;
        padding: 6px 15px;
      }
      
      .feature {
        font-size: 1.1rem;
      }
      
      .highlight {
        font-size: 1.4rem;
        padding: 12px 25px;
      }
      
      .footer {
        padding: 12px 20px;
      }
      
      .logo {
        width: 40px;
        height: 40px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <img class="bg-image" src="backgroud.jpg" alt="农田背景">
    <div class="grid-overlay"></div>
    
    <div class="content">
      <div class="header">
        <h1 class="title">测亩易</h1>
        <p class="subtitle">数字农服新起点</p>
      </div>
      
      <div class="features">
        <div class="feature">测亩量地</div>
        <div class="feature">农资电商</div>
        <div class="feature">农机咨询</div>
        <div class="feature">信息发布</div>
        <div class="feature">作业管理</div>
        <div class="feature">智能顾问</div>
      </div>
      
      <div class="highlight">统统只要一个APP！</div>
    </div>
    
    <div class="footer">
      <img class="logo" src="logo.jpg" alt="logo">
      <div class="company">易联农业荣誉出品</div>
    </div>
  </div>
</body>
</html>