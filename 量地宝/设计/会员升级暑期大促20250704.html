<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量地宝 - 会员升级</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Framer Motion -->
    <script src="https://unpkg.com/framer-motion@11.0.0/dist/framer-motion.js"></script>
    
    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    

    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#6366f1',
                        secondary: '#f59e0b',
                        accent: '#ec4899',
                        neutral: '#6b7280',
                        surface: '#f8fafc',
                        'surface-2': '#f1f5f9',
                        'surface-3': '#e2e8f0'
                    },
                    animation: {
                        'fade-in-up': 'fadeInUp 0.8s ease-out',
                        'fade-in-down': 'fadeInDown 0.8s ease-out',
                        'slide-in-left': 'slideInLeft 0.8s ease-out',
                        'slide-in-right': 'slideInRight 0.8s ease-out',
                        'scale-in': 'scaleIn 0.6s ease-out',
                        'float': 'float 6s ease-in-out infinite'
                    }
                }
            }
        }
    </script>
    
    <style>
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.8);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .bento-card {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .bento-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .glow-effect {
            position: relative;
            overflow: hidden;
        }
        
        .glow-effect::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(99, 102, 241, 0.1), transparent);
            animation: rotate 8s linear infinite;
        }
        
        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .gradient-text {
            background: linear-gradient(135deg, #6366f1, #ec4899);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .price-highlight {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            font-weight: 900;
            font-size: clamp(2.5rem, 8vw, 4rem);
            line-height: 0.9;
            letter-spacing: -0.02em;
        }
        
        .micro-interaction {
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .micro-interaction:hover {
            transform: scale(1.05);
        }
        
        .micro-interaction:active {
            transform: scale(0.95);
        }
        
        
        
        .scroll-indicator {
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            z-index: 1000;
        }
        
        .feature-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(99, 102, 241, 0.05));
            border: 1px solid rgba(99, 102, 241, 0.2);
        }
    </style>
</head>

<body class="bg-gradient-to-br from-slate-50 via-white to-slate-100 min-h-screen">
    <!-- 页面容器 - 模拟手机屏幕 -->
    <div class="max-w-sm mx-auto bg-white rounded-3xl shadow-2xl overflow-hidden relative">
        <!-- 状态栏 -->
        <div class="h-11 bg-white flex justify-between items-center px-5 text-sm font-semibold text-slate-800 animate-fade-in-down">
            <span>9:41</span>
            <span>100%</span>
        </div>

        <!-- 导航栏 -->
        <div class="h-14 bg-white/90 backdrop-blur-md flex items-center justify-center border-b border-slate-100 animate-fade-in-down">
            <div class="text-lg font-bold text-slate-900">我的</div>
        </div>

        <!-- 会员升级 Bento Grid -->
        <div class="p-4 space-y-3">
                                      <!-- 主卡片 - 会员升级 -->
             <div class="bento-card rounded-2xl p-3 glow-effect animate-fade-in-up" style="animation-delay: 0.1s; aspect-ratio: 10/7;">
                 <div class="relative z-10 h-full flex flex-col">
                     <!-- 头部 -->
                     <div class="flex items-center justify-between mb-2">
                         <div class="flex items-center space-x-1.5">
                             <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20 flex items-center justify-center">
                                 <i class="fas fa-crown text-primary text-sm"></i>
                             </div>
                             <div>
                                 <h3 class="font-bold text-slate-900 text-sm">量地宝会员</h3>
                             </div>
                         </div>
                         <button class="bg-gradient-to-r from-secondary to-orange-500 text-white px-2.5 py-1 rounded-lg font-bold text-xs micro-interaction shadow-lg hover:shadow-xl">
                             立即升级
                         </button>
                     </div>

                     <!-- 促销标签 -->
                     <div class="inline-flex items-center space-x-1 bg-gradient-to-r from-orange-500/20 to-red-500/20 border border-orange-500/30 rounded-full px-2.5 py-1 mb-2 animate-slide-in-left w-fit" style="animation-delay: 0.2s;">
                         <i class="fas fa-fire text-orange-500 text-xs"></i>
                         <span class="font-bold text-slate-900 text-xs">暑期助农公益大促</span>
                     </div>

                     <!-- 价格展示 -->
                     <div class="mb-2 animate-scale-in" style="animation-delay: 0.3s;">
                         <div class="flex items-baseline space-x-1">
                             <span class="text-3xl font-black gradient-text">¥9.9</span>
                             <span class="text-slate-600 font-semibold text-xs">/年</span>
                             <span class="text-slate-400 line-through text-sm">¥68</span>
                                                           <span class="text-xs text-slate-500 ml-1">立省 58 元</span>
                         </div>
                     </div>

                     <!-- 权益卡片网格 - 极简版 -->
                     <div class="grid grid-cols-2 gap-2 flex-1 min-h-0">
                         <!-- 无广告 -->
                         <div class="bento-card rounded-lg p-2 text-center animate-slide-in-left flex flex-col justify-center" style="animation-delay: 0.4s;">
                             <div class="w-6 h-6 rounded-lg bg-gradient-to-br from-green-500/10 to-green-500/5 border border-green-500/20 flex items-center justify-center mx-auto mb-1">
                                 <i class="fas fa-shield-alt text-green-500 text-xs"></i>
                             </div>
                             <h4 class="font-bold text-slate-900 text-xs mb-0.5">无广告</h4>
                             <p class="text-slate-500 text-xs">纯净体验</p>
                             <!-- 微型图表 -->
                             <div class="mt-1 h-2 bg-gradient-to-r from-green-100 to-emerald-50 rounded-sm flex items-center justify-center">
                                 <div class="w-full h-0.5 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full"></div>
                             </div>
                         </div>

                         <!-- 无限存储 -->
                         <div class="bento-card rounded-lg p-2 text-center animate-slide-in-right flex flex-col justify-center" style="animation-delay: 0.5s;">
                             <div class="w-6 h-6 rounded-lg bg-gradient-to-br from-blue-500/10 to-blue-500/5 border border-blue-500/20 flex items-center justify-center mx-auto mb-1">
                                 <i class="fas fa-database text-blue-500 text-xs"></i>
                             </div>
                             <h4 class="font-bold text-slate-900 text-xs mb-0.5">无限存储</h4>
                             <p class="text-slate-500 text-xs">地块随意存</p>
                             <!-- 存储进度示意 -->
                             <div class="mt-1 h-2 bg-gradient-to-r from-blue-100 to-cyan-50 rounded-sm flex items-center px-1">
                                 <div class="flex space-x-0.5">
                                     <div class="w-1 h-1 bg-blue-400 rounded-full"></div>
                                     <div class="w-1 h-1 bg-cyan-400 rounded-full"></div>
                                     <div class="w-1 h-1 bg-blue-300 rounded-full"></div>
                                 </div>
                                 <span class="ml-auto text-xs text-slate-400">∞</span>
                             </div>
                         </div>
                     </div>
                 </div>
             </div>

            
        </div>

        <!-- 页面其他内容 -->
        <div class="p-4 space-y-3">
            <!-- 模拟其他菜单项 -->
            <div class="bento-card rounded-xl p-4 flex items-center space-x-3 animate-fade-in-up" style="animation-delay: 0.7s;">
                <div class="feature-icon">
                    <i class="fas fa-user text-slate-600"></i>
                </div>
                <span class="text-slate-700 font-medium">个人信息</span>
                <i class="fas fa-chevron-right text-slate-400 ml-auto"></i>
            </div>

            <div class="bento-card rounded-xl p-4 flex items-center space-x-3 animate-fade-in-up" style="animation-delay: 0.8s;">
                <div class="feature-icon">
                    <i class="fas fa-map-marked-alt text-slate-600"></i>
                </div>
                <span class="text-slate-700 font-medium">我的地块</span>
                <i class="fas fa-chevron-right text-slate-400 ml-auto"></i>
            </div>

            <div class="bento-card rounded-xl p-4 flex items-center space-x-3 animate-fade-in-up" style="animation-delay: 0.9s;">
                <div class="feature-icon">
                    <i class="fas fa-cog text-slate-600"></i>
                </div>
                <span class="text-slate-700 font-medium">设置</span>
                <i class="fas fa-chevron-right text-slate-400 ml-auto"></i>
            </div>

            <div class="bento-card rounded-xl p-4 flex items-center space-x-3 animate-fade-in-up" style="animation-delay: 1.0s;">
                <div class="feature-icon">
                    <i class="fas fa-question-circle text-slate-600"></i>
                </div>
                <span class="text-slate-700 font-medium">帮助与反馈</span>
                <i class="fas fa-chevron-right text-slate-400 ml-auto"></i>
            </div>
        </div>
    </div>

    <!-- 滚动指示器 -->
    <div class="scroll-indicator">
        <div class="w-1 h-20 bg-gradient-to-b from-primary/20 to-primary rounded-full">
            <div class="w-1 h-4 bg-primary rounded-full animate-float"></div>
        </div>
    </div>

    <script>
        

        // 滚动动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in-up');
                }
            });
        }, observerOptions);

        // 观察所有动画元素
        document.querySelectorAll('.bento-card').forEach(el => {
            observer.observe(el);
        });

        // 微交互效果
        document.querySelectorAll('.micro-interaction').forEach(el => {
            el.addEventListener('mouseenter', () => {
                el.style.transform = 'scale(1.05)';
            });
            
            el.addEventListener('mouseleave', () => {
                el.style.transform = 'scale(1)';
            });
            
            el.addEventListener('mousedown', () => {
                el.style.transform = 'scale(0.95)';
            });
            
            el.addEventListener('mouseup', () => {
                el.style.transform = 'scale(1.05)';
            });
        });

        // 滚动视差效果
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('.glow-effect');
            const speed = scrolled * 0.5;
            
            if (parallax) {
                parallax.style.transform = `translateY(${speed}px)`;
            }
        });
    </script>
</body>

</html>
