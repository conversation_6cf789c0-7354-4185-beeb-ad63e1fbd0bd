<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测亩易会员续费提醒</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@400;600;700;900&family=PingFang+SC:wght@400;600;700;900&display=swap');

        .poster-container {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'PingFang SC', sans-serif;
            background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
            position: relative;
            overflow: hidden;
            aspect-ratio: 16/9;
            width: 100%;
            max-width: 800px;
            height: 450px;
            border-radius: 0;
            box-shadow:
                0 0 0 1px rgba(0, 0, 0, 0.04),
                0 2px 4px rgba(0, 0, 0, 0.02),
                0 8px 24px rgba(0, 0, 0, 0.06);
        }

        .poster-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(0, 122, 255, 0.02) 0%, transparent 70%);
            pointer-events: none;
        }

        .warning-banner {
            background: linear-gradient(90deg, #ff9500 0%, #ffb347 100%);
            position: relative;
            overflow: hidden;
            height: 30%;
            border-radius: 0;
        }

        .warning-banner::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
        }

        .warning-icon {
            width: 3.2rem;
            height: 3.2rem;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow:
                0 0 0 1px rgba(0, 0, 0, 0.04),
                0 1px 3px rgba(0, 0, 0, 0.12),
                0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .main-title-section {
            height: 38%;
            background: linear-gradient(180deg, transparent 0%, rgba(0, 122, 255, 0.01) 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 16px 20px;
        }

        .main-title {
            font-size: 5.2rem;
            font-weight: 700;
            background: linear-gradient(180deg, #1d1d1f 0%, #424245 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
            line-height: 1;
            letter-spacing: -0.02em;
            margin-bottom: 16px;
        }

        .title-underline {
            width: 90px;
            height: 3px;
            background: linear-gradient(90deg, transparent 0%, #ff9500 50%, transparent 100%);
        }

        .benefits-section {
            height: 32%;
            padding: 0 30px 20px 30px;
        }

        .benefit-item {
            background: transparent;
            border: none;
            border-radius: 0;
            padding: 0;
            box-shadow: none;
            position: relative;
            overflow: hidden;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .benefit-icon {
            font-size: 3.5rem;
            margin-right: 1rem;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .benefit-text {
            font-size: 2.8rem;
            font-weight: 600;
            color: #1d1d1f;
            line-height: 1.2;
            letter-spacing: -0.01em;
        }

        .warning-text {
            font-size: 3rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.95);
            letter-spacing: -0.01em;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            white-space: nowrap;
        }

        @media (max-width: 640px) {
            .poster-container {
                max-width: 600px;
                height: 337px;
            }

            .main-title {
                font-size: 4rem;
                margin-bottom: 12px;
            }

            .benefit-text {
                font-size: 2.2rem;
            }

            .benefit-icon {
                font-size: 2.8rem;
                margin-right: 0.8rem;
            }

            .warning-text {
                font-size: 2.4rem;
            }

            .warning-icon {
                width: 2.8rem;
                height: 2.8rem;
            }

            .main-title-section {
                padding: 12px 16px;
            }

            .benefits-section {
                padding: 0 24px 16px 24px;
            }
        }

        @media (max-width: 480px) {
            .poster-container {
                max-width: 400px;
                height: 225px;
            }

            .main-title {
                font-size: 3.2rem;
                margin-bottom: 8px;
            }

            .benefit-text {
                font-size: 1.8rem;
            }

            .benefit-icon {
                font-size: 2.4rem;
                margin-right: 0.6rem;
            }

            .warning-text {
                font-size: 2rem;
            }

            .warning-icon {
                width: 2.4rem;
                height: 2.4rem;
            }

            .benefits-section {
                padding: 0 20px 12px 20px;
            }

            .main-title-section {
                padding: 8px 12px;
            }
        }

        @media (max-width: 360px) {
            .poster-container {
                max-width: 320px;
                height: 180px;
            }

            .main-title {
                font-size: 2.6rem;
                margin-bottom: 6px;
            }

            .benefit-text {
                font-size: 1.5rem;
            }

            .benefit-icon {
                font-size: 2rem;
                margin-right: 0.5rem;
            }

            .warning-text {
                font-size: 1.6rem;
            }

            .warning-icon {
                width: 2rem;
                height: 2rem;
            }

            .title-underline {
                width: 70px;
                height: 2px;
            }
        }
    </style>
</head>

<body class="bg-gray-50 p-4">
    <div class="flex justify-center items-center min-h-screen">
        <div class="poster-container flex flex-col relative">

            <!-- 顶部警告横幅 -->
            <div class="warning-banner flex items-center justify-center px-4 relative z-10">
                <div class="warning-icon mr-4">
                    <svg class="w-8 h-8 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                            clip-rule="evenodd"></path>
                    </svg>
                </div>
                <span class="warning-text">会员已过期，仅可查看10个地块</span>
            </div>

            <!-- 中部主标题区域 -->
            <div class="main-title-section">
                <h1 class="main-title text-center">续费会员</h1>
                <div class="title-underline"></div>
            </div>

            <!-- 底部权益区域 -->
            <div class="benefits-section grid grid-cols-2">

                <!-- 查看所有地块 -->
                <div class="benefit-item">
                    <div class="benefit-icon">👀</div>
                    <div class="benefit-text">查看所有地块</div>
                </div>

                <!-- 继续享受特权 -->
                <div class="benefit-item">
                    <div class="benefit-icon">✨</div>
                    <div class="benefit-text">继续享受特权</div>
                </div>

            </div>

        </div>
    </div>
</body>

</html>