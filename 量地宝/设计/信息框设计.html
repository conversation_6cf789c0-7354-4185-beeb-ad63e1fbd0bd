<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>信息框设计</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .info-box {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: rgba(0, 80, 40, 0.85);
            border-radius: 12px;
            padding: 10px 15px;
            color: white;
            max-width: 100%;
            width: 360px;
            margin: 20px auto;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .info-item {
            display: flex;
            align-items: center;
            margin: 0 5px;
        }
        
        /* 新的垂直布局样式 */
        .info-item-vertical {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin: 0 4px;
            height: 50px;
            min-width: 40px;
        }
        
        .icon {
            width: 22px;
            height: 22px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .icon img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        
        /* 垂直布局中的图标 */
        .icon-vertical {
            width: 26px;
            height: 26px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 3px;
        }
        
        .icon-vertical img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        
        .text {
            font-size: 14px;
            white-space: nowrap;
        }
        
        /* 垂直布局中的文本 */
        .text-vertical {
            font-size: 11px;
            white-space: nowrap;
            text-align: center;
        }
        
        /* 为长度数值专门设置样式 */
        .length-value {
            font-size: 14px;
            font-weight: 500;
            margin-top: -2px;
        }
        
        /* 统一文本样式 */
        .text-consistent {
            font-size: 14px;
            font-weight: 500;
            margin-top: -2px;
        }
        
        .divider {
            width: 1px;
            height: 36px;
            background-color: rgba(255, 255, 255, 0.3);
            margin: 0 4px;
        }
        
        /* 为无文字图标添加提示标签 */
        .info-item-no-text {
            position: relative;
            height: 50px;
            display: flex;
            align-items: center;
            margin: 0 3px;
        }
        
        .info-item-no-text:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 10;
        }
        
        /* 每个图标都有不同的背景色调 */
        .icon-bg-nav {
            background-color: rgba(255, 255, 255, 0.15);
            border-radius: 50%;
            padding: 6px;
            width: 28px;
            height: 28px;
        }
        
        .icon-bg-delete {
            background-color: rgba(255, 255, 255, 0.15);
            border-radius: 50%;
            padding: 6px;
            width: 28px;
            height: 28px;
        }
        
        /* 移动端适配 */
        @media (max-width: 480px) {
            .info-box {
                width: 95%;
                padding: 8px 12px;
            }
            
            .info-item-vertical {
                margin: 0 2px;
                min-width: 36px;
            }
            
            .divider {
                margin: 0 2px;
            }
        }
        
        /* 橘黄色背景的信息框 */
        .info-box-orange {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: rgba(88,167,60, 0.85);
            border-radius: 12px;
            padding: 10px 15px;
            color: white;
            max-width: 100%;
            width: 360px;
            margin: 20px auto;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <div class="info-box">
        <!-- 长度信息 - 垂直布局 -->
        <div class="info-item-vertical">
            <div class="icon-vertical">
                <img src="图标/长度.png" alt="长度">
            </div>
            <div class="text-vertical length-value">202505米</div>
        </div>
        
        <div class="divider"></div>
        
        <!-- 编辑 - 垂直布局 -->
        <div class="info-item-vertical">
            <div class="icon-vertical">
                <img src="图标/修改.png" alt="编辑">
            </div>
            <div class="text-vertical text-consistent">编辑</div>
        </div>
        
        <div class="divider"></div>
        
        <!-- 详情 - 垂直布局 -->
        <div class="info-item-vertical">
            <div class="icon-vertical">
                <img src="图标/详情.png" alt="详情">
            </div>
            <div class="text-vertical text-consistent">详情</div>
        </div>
        
        <div class="divider"></div>
        
        <!-- 导航 - 无文字图标 -->
        <div class="info-item-no-text" data-tooltip="导航">
            <div class="icon icon-bg-nav">
                <img src="图标/导航.png" alt="导航">
            </div>
        </div>
        
        <div class="divider"></div>
        
        <!-- 删除 - 无文字图标 -->
        <div class="info-item-no-text" data-tooltip="删除">
            <div class="icon icon-bg-delete">
                <img src="图标/删除.png" alt="删除">
            </div>
        </div>
    </div>

    <h3 style="text-align: center; margin-top: 40px; margin-bottom: 20px;">对比版本</h3>

    <div class="info-box-orange">
        <!-- 长度信息 - 垂直布局 -->
        <div class="info-item-vertical">
            <div class="icon-vertical">
                <img src="图标/长度.png" alt="长度">
            </div>
            <div class="text-vertical length-value">202505米</div>
        </div>
        
        <div class="divider"></div>
        
        <!-- 编辑 - 垂直布局 -->
        <div class="info-item-vertical">
            <div class="icon-vertical">
                <img src="图标/修改.png" alt="编辑">
            </div>
            <div class="text-vertical text-consistent">编辑</div>
        </div>
        
        <div class="divider"></div>
        
        <!-- 详情 - 垂直布局 -->
        <div class="info-item-vertical">
            <div class="icon-vertical">
                <img src="图标/详情.png" alt="详情">
            </div>
            <div class="text-vertical text-consistent">详情</div>
        </div>
        
        <div class="divider"></div>
        
        <!-- 导航 - 无文字图标 -->
        <div class="info-item-no-text" data-tooltip="导航">
            <div class="icon icon-bg-nav">
                <img src="图标/导航.png" alt="导航">
            </div>
        </div>
        
        <div class="divider"></div>
        
        <!-- 删除 - 无文字图标 -->
        <div class="info-item-no-text" data-tooltip="删除">
            <div class="icon icon-bg-delete">
                <img src="图标/删除.png" alt="删除">
            </div>
        </div>
    </div>

    <div style="text-align: center; margin-top: 30px;">
        <p>您可以修改颜色、图标和样式以适应您的应用设计</p>
    </div>
</body>
</html>
