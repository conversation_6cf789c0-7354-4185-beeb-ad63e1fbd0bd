<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会员权益对比</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
            background: linear-gradient(135deg, #fafbfc 0%, #f1f3f4 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            margin: 0;
        }

        .container {
            width: 100vw;
            height: 100vh;
            aspect-ratio: 9/16;
            max-width: 56.25vh;
            max-height: 177.78vw;
            display: flex;
            flex-direction: column;
            padding: 30px 20px;
        }

                 /* 标题 */
         .hero-title {
             text-align: left;
             font-size: 22px;
             font-weight: 700;
             color: #1d1d1f;
             margin-bottom: 6px;
             letter-spacing: -0.3px;
             line-height: 1.1;
             margin-left: 8px;
         }

        /* 微尘：副标题 */
        .hero-subtitle {
            text-align: left;
            font-size: 14px;
            font-weight: 400;
            color: #8e8e93;
            letter-spacing: 0.3px;
            margin-bottom: 24px;
            margin-left: 8px;
            opacity: 0.8;
        }

                 /* Bento Grid 容器 */
         .bento-grid {
             flex: 1;
             display: flex;
             flex-direction: column;
         }

                 /* 信息盒子 */
         .info-box {
             background: rgba(255, 255, 255, 0.7);
             border-radius: 32px;
             padding: 40px;
             backdrop-filter: blur(80px) saturate(180%);
             border: 1px solid rgba(255, 255, 255, 0.3);
             box-shadow: 
                 0 40px 80px rgba(0, 0, 0, 0.06),
                 0 16px 32px rgba(0, 0, 0, 0.04),
                 0 8px 16px rgba(0, 0, 0, 0.02),
                 inset 0 2px 0 rgba(255, 255, 255, 0.9),
                 inset 0 -1px 0 rgba(0, 0, 0, 0.02);
             position: relative;
             overflow: hidden;
         }

                 .info-box::before {
             content: '';
             position: absolute;
             top: 0;
             left: 0;
             right: 0;
             height: 1px;
             background: linear-gradient(90deg, transparent, rgba(0, 122, 255, 0.6), transparent);
         }

                 /* 功能区域 */
         .feature-section {
             margin-bottom: 28px;
         }
         
         .feature-section:last-child {
             margin-bottom: 0;
         }

         /* 分隔线 */
         .section-divider {
             height: 1px;
             background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.06), transparent);
             margin: 24px 0;
         }

         /* 功能标题 */
         .feature-title {
             display: flex;
             align-items: center;
             gap: 12px;
             margin-bottom: 24px;
         }

                 .feature-icon {
             width: 32px;
             height: 32px;
             border-radius: 10px;
             display: flex;
             align-items: center;
             justify-content: center;
             font-size: 16px;
             background: rgba(0, 122, 255, 0.1);
             color: #007AFF;
         }

         .feature-name {
             font-size: 16px;
             font-weight: 600;
             color: #1d1d1f;
             letter-spacing: -0.3px;
         }

        /* 对比区域 */
        .comparison-row {
            display: flex;
            gap: 12px;
        }

        .plan-card {
            flex: 1;
            padding: 20px 16px;
            border-radius: 16px;
            text-align: center;
            position: relative;
            transition: all 0.3s ease;
        }

                 /* 免费版卡片 */
         .free-card {
             background: rgba(255, 255, 255, 0.4);
             border: 1px solid rgba(0, 0, 0, 0.04);
             box-shadow: 
                 0 8px 16px rgba(0, 0, 0, 0.02),
                 0 4px 8px rgba(0, 0, 0, 0.01),
                 inset 0 1px 0 rgba(255, 255, 255, 0.6);
             position: relative;
             transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
         }

         .free-card::after {
             content: '免费版';
             position: absolute;
             top: 8px;
             right: 8px;
             background: rgba(142, 142, 147, 0.08);
             color: #8e8e93;
             border: 0.5px solid rgba(142, 142, 147, 0.2);
             padding: 3px 8px;
             border-radius: 8px;
             font-size: 10px;
             font-weight: 500;
             letter-spacing: 0.3px;
             backdrop-filter: blur(20px);
         }

         /* 会员版卡片 - 光晕效果 */
         .premium-card {
             background: rgba(255, 255, 255, 0.6);
             border: 1px solid rgba(0, 122, 255, 0.08);
             box-shadow: 
                 0 8px 16px rgba(0, 122, 255, 0.04),
                 0 4px 8px rgba(0, 122, 255, 0.02),
                 inset 0 1px 0 rgba(255, 255, 255, 0.8),
                 inset 0 0 0 1px rgba(0, 122, 255, 0.02);
             position: relative;
             transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
         }

                 .premium-card::before {
             content: '';
             position: absolute;
             top: -1px;
             left: -1px;
             right: -1px;
             bottom: -1px;
             background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 149, 0, 0.12));
             border-radius: 17px;
             z-index: -1;
             filter: blur(3px);
         }

         .premium-card::after {
             content: '会员版';
             position: absolute;
             top: 8px;
             right: 8px;
             background: rgba(0, 122, 255, 0.08);
             color: #007AFF;
             border: 0.5px solid rgba(0, 122, 255, 0.2);
             padding: 3px 8px;
             border-radius: 8px;
             font-size: 10px;
             font-weight: 500;
             letter-spacing: 0.3px;
             backdrop-filter: blur(20px);
         }

        /* 状态图标 */
        .status-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 6px;
            font-size: 16px;
        }

                 .status-negative {
             background: rgba(255, 59, 48, 0.1);
             color: #FF3B30;
         }

         .status-positive {
             background: rgba(52, 199, 89, 0.1);
             color: #34C759;
         }

                 /* 状态文字 */
         .status-text {
             font-size: 12px;
             font-weight: 600;
             margin-bottom: 4px;
         }

                 .status-negative .status-text {
             color: #1d1d1f;
         }

         .status-positive .status-text {
             color: #1d1d1f;
         }

                 /* 描述文字 - 微尘 */
         .status-desc {
             font-size: 10px;
             color: #8E8E93;
             font-weight: 400;
             opacity: 0.8;
         }

                 /* 数值强调 - 巨物概念 */
         .value-highlight {
             font-size: 20px;
             font-weight: 800;
             line-height: 1;
             margin-bottom: 6px;
         }

                 .value-highlight.negative {
             color: #FF3B30;
         }

         .value-highlight.positive {
             color: #34C759;
         }

                 /* 响应式调整 */
         @media (max-width: 768px) {
             .container {
                 padding: 20px 15px;
             }

             .info-box {
                 padding: 20px;
             }

             .plan-card {
                 padding: 16px 12px;
             }
         }

        /* 微动画 */
        .info-box {
            animation: fadeInUp 0.6s ease-out;
        }

        .info-box:nth-child(2) {
            animation-delay: 0.1s;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- 巨物：主标题 -->
        <h1 class="hero-title">会员权益</h1>

        <!-- 统一信息盒子 -->
        <div class="bento-grid">
            <div class="info-box">
                <!-- 广告体验部分 -->
                <div class="feature-section">
                    <div class="feature-title">
                        <div class="feature-icon">📢</div>
                        <div class="feature-name">广告体验</div>
                    </div>

                    <div class="comparison-row">
                        <div class="plan-card free-card">
                            <div class="status-icon status-negative">✕</div>
                            <div class="status-text">有广告干扰</div>
                            <div class="status-desc">开屏 + 弹窗广告</div>
                        </div>

                        <div class="plan-card premium-card">
                            <div class="status-icon status-positive">✓</div>
                            <div class="status-text">纯净无广告</div>
                            <div class="status-desc">专注测亩体验</div>
                        </div>
                    </div>
                </div>

                <!-- 分隔线 -->
                <div class="section-divider"></div>

                <!-- 地块保存部分 -->
                <div class="feature-section">
                    <div class="feature-title">
                        <div class="feature-icon">💾</div>
                        <div class="feature-name">地块保存</div>
                    </div>

                    <div class="comparison-row">
                        <div class="plan-card free-card">
                            <div class="value-highlight negative">3</div>
                            <div class="status-text">仅限3个</div>
                            <div class="status-desc">数量限制</div>
                        </div>

                        <div class="plan-card premium-card">
                            <div class="value-highlight positive">∞</div>
                            <div class="status-text">无限保存</div>
                            <div class="status-desc">想存就存</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>