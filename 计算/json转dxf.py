import ezdxf, json, math, time, os

def lonlat_to_web_mercator(lon, lat):
    R = 6378137.0
    x = R * math.radians(lon)
    y = R * math.log(math.tan((math.pi/4) + (math.radians(lat)/2)))
    return x, y

def load_geojson(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        geojson = json.load(f)
    polygons = []
    for feature in geojson.get('features', []):
        geometry = feature.get('geometry', {})
        if geometry.get('type') == 'Polygon':
            coords = geometry.get('coordinates', [])[0]
            polygons.append(coords)
    return polygons

def create_dxf_without_labels(polygons, save_dir):
    all_projected_points = []
    projected_polygons = []

    for points in polygons:
        projected = [lonlat_to_web_mercator(lon, lat) for lon, lat in points]
        projected_polygons.append(projected)
        all_projected_points.extend(projected)

    if not all_projected_points:
        print("❌ 没有任何地块数据，退出。")
        return

    min_x = min(p[0] for p in all_projected_points)
    max_x = max(p[0] for p in all_projected_points)
    min_y = min(p[1] for p in all_projected_points)
    max_y = max(p[1] for p in all_projected_points)

    center_x = (min_x + max_x) / 2
    center_y = (min_y + max_y) / 2

    doc = ezdxf.new(setup=True, dxfversion="AC1021")
    msp = doc.modelspace()

    for points in projected_polygons:
        moved_points = [(x - center_x, y - center_y) for x, y in points]
        if moved_points[0] != moved_points[-1]:
            moved_points.append(moved_points[0])
        msp.add_lwpolyline(moved_points, close=True)

    timestamp = time.strftime("%Y%m%d_%H%M%S")
    filename = f"polygons_only_{timestamp}.dxf"
    save_path = os.path.join(save_dir, filename)
    doc.saveas(save_path)
    print(f"✅ 只包含地块轮廓的DXF已生成: {save_path}")

if __name__ == "__main__":
    input_file = r"F:\Python\计算\converted_wgs84.geojson"
    output_dir = "."
    polygons = load_geojson(input_file)
    create_dxf_without_labels(polygons, output_dir)
