def find_integer_solutions_final(c: float):
    """
    查找方程 4.99x + 68y + 58z = c 的唯一整数解 (x, y, z)，
    其中 c 是给定的大于0的数值。
    """
    # 初始化解列表
    solutions = []

    # 确定 y 和 z 的最大可能值
    max_value_y = int(c // 68)
    max_value_z = int(c // 58)

    # 遍历 y 和 z 的所有可能值
    for y in range(max_value_y + 1):
        for z in range(max_value_z + 1):
            # 计算临时值 temp
            temp = c - 68 * y - 58 * z

            # 计算 x，并检查是否为整数
            x = temp / 4.99
            if abs(x - round(x)) < 1e-9:
                solutions.append((int(round(x)), y, z))

    # 返回解列表
    return solutions


# 使用示例
c = 155.92
solutions = find_integer_solutions_final(c)
print(f"给定 c = {c}, 找到的解: {solutions}")
