import json
import math

# 坐标转换函数（照你的版本）
def _transformlat(lng, lat):
    ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * math.sqrt(abs(lng))
    ret += (20.0 * math.sin(6.0 * lng * math.pi) + 20.0 * math.sin(2.0 * lng * math.pi)) * 2.0 / 3.0
    ret += (20.0 * math.sin(lat * math.pi) + 40.0 * math.sin(lat / 3.0 * math.pi)) * 2.0 / 3.0
    ret += (160.0 * math.sin(lat / 12.0 * math.pi) + 320.0 * math.sin(lat * math.pi / 30.0)) * 2.0 / 3.0
    return ret

def _transformlng(lng, lat):
    ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * math.sqrt(abs(lng))
    ret += (20.0 * math.sin(6.0 * lng * math.pi) + 20.0 * math.sin(2.0 * lng * math.pi)) * 2.0 / 3.0
    ret += (20.0 * math.sin(lng * math.pi) + 40.0 * math.sin(lng / 3.0 * math.pi)) * 2.0 / 3.0
    ret += (150.0 * math.sin(lng / 12.0 * math.pi) + 300.0 * math.sin(lng / 30.0 * math.pi)) * 2.0 / 3.0
    return ret

def gcj02_to_wgs84(lng, lat):
    a = 6378245.0
    ee = 0.00669342162296594323
    pi = math.pi
    if not (-180 <= lng <= 180 and -90 <= lat <= 90):
        return lng, lat
    dlat = _transformlat(lng - 105.0, lat - 35.0)
    dlng = _transformlng(lng - 105.0, lat - 35.0)
    radlat = lat / 180.0 * pi
    magic = math.sin(radlat)
    magic = 1 - ee * magic * magic
    if magic < 0:
        magic = 0
    sqrtmagic = math.sqrt(magic)
    if sqrtmagic == 0:
        return lng, lat
    dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * pi)
    dlng = (dlng * 180.0) / (a / sqrtmagic * math.cos(radlat) * pi)
    mglat = lat + dlat
    mglng = lng + dlng
    return lng * 2 - mglng, lat * 2 - mglat

# === 真正修正后的主程序 ===
def convert_gcj02_json_to_geojson(input_file, output_file):
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    features = []
    for item in data:
        points_str = item.get("points")
        if points_str:
            raw_points = json.loads(points_str)
            # 🔥核心改动：每个点转换后，保存成新坐标
            corrected_coords = []
            for p in raw_points:
                lng_gcj, lat_gcj = p['longitude'], p['latitude']
                lng_wgs84, lat_wgs84 = gcj02_to_wgs84(lng_gcj, lat_gcj)
                corrected_coords.append([lng_wgs84, lat_wgs84])  # ⚡用新坐标
            # 保证闭合
            if corrected_coords[0] != corrected_coords[-1]:
                corrected_coords.append(corrected_coords[0])

            feature = {
                "type": "Feature",
                "properties": {
                    "measure_area": item.get("measure_area", 0)
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [corrected_coords]
                }
            }
            features.append(feature)

    geojson = {
        "type": "FeatureCollection",
        "features": features
    }

    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(geojson, f, ensure_ascii=False, indent=2)

    print(f"✅ 成功输出转换后的WGS84标准GeoJSON: {output_file}")

# === 主程序入口 ===
if __name__ == "__main__":
    input_file = "计算/plots_20250610_27.json"
    output_file = "计算/plots_20250610_27_converted_wgs84.geojson"
    convert_gcj02_json_to_geojson(input_file, output_file)
