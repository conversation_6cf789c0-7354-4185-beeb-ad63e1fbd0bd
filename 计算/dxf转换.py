import ezdxf
import json
import time
import os


def load_geojson(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def create_dxf_from_geojson(geojson_data, save_dir):
    doc = ezdxf.new()
    msp = doc.modelspace()

    features = geojson_data.get('features', [])
    for feature in features:
        geometry = feature.get('geometry', {})
        if geometry.get('type') == 'Polygon':
            # GeoJSON Polygon的坐标：[环1, 环2, ...]，我们取第一个环（一般是地块外圈）
            coords = geometry.get('coordinates', [])
            if coords:
                points = [(lng, lat) for lng, lat in coords[0]]
                if points[0] != points[-1]:
                    points.append(points[0])  # 确保闭合
                msp.add_lwpolyline(points, close=True)

    timestamp = time.strftime("%Y%m%d_%H%M%S")
    filename = f"geo_boundaries_{timestamp}.dxf"
    save_path = os.path.join(save_dir, filename)

    doc.saveas(save_path)
    print(f"✅ 成功生成DXF: {save_path}")


if __name__ == "__main__":
    input_file = "boundaries.json"
    output_dir = "."

    geojson_data = load_geojson(input_file)
    create_dxf_from_geojson(geojson_data, output_dir)
