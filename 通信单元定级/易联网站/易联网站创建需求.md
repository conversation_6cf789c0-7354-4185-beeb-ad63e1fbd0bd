# 联农创世官网创建需求文档 (V13.0 - 实际实现同步版)

**版本：** 13.0 (实际实现同步版，基于V12.0更新)
**日期：** 2025/06-27
**文档目的：** 采用多层级数字序号格式，提供与实际网页完全同步的官网建设指导文件

---

## 1. 项目概述与核心理念

### 1.1 项目定位
- **核心定位：** 简洁、高端、自信的科技实力展示窗口
- **目标用户：** 农业生产组织、农机制造商
- **核心价值：** 展示数字农业专业实力与成功案例

### 1.2 设计理念与完整要求

#### 1.2.1 核心设计理念
- **设计风格：** Bento Grid风格，色彩柔和、高级
- **视觉基调：** "Apple"式科技范儿
- **视觉焦点：** 超大字体与小元素的强烈对比
- **版式风格：** 中英文混用，中文大字体粗体，英文小字作为点缀

#### 1.2.2 完整设计风格要求
1. **Bento Grid风格：** 使用Bento Grid风格的视觉设计，色彩搭配要柔和
2. **超大视觉元素：** 强调超大字体或数字突出核心要点，画面中有超大视觉元素强调重点，与小元素的比例形成反差
3. **中英文混用：** 中文大字体粗体，英文小字作为点缀
4. **图形化元素：** 简洁的勾线图形化作为数据可视化或者配图元素
5. **高亮色渐变：** 运用高亮色自身透明度渐变制造科技感，但是不同高亮色不要互相渐变
6. **Apple式动效：** 模仿Apple官网的动效，向下滚动鼠标配合动效
7. **数据可视化：** 数据可以引用在线的图表组件，样式需要跟主题一致
8. **Framer Motion：** 使用Framer Motion (通过CDN引入)
9. **技术栈：** 使用HTML5、TailwindCSS 3.0+(通过CDN引入)和必要的JavaScript
10. **专业图标：** 使用专业图标库如FontAwesome或Material Icons(通过CDN引入)
11. **避免Emoji：** 避免使用emoji作为主要图标
12. **内容完整性：** 不要省略内容要点

### 1.3 设计约束
- **图形元素：** 采用简洁的勾线图形，**严禁使用Emoji**
- **动效标准：** 模仿Apple官网，实现流畅、有意义的滚动动效
- **开发原则：** **简洁至上**，服务于"一天内完成"的核心目标
- **内容完整性：** **不省略**以下所有定义的核心内容

### 1.4 技术规范
- **技术栈：** HTML5、TailwindCSS 3.0+、Framer Motion、JavaScript
- **资源引入：** 全部通过CDN引入
- **兼容性：** 现代浏览器优先，响应式设计

---

## 2. 网站架构与导航设计

### 2.1 整体架构
- **架构方案：** "1+1"极简方案
  - `index.html` (首页)：网站的绝对核心和"名片"
  - `solutions.html` (解决方案页)：网站的"技术规格书"

### 2.2 导航栏设计
- **设计风格：** 简洁的顶部固定导航栏
- **Logo设计：** 使用图片logo `pics/易联农业logo.png`（亿联农业）
- **导航项目：**
  - Logo图片 (点击返回首页)
  - `解决方案` (链接到 solutions.html)
  - `联系我们` (按钮样式, 点击平滑滚动至页脚)

---

## 3. 首页内容设计 (index.html)

### 3.1 Hero区域设计
#### 3.1.1 公司标识
- **公司名称：** `联农创世（北京）农业科技有限公司`
- **展示方式：** 使用hero-company-name类，大字体权重800，响应式设计

#### 3.1.2 核心定位
- **主标题：** `数字农业专家`
- **英文副标题：** `Digital Agriculture Expert`
- **字体规格：** 使用hero-title类，超大字体突出，渐变色彩

#### 3.1.3 企业认证展示
- **认证类型：** 
  - `国家高新企业`
  - `专精特新企业`
- **展示形式：** 大图标+文字组合，使用highlight-gradient背景，Font Awesome图标

#### 3.1.4 认证证书
- **证书图片：** 
  - `高新证书.jpg`
  - `专精特新证书.png`
- **展示要求：** 使用certificate-img类，支持hover放大效果

### 3.2 公司基本信息展示
#### 3.2.1 成立时间展示
- **展示内容：** `2017` (使用big-number类显示)
- **标题：** `成立年份`
- **描述：** `专注数字农业8年深耕`
- **英文：** `8 Years Focused on Digital Agriculture`
- **展示方式：** Bento卡片，highlight-gradient背景

#### 3.2.2 服务客户展示
- **标题：** `服务客户`
- **客户类型：**
  - `农业生产组织` (Agricultural Organizations)
  - `农机制造商` (Machinery Manufacturers)
- **展示方式：** 双图标并列，大尺寸Font Awesome图标
- **卡片样式：** success-gradient背景

### 3.3 核心技术能力展示
#### 3.3.1 技术能力概述
- **标题：** `核心技术能力`
- **英文：** `Core Technology Capabilities`
- **展示方式：** 6个技术卡片，**单行全宽布局**，每个技术独占一行

#### 3.3.2 技术卡片详情
**实际布局：** 采用单行全宽展示，每个技术能力独占一行，便于展示详细内容和配图

1. **大模型AI技术**
   - 描述：`智能农业问答与决策支持`
   - 英文：`AI-Powered Agricultural Intelligence`
   - **实际展示：** 3个AI应用场景截图
     - `病虫害识别(1).png` - 病虫害识别AI界面
     - `农机ai.jpg` - 农机专家AI界面  
     - `种植专家ai.png` - 种植专家AI界面
   - **布局特点：** 3列网格展示不同AI应用场景，每个都有hover效果

2. **物联网系统**
   - 描述：`设备监控与数据采集`
   - 英文：`IoT Monitoring & Data Collection`
   - **实际展示：** 完整物联网解决方案图
     - `物联网系统.png` - 展示APP界面与硬件设备的完整解决方案
   - **布局特点：** 全宽图片展示，突出系统完整性

3. **农田地块识别**
   - 描述：`地块边界精准识别与分析`
   - 英文：`Agricultural Field Recognition`
   - **实际展示：** 识别前后对比图
     - `农田识别前.png` - 原始农田图像
     - `农田识别后.png` - 识别完成后的地块边界图
   - **布局特点：** 2列对比展示，突出识别效果

4. **数字农业软件开发**
   - 描述：`8年行业深度理解与实践`
   - 英文：`Digital Agriculture Software`
   - **实际展示：** 5个APP界面截图
     - `农机监控.jpg` - 农机监控界面
     - `地块列表.png` - 地块管理界面
     - `作业统计.jpg` - 作业统计分析界面
     - `作业单.jpg` - 作业单管理界面
     - `农机详情.jpg` - 农机设备详情界面
   - **布局特点：** 5列网格展示，每个APP界面都有标签说明

5. **基于农机轨迹的智能算法**
   - 描述：`轨迹分析与智能优化`
   - 英文：`Trajectory-Based Intelligence`
   - **实际展示：** 轨迹识别前后对比图
     - `轨迹识别1.png` - 原始农机轨迹
     - `轨迹识别2.png` - 识别后生成的地块
   - **布局特点：** 2列对比展示，展示从轨迹到地块的转换过程

6. **移动应用开发**
   - 描述：`跨平台农业管理应用`
   - 英文：`Cross-Platform Mobile Apps`
   - **实际展示：** 与数字农业软件开发合并展示

### 3.4 成功案例展示
#### 3.4.1 e联农机共享平台
- **案例标题：** `e联农机共享平台`
- **英文副标题：** `IoT Agricultural Machinery Sharing Platform`
- **配图要求：** `pics/e联农机介绍页.png`
- **展示方式：** 满屏宽度图片 + 6个特色亮点
- **核心亮点：**
  - `中国首家物联网农机共享服务平台`
  - `以秸秆收储为核心，依托意大利D630型圆捆机产品`
  - `物联网系统实时监控：机器定位、作业效率、工作时间、机手排名`
  - `创新主动式服务：2小时响应，定期巡视，专用配件箱`
  - `全产业链模式：连接制造企业、平台、机主、收储企业`
  - `为合作伙伴免费开放物联网系统，实现合作共赢`
- **案例定位：** 展示农机制造商解决方案的成功实践

### 3.5 页脚信息
#### 3.5.1 公司信息展示
- **公司名称：** `联农创世（北京）农业科技有限公司`
- **标语：** `数字农业专家 · 国家高新企业 · 专精特新企业`
- **公司描述：**
  - `成立于2017年，专注数字农业技术研发与服务`
  - `为农业生产组织和农机制造商提供数字化解决方案`

#### 3.5.2 联系信息
- **地址：** `北京市亦庄开发区林肯公园C区21号楼1504`
- **电话：** `4000-658-698`
- **邮箱：** `<EMAIL>`

#### 3.5.3 版权信息
- **版权声明：** `© 2024 联农创世（北京）农业科技有限公司. 保留所有权利.`
- **备案号：** `京ICP备18021229号-1`

---

## 4. 解决方案页内容设计 (solutions.html)

### 4.1 页面概述与核心逻辑
#### 4.1.1 开篇逻辑说明
- **核心理念：** 两大解决方案都围绕"人、机、地、活"的数字化管理核心
- **方案区别：** 
  - 前者直接服务农业生产组织
  - 后者赋能农机企业，帮助其客户实现类似功能
  - 为制造商提供智能报修、AI客服等增值服务，构建完整的服务生态

### 4.2 方案A：农业生产组织解决方案
#### 4.2.1 方案概述
- **解决方案名称：** `农业生产组织数字化管理解决方案`
- **核心价值：** `"人、机、地、活"全流程数字化管理`

#### 4.2.2 功能模块
- **人员管理与派单：** 人员调度、任务分配、绩效管理
- **设备监控：** 实时位置、作业状态、维护提醒
- **地块规划：** 地块测量、作业规划、进度跟踪
- **任务跟踪：** 作业记录、质量监控、完成度统计
- **物料管理：** 库存管理、采购计划、成本控制
- **数据分析：** 作业分析、效率统计、决策支持

#### 4.2.3 成功案例：辽宁万盈农业
- **案例标题：** `辽宁万盈农业`
- **英文副标题：** `Agricultural Socialized Service Leader`
- **配图要求：** 
  - `pics/万盈农业logo.jpg`
  - **新增：** `pics/万盈app.jpg` - 万盈农业数字化管理系统实际界面
- **展示方式：** Logo+标题并列 + APP界面展示 + 4个核心成果
- **核心成果：**
  - `为农业社会化服务领军者定制开发核心数字系统(ADMS)`
  - `系统支撑其 30.6万亩 玉米托管业务高效运营`
  - `系统精确记录 6项作业、11个阶段、27个步骤、175项信息`
  - `助力其实现数字化管理转型，提升托管服务质量与效率`

#### 4.2.4 其他成功案例
**新增展示：** 更多成功案例部分

1. **丰收大管家平台**
   - **配图：** `pics/丰收大管家平台.jpg`
   - **描述：** 农业生产全流程数字化管理平台
   - **特点：** 为农业合作社提供从种植到收获的全程数字化管理解决方案
   - **样式：** emerald-gradient背景

2. **淳化和坤农服管理系统**
   - **配图：** `pics/淳化和坤农服管理系统.jpg`
   - **描述：** 专业农业服务组织管理系统
   - **特点：** 为农业服务组织打造的专业化、标准化管理平台
   - **样式：** tech-gradient背景

### 4.3 方案B：农机制造商解决方案
#### 4.3.1 方案概述
- **解决方案名称：** `农机制造商数智化农机系统解决方案`
- **行业背景：** `对标汽车行业数智化趋势，我们能为中小企业低成本、快速地实现行业龙头企业正在布局的智能化系统`

#### 4.3.2 核心价值体系
- **🛡️ 防御：** 构建竞争壁垒，保护市场份额
- **⚡ 进攻：** 创新服务模式，开拓新收入源
- **🔗 关系：** 深化客户粘性，建立长期关系

#### 4.3.3 功能模块详述
##### 4.3.3.1 为农机用户提供
- **作业监控：** 实时作业状态、效率分析
- **收益记账：** 作业收入、成本核算
- **地块管理：** 地块信息、作业记录
- **设备管控：** 设备状态、远程控制

##### 4.3.3.2 为制造商提供
- **一键报修：** 快速报修、维修派单
- **AI客服：** 智能问答、技术支持
- **品牌专属APP：** 定制化应用、品牌展示
- **保养服务：** 保养提醒、服务预约

#### 4.3.4 成功案例：e联农机共享平台
- **案例标题：** `e联农机共享平台`
- **英文副标题：** `IoT Agricultural Machinery Sharing Platform`
- **配图要求：** `pics/e联农机介绍页.png`
- **展示方式：** 满屏宽度图片 + 6个特色亮点
- **核心亮点：**
  - `中国首家物联网农机共享服务平台`
  - `以秸秆收储为核心，依托意大利D630型圆捆机产品`
  - `物联网系统实时监控：机器定位、作业效率、工作时间、机手排名`
  - `创新主动式服务：2小时响应，定期巡视，专用配件箱`
  - `全产业链模式：连接制造企业、平台、机主、收储企业`
  - `为合作伙伴免费开放物联网系统，实现合作共赢`

#### 4.3.5 实施路径规划
##### 4.3.5.1 第一阶段：防守反击版 (3-6个月)
- **基础监控：** 建立基本的设备监控功能
- **品牌APP建立：** 开发品牌专属应用
- **用户数据收集：** 建立用户数据体系

##### 4.3.5.2 第二阶段：进攻创收版 (6-12个月)
- **增值服务开发：** 开发付费增值功能
- **AI功能集成：** 集成智能化服务
- **收费模式建立：** 建立可持续收费模式

##### 4.3.5.3 第三阶段：生态垄断版 (12个月+)
- **完整生态构建：** 建立完整服务生态
- **平台化运营：** 实现平台化商业模式
- **行业标准制定：** 参与制定行业标准

---

## 5. 设计规范与技术要求

### 5.1 色彩方案
- **背景色：** 纯白 (`#FFFFFF`)
- **主文本色：** 深石墨灰 (`#222222`)
- **柔和强调色：** 深灰蓝 (`#3B5998`)，用于标题、Bento Grid边框
- **行动点睛色：** 品牌红 (`#E31937`)，**仅用于**最重要的行动号召按钮

### 5.2 图片资源规范
- **存储路径：** 所有图片统一存放于 `pics/` 文件夹
- **命名规范：** 图片名需通俗易懂，反映内容
- **应用原则：** 优先使用真实产品截图和案例图片，确保真实性

#### 5.2.1 实际使用的图片资源清单

**基础资源：**
- `易联农业logo.png` - 公司Logo
- `高新证书.jpg` - 国家高新技术企业证书
- `专精特新证书.png` - 专精特新企业证书

**AI技术展示：**
- `病虫害识别(1).png` - 病虫害识别AI界面
- `农机ai.jpg` - 农机专家AI界面
- `种植专家ai.png` - 种植专家AI界面

**技术能力展示：**
- `物联网系统.png` - 完整物联网解决方案图
- `农田识别前.png` - 农田识别前原始图像
- `农田识别后.png` - 农田识别后地块边界图
- `轨迹识别1.png` - 原始农机轨迹
- `轨迹识别2.png` - 轨迹识别后生成的地块

**软件开发展示：**
- `农机监控.jpg` - 农机监控界面
- `地块列表.png` - 地块管理界面
- `作业统计.jpg` - 作业统计分析界面
- `作业单.jpg` - 作业单管理界面
- `农机详情.jpg` - 农机设备详情界面

**成功案例：**
- `万盈农业logo.jpg` - 万盈农业Logo
- `万盈app.jpg` - 万盈农业数字化管理系统界面
- `丰收大管家平台.jpg` - 丰收大管家平台界面
- `淳化和坤农服管理系统.jpg` - 淳化和坤农服管理系统界面
- `e联农机介绍页.png` - e联农机共享平台介绍

---

## 6. 全局组件设计

### 6.1 全局页脚 (Footer)
- **定位：** 在所有页面的底部始终存在
- **包含内容：** 
  - 公司全称
  - 公司地址
  - 联系电话
  - 电子邮箱
- **联系信息：**
  - **地址：** 北京市亦庄开发区林肯公园C区21号楼1504
  - **电话：** 4000-658-698
  - **邮箱：** <EMAIL>