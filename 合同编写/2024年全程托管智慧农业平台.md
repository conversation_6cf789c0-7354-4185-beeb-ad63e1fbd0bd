
合同编号：YLJS20240301A


技术服务合同


项目名称： 万盈全程托管智慧服务平台

委托方（甲方）：辽宁万盈农业科技有限公司 
受托方（乙方）：联农创世（北京）农业科技有限公司  
                    
   签订时间：   2024年 3月1日             
签订地点：        北京                           





技术服务合同

   委托方（甲方）：   辽宁万盈农业科技有限公司            
       法定代表人：         韩卓                           
       通讯地址：     辽宁沈阳市沈北新区沈北路49号        
     受托方（乙方）：  联农创世（北京）农业科技有限公司     
       法定代表人：        曲宏伟                          
       通讯地址： 北京市亦庄开发区林肯公园C区21号楼1504室

本合同甲方委托乙方进行全程托管智慧服务平台的开发，并支付相应的技术服务报酬。双方经过平等协商，在真实、充分地表达各自意愿的基础上，根据《中华人民共和国民法典》的规定，达成如下协议，并由双方共同恪守。    
第一条 甲方委托乙方进行技术服务的内容如下：
1.技术服务的目标为建设万盈全程托管智慧服务平台。
2.技术服务内容包括： 
保证智慧服务平台的数字化、高效调度、负载均衡及各子系统运行流畅度。 
构建计算平台、计算资源池、存储资源池、网络资源池，提供网络安全服务，为各业务系统和监管系统提供信息化基础服务能力支撑。 
有效处理资源不低于以下数据：CPU：20核；内存：16GB；存储：2048GB。
    第二条 乙方应按下列要求完成技术服务工作：
1.平台基础环境搭建与调试应在合同签订后 80个工作日内完成。
2.平台各子系统及负载均衡系统应在2024年7月31日前完成开发和测试。
3.具体需求详见《技术需求细则》（见附件）。
第三条甲方向乙方支付技术服务报酬及支付方式为：
1.技术开发费用：800,000.00大写：捌拾万元整。
2.具体支付方式和时间如下：
1）本合同签订10日内，甲方向乙方支付160,000.00元，大写：壹拾陆万元整；
2）2024年4月30日前甲方向乙方支付240,000.00元，大写：贰拾肆万元整；
3）2024年7月31日前验收合格后甲方向乙方支付400,000.00元，大写：肆拾万元整。
3.乙方收到款项后  10  个工作日内为甲方开具支付的信息技术服务费增值税专用发票。 
      乙方开户银行名称、帐号为：
         开户银行：  中国建设银行股份有限公司北京经济技术开发区北环西路支行 
         帐　　号：  1105 0171 7100 0000 0133  
账户名称：　联农创世（北京）农业科技有限公司 
    第四条 双方确定，发生不可抗力，致使本合同的履行成为不可能的，可以解除本合同。
第五条　双方在履行本合同过程中若发生争议，应协商解决。
    第六条 本合同一式 2 份，具有同等法律效力。
    第七条 本合同经双方签字盖章后生效。扫描件有效。

甲方：       辽宁万盈农业科技有限公司        （盖章）                
法定代表人／委托代理人：　　　　  　　　      　（签名）　
                                       年     月     日

乙方：      联农创世（北京）农业科技有限公司    （盖章）                
 法定代表人／委托代理人：　　　　  　　      　　（签名）
                                       年     月     日





附件：《技术需求细则》

技术需求细则
一、智慧服务平台运行基础环境
1. 数字化
   - 实现农业生产全过程的数字化管理，包括种植、管理、收获、物流等环节，提供电子记录和自动化处理功能。
   - 用户界面应友好，易于操作，支持中文和英文两种语言选择。
   - 数据展示包括实时监控、历史记录、统计分析图表等，支持移动端（Android和iOS）和PC端（Web浏览器）访问。
2. 高效调度
   - 配置智能调度算法，支持自动化资源分配，优化各环节的资源使用。
   - 实现任务优先级管理，支持多任务调度，提供任务完成时间预测功能。
   - 系统应能够根据实时数据动态调整资源分配，提高整体运行效率。
3. 负载均衡
   - 部署高性能负载均衡系统，支持HTTP、HTTPS、TCP、UDP协议，确保高并发情况下系统的稳定性。
   - 配置自动故障转移机制，确保单点故障时的业务连续性。
   - 提供实时监控和报警功能，检测和报告负载异常情况。
4. 子系统运行流畅度
   - 各子系统应独立运行，采用微服务架构设计，确保服务之间的解耦。
   - 系统响应时间不超过200毫秒，支持高并发访问，保证用户操作的实时性。
   - 提供性能优化工具，定期进行系统性能测试和优化。

二、基础环境搭建
1. 计算平台
   - 建设基于Kubernetes的高性能计算平台，支持容器化应用的部署和管理。
   - 配置虚拟化技术（如VMware、KVM），提高硬件资源利用率，支持灵活的计算资源调度。
   - 计算平台应支持横向扩展，能够根据业务需求增加计算节点。
2. 计算资源池
   - 建立动态计算资源池，支持按需分配和释放计算资源。
   - 资源池中每个计算节点配置20核CPU和16GB内存，支持负载均衡和自动伸缩。
   - 提供资源使用监控和管理界面，实时展示资源使用情况。
3. 存储资源池
   - 建立分布式存储系统（如Ceph、HDFS），提供高容量、高可靠性的存储服务，支持数据的快速存取。
   - 配置2048GB的存储设备，支持RAID 10级别的冗余，确保数据的可靠性和可用性。
   - 提供数据备份和恢复机制，支持定时快照和异地备份，确保数据安全。
4. 网络资源池
   - 建立基于SDN（软件定义网络）的网络资源池，提供高效、稳定的网络连接。
   - 部署多层防火墙和入侵检测系统，提供DDoS防护和流量监控功能。
   - 网络资源池应支持多租户隔离，确保不同业务系统之间的数据安全。
5. 网络安全服务
   - 提供全面的网络安全解决方案，包括防火墙、入侵检测、防病毒等，保护系统免受网络攻击。
   - 实现数据传输加密（如TLS/SSL），保护数据隐私和安全。
   - 配置安全监控和日志记录系统，实时监控网络安全状态，提供安全事件报告和处理机制。

三、资源配置标准
1. CPU
   - 每个计算节点配置20核的高性能CPU，支持多线程并行处理，提高数据处理效率。
   - 配置CPU负载监控和报警系统，自动调整计算资源，确保高负载情况下系统稳定运行。
2. 内存
   - 每个计算节点配置16GB的内存，确保系统的稳定运行和数据处理能力。
   - 内存应支持动态扩展，根据业务需求自动调整内存分配。
3. 存储
   - 配置2048GB的高性能存储设备，支持SSD和HDD混合存储，提高数据访问速度。
   - 存储系统应支持快照和备份功能，确保数据在发生故障时能够快速恢复。

四、系统集成与维护
1. 系统集成
   - 提供标准API接口，支持与现有农业管理系统的无缝集成，实现数据和功能的共享。
   - 系统应支持数据同步和数据一致性检查，确保数据的完整性和准确性。
   - 提供开发者文档和技术支持，方便第三方系统的接入和扩展。
2. 数据分析
   - 系统应具备实时数据分析能力，支持多维度的数据分析和报表生成。
   - 提供数据可视化工具（如Grafana、Kibana），帮助用户直观了解农业生产状况。
   - 数据分析系统应支持历史数据查询和趋势分析，提供预测和决策支持功能。
3. 用户管理
   - 提供完善的用户管理系统，支持多级用户权限设置，确保系统的安全性和操作的可控性。
   - 用户管理系统应支持用户行为日志记录，提供详细的操作记录和审计功能。
   - 支持单点登录（SSO）和多因素认证（MFA），提高系统的安全性。
4. 维护与支持
   - 提供24/7系统维护和技术支持服务，确保系统的长期稳定运行。
   - 定期进行系统更新和优化，提升系统性能和安全性。
   - 提供故障处理和紧急响应机制，确保系统在任何情况下都能正常运行。