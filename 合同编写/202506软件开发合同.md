# 软件开发技术服务合同

**合同编号：** YLJS20250601A

**项目名称：** 丰收大管家智慧农业管理系统

**委托方（甲方）：** 辽宁万盈农业科技有限公司  
**受托方（乙方）：** 联农创世（北京）农业科技有限公司

**签订时间：** 2025年 6月   日  
**签订地点：** 北京

---

## 软件开发技术服务合同

**委托方（甲方）：** 辽宁万盈农业科技有限公司  
**法定代表人：** 韩卓  
**通讯地址：** 辽宁沈阳市沈北新区沈北路49号

**受托方（乙方）：** 联农创世（北京）农业科技有限公司  
**法定代表人：** 曲宏伟  
**通讯地址：** 北京市亦庄开发区林肯公园C区21号楼1504室

本合同甲方委托乙方进行丰收大管家智慧农业管理系统的开发，并支付相应的技术服务报酬。双方经过平等协商，在真实、充分地表达各自意愿的基础上，根据《中华人民共和国民法典》的规定，达成如下协议，并由双方共同恪守。

## 第一条 技术服务内容

1. 技术服务的目标为建设丰收大管家智慧农业管理系统。

2. 技术服务内容包括：
   - 构建农业数字化管理平台，实现基础信息管理、合同管理、作业管理、巡地管理、物料管理和统计管理等功能模块。
   - 开发移动端应用程序和Web管理后台系统，支持GPS/北斗定位技术和农机监控器设备数据采集。
   - 建立服务器运行环境和数据库系统，支持TCP协议通信和16进制数据处理，确保系统正常运行。

## 第二条 完成要求

1. 系统基础框架搭建与核心功能开发应在合同签订后 90个工作日内完成。
2. 移动端APP和PC端管理系统应在2025年4月30日前完成开发和测试。
3. 系统上线部署和用户培训应在2025年6月30日前完成。
4. 具体需求详见《开发需求细则》（见附件）。

## 第三条 支付方式

1. **技术开发费用：** 1,000,000.00元，大写：壹佰万元整。

2. **具体支付方式和时间如下：**
   - 本合同签订10日内，甲方向乙方支付400,000.00元，大写：肆拾万元整；
   - 系统开发完成验收后，甲方向乙方支付600,000.00元，大写：陆拾万元整。

3. 乙方收到款项后 10 个工作日内为甲方开具支付的信息技术服务费增值税专用发票。

**乙方开户银行信息：**
- **开户银行：** 中国建设银行股份有限公司北京经济技术开发区北环西路支行
- **帐号：** 1105 0171 7100 0000 0133
- **账户名称：** 联农创世（北京）农业科技有限公司

## 第四条 不可抗力

双方确定，发生不可抗力，致使本合同的履行成为不可能的，可以解除本合同。

## 第五条 争议解决

双方在履行本合同过程中若发生争议，应协商解决。

## 第六条 合同效力

本合同一式 2 份，具有同等法律效力。

## 第七条 生效条件

本合同经双方签字盖章后生效。扫描件有效。

---

## 签字区域

**甲方：** 辽宁万盈农业科技有限公司 （盖章）  
**法定代表人／委托代理人：** ________________（签名）  
**日期：** ____年____月____日

**乙方：** 联农创世（北京）农业科技有限公司 （盖章）  
**法定代表人／委托代理人：** ________________（签名）  
**日期：** ____年____月____日

---

# 附件：开发需求细则

## 一、丰收大管家系统核心功能模块

### 1. 基础信息管理模块

#### 1.1 人员管理子系统
- 构建客户信息全生命周期管理体系
- 支持作业人员智能调度与实时定位监控
- 建立人员信息数据库，支持多维度信息检索与统计分析

#### 1.2 土地管理子系统
- 支持多种智能测量模式：走一圈、画地块、按垄测量、精准画地、智能画地
- 构建地块空间信息管理体系，支持面积计算与边界确定
- 建立地块信息数据库，支持空间数据存储与智能查询

#### 1.3 农机管理子系统
- 构建农机设备全生命周期管理平台
- 支持农事作业记录与数字化管理
- 构建终端设备统一管理体系
- 支持农机作业轨迹智能记录与数据分析

### 2. 合同管理模块
- 构建合同信息全流程管理体系
- 支持农业服务信息数字化记录与智能管理

### 3. 作业管理模块
- 支持作业任务智能批量生成与动态分配
- 构建作业任务在线协同与进度可视化跟踪体系
- 集成智能导航服务，支持作业地点精准定位
- 支持作业轨迹智能记录与数据存储
- 提供历史作业数据多维度查询与统计分析

### 4. 巡地管理模块
- 支持巡地记录数字化管理，集成影像与位置信息采集
- 构建作业过程全链路追溯查询体系
- 建立地块管理数据库

### 5. 物料管理模块（简易进销存系统）
- 构建智能库存管理体系
- 支持入库、出库、返库全流程数字化管理
- 支持物料领取与使用记录智能化追踪

### 6. 统计管理模块
- 构建多维度数据统计分析引擎
- 支持基于GIS的可视化展示，集成地块分布、设备位置与人员信息
- 提供数据统计报表导出与分析功能

### 7. 辅助功能模块
- 集成气象信息服务，支持天气预报数据展示
- 集成农历信息服务，支持农时节气信息展示

## 二、技术实现要求

### 1. 移动端应用开发
- 构建跨平台移动端应用，支持主流移动设备生态
- 集成高精度卫星定位技术（GPS/北斗），支持精准位置服务
- 集成移动设备原生功能，支持影像采集与智能导航
- 构建离线数据存储机制，保障野外作业环境下的系统可用性

### 2. Web管理后台开发
- 构建Web端管理控制台系统
- 支持数据管理与智能统计分析
- 兼容主流浏览器环境，支持响应式访问

### 3. 农机物联网设备接入
- 支持农机监控设备数据采集与智能处理
- 构建农机作业轨迹信息智能记录与存储体系
- 支持设备数据自动同步与实时传输
- 兼容TCP通信协议，支持多种数据格式处理
- 构建断点续传机制与离线数据缓存体系

### 4. 数据库系统建设
- 构建企业级数据库系统，保障数据安全存储与高可用性
- 支持多品类农作物数据管理（玉米、水稻、花生、小麦、大豆等）
- 构建数据备份与容灾恢复机制

## 三、用户权限管理

### 1. 用户角色定义
- **系统管理员：** 负责系统整体管理和维护
- **农场管理员：** 负责农场日常运营管理
- **机手：** 执行农机作业任务
- **普通工作人员：** 执行日常农事活动

### 2. 权限控制
- 构建基于角色的权限管理体系（RBAC）
- 支持不同角色用户的功能模块访问控制

## 四、农机监控器技术要求

### 1. 硬件基本配置
- 集成高精度卫星定位模块，支持精准定位服务
- 配置移动通信模块与数据存储单元
- 支持宽范围电压供电，适应农机设备环境
- 配置设备状态指示与监控功能

### 2. 通信功能要求
- 支持标准通信协议与服务器数据交互
- 兼容多种数据格式传输与处理
- 支持断点续传与远程升级机制
- 支持实时数据采集与传输

### 3. 数据采集功能
- 采集时空信息、运行状态等核心数据
- 支持设备标识与作业数据智能采集
- 构建离线数据缓存与同步机制

## 五、系统基本要求

### 1. 系统稳定性要求
- 构建高可用性分布式架构，保障系统7×24小时稳定运行
- 构建多层级数据安全防护体系，确保数据完整性与访问控制

### 2. 用户界面要求
- 构建响应式自适应用户交互界面，优化用户体验
- 支持农业生产环境下的多场景应用适配

## 六、交付标准

### 1. 软件交付内容
- 交付跨平台移动端应用程序及部署包
- 交付Web端管理控制台系统
- 提供系统架构文档及操作指南

### 2. 实施培训服务
- 提供系统部署实施及用户培训服务
- 确保用户熟练掌握系统核心功能操作

### 3. 技术支持服务
- 提供7×24小时技术支持及运维服务
- 协助处理系统运行过程中的技术问题

