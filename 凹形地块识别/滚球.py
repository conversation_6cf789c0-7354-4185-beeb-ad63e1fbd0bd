import numpy as np
from shapely.geometry import Polygon, MultiPolygon
import geopandas as gpd
from pyproj import Transformer
import json
import os
import folium
import alphashape
from math import radians, cos, sin, asin, sqrt
from scipy.spatial import ConvexHull

# --- 参数阈值区 ---
ALPHA_VALUE = 0.05 
ADD_JITTER = False
JITTER_AMOUNT_METERS = 0.01

MAP_DEFAULT_ZOOM_START = 15
POINTS_MARKER_RADIUS = 1
POINTS_MARKER_FILL_OPACITY = 0.6
PROCESSED_POLY_COLOR = 'red'
PROCESSED_POLY_WEIGHT = 3
PROCESSED_POLY_FILL_COLOR = '#FF0000'
PROCESSED_POLY_FILL_OPACITY = 0.25
AREA_MARKER_FONT_SIZE = '10pt'
# --- 参数阈值区结束 ---

def haversine(lon1, lat1, lon2, lat2):
    lon1, lat1, lon2, lat2 = map(radians, [float(lon1), float(lat1), float(lon2), float(lat2)])
    dlon = lon2 - lon1; dlat = lat2 - lat1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * asin(sqrt(a)); r = 6371000
    return c * r

script_dir = os.path.dirname(os.path.abspath(__file__))
file_path = os.path.join(script_dir, '866214077609600_20240814_wgs84.json') # 确保文件名正确
with open(file_path, 'r', encoding='utf-8') as f: data = json.load(f)
coords_list_latlon_original = [[float(item['longitude']), float(item['latitude'])] for item in data]
points_np_array_latlon = np.array(coords_list_latlon_original, dtype=float)

if len(coords_list_latlon_original) < 3: raise ValueError("至少需三点。")

points_for_alphashape_meter = []
target_crs_meter = None
print("开始坐标转换 (经纬度 -> 米制)...")
try:
    temp_gdf_for_crs = gpd.GeoDataFrame(
        geometry=gpd.points_from_xy([p[0] for p in coords_list_latlon_original], [p[1] for p in coords_list_latlon_original]),
        crs="EPSG:4326")
    target_crs_meter = temp_gdf_for_crs.estimate_utm_crs()
    if target_crs_meter is None:
        print("无法自动估算UTM，尝试EPSG:3857。"); target_crs_meter = "EPSG:3857"
    print(f"目标米制坐标系: {target_crs_meter}")
    transformer_to_meter = Transformer.from_crs("EPSG:4326", target_crs_meter, always_xy=True)
    points_for_alphashape_meter = [transformer_to_meter.transform(lon, lat) for lon, lat in coords_list_latlon_original]
    if ADD_JITTER and points_for_alphashape_meter:
        print(f"为米制坐标点添加扰动，幅度: {JITTER_AMOUNT_METERS} 米...")
        points_for_alphashape_meter = [(x + np.random.uniform(-JITTER_AMOUNT_METERS, JITTER_AMOUNT_METERS),
                                        y + np.random.uniform(-JITTER_AMOUNT_METERS, JITTER_AMOUNT_METERS))
                                       for x, y in points_for_alphashape_meter]
    print("坐标转换完成。")
except Exception as e_proj:
    print(f"坐标转换失败: {e_proj}"); exit()

print(f"使用 Alpha Value: {ALPHA_VALUE} 计算 Alpha Shape (基于米制坐标)...")
alpha_shape_geom_meter = None
try:
    alpha_shape_geom_meter = alphashape.alphashape(points_for_alphashape_meter, alpha=ALPHA_VALUE)
except Exception as e_alpha:
    print(f"计算 Alpha Shape (米制) 时出错: {e_alpha}")

processed_alpha_geom_meter_single = None
if alpha_shape_geom_meter and not alpha_shape_geom_meter.is_empty:
    if alpha_shape_geom_meter.geom_type == 'Polygon':
        processed_alpha_geom_meter_single = alpha_shape_geom_meter
        print("Alpha Shape (米制) 计算得到单一 Polygon。")
    elif alpha_shape_geom_meter.geom_type in ['MultiPolygon', 'GeometryCollection']:
        print(f"Alpha Shape (米制) 返回 {alpha_shape_geom_meter.geom_type}，合并顶点后计算凸包。")
        all_points_from_geoms = []
        geoms_to_process = []
        if alpha_shape_geom_meter.geom_type == 'MultiPolygon': geoms_to_process = alpha_shape_geom_meter.geoms
        elif alpha_shape_geom_meter.geom_type == 'GeometryCollection':
            for geom_item in alpha_shape_geom_meter.geoms:
                if geom_item.geom_type == 'Polygon': geoms_to_process.append(geom_item)
                elif geom_item.geom_type == 'MultiPolygon': geoms_to_process.extend(list(geom_item.geoms))
        if geoms_to_process:
            for poly in geoms_to_process:
                if poly.geom_type == 'Polygon': all_points_from_geoms.extend(list(poly.exterior.coords))
            if len(all_points_from_geoms) >= 3:
                unique_points_for_hull = np.array(list(set(map(tuple, all_points_from_geoms))))
                if unique_points_for_hull.shape[0] >= 3:
                    hull_of_fragments = ConvexHull(unique_points_for_hull)
                    processed_alpha_geom_meter_single = Polygon(unique_points_for_hull[hull_of_fragments.vertices])
                    print("已据分离碎块顶点计算新凸包。")
                else: print("碎块唯一顶点数少于3。")
            else: print("碎块顶点总数少于3。")
        else: print(f"{alpha_shape_geom_meter.geom_type} 中无Polygon。")
    else: print(f"Alpha Shape (米制) 返回 {alpha_shape_geom_meter.geom_type}，非面状。")

if processed_alpha_geom_meter_single is None:
    print("Alpha Shape未能生成有效单一米制多边形，将用原始米制点集凸包。")
    if len(points_for_alphashape_meter) >=3:
        hull_fallback = ConvexHull(np.array(points_for_alphashape_meter))
        processed_alpha_geom_meter_single = Polygon(np.array(points_for_alphashape_meter)[hull_fallback.vertices])
    else: print("原始点集不足3个。")

final_display_polygon_latlon = None
if processed_alpha_geom_meter_single and not processed_alpha_geom_meter_single.is_empty:
    print("开始将最终单一多边形转回经纬度...")
    try:
        transformer_to_latlon = Transformer.from_crs(target_crs_meter, "EPSG:4326", always_xy=True)
        def transform_polygon_coords(polygon_meter, transformer):
            exterior_latlon = [transformer.transform(x, y) for x, y in polygon_meter.exterior.coords]
            interiors_latlon = [[transformer.transform(x, y) for x, y in interior.coords] for interior in polygon_meter.interiors]
            return Polygon(exterior_latlon, interiors_latlon)
        final_display_polygon_latlon = transform_polygon_coords(processed_alpha_geom_meter_single, transformer_to_latlon)
        if final_display_polygon_latlon: print("转回经纬度完成。")
    except Exception as e_reproj_final:
        print(f"逆转换回经纬度失败: {e_reproj_final}")
        final_display_polygon_latlon = None

if final_display_polygon_latlon is None:
    print("所有尝试均失败，最终用原始经纬度点集凸包。")
    hull_ultimate_fallback = ConvexHull(points_np_array_latlon)
    final_display_polygon_latlon = Polygon([points_np_array_latlon[vertex] for vertex in hull_ultimate_fallback.vertices])

processed_hull_area_m2 = 0; area_info_text_for_popup = "面积信息不可用"
try:
    if final_display_polygon_latlon and not final_display_polygon_latlon.is_empty:
        gdf = gpd.GeoDataFrame(geometry=[final_display_polygon_latlon], crs="EPSG:4326")
        gdf_utm = gdf.to_crs(gdf.estimate_utm_crs())
        processed_hull_area_m2 = gdf_utm['geometry'].area.iloc[0]
        area_info_text_for_popup = f"最终地块面积: {processed_hull_area_m2:.2f} m² (α={ALPHA_VALUE}{', ProjJittered' if ADD_JITTER else ', Projected'})"
    else: area_info_text_for_popup = f"计算结果无效 (α={ALPHA_VALUE}{', ProjJittered' if ADD_JITTER else ', Projected'})。"
except Exception as e_area:
    print(f"面积计算出错: {e_area}")
    area_info_text_for_popup = f"面积计算出错 (α={ALPHA_VALUE}{', ProjJittered' if ADD_JITTER else ', Projected'})"

gdf_to_display_latlon = gpd.GeoDataFrame(geometry=[final_display_polygon_latlon], crs="EPSG:4326")
map_center_lat = points_np_array_latlon[:, 1].mean(); map_center_lon = points_np_array_latlon[:, 0].mean()
m = folium.Map(location=[map_center_lat, map_center_lon], zoom_start=MAP_DEFAULT_ZOOM_START, tiles="OpenStreetMap")

for p_idx, p_coord_orig_lonlat in enumerate(coords_list_latlon_original):
    folium.CircleMarker(location=[p_coord_orig_lonlat[1], p_coord_orig_lonlat[0]], radius=POINTS_MARKER_RADIUS, color='blue', fill=True, fill_color='blue', fill_opacity=POINTS_MARKER_FILL_OPACITY, tooltip=f"点 {p_idx+1}: ({p_coord_orig_lonlat[1]:.5f}, {p_coord_orig_lonlat[0]:.5f})").add_to(m)

popup_html_content = f"""<div style="font-family:Arial,sans-serif;font-size:13px;"><h4>地块信息</h4><p><strong>{area_info_text_for_popup}</strong></p></div>"""
if final_display_polygon_latlon and not final_display_polygon_latlon.is_empty:
    folium.GeoJson(gdf_to_display_latlon.__geo_interface__, name='最终地块边界', style_function=lambda x: {'color':PROCESSED_POLY_COLOR,'weight':PROCESSED_POLY_WEIGHT,'fillColor':PROCESSED_POLY_FILL_COLOR,'fillOpacity':PROCESSED_POLY_FILL_OPACITY}, tooltip=folium.Tooltip(area_info_text_for_popup), popup=folium.Popup(popup_html_content, max_width=350, sticky=False)).add_to(m)

if final_display_polygon_latlon and not final_display_polygon_latlon.is_empty:
    if final_display_polygon_latlon.geom_type == 'MultiPolygon': display_centroid = final_display_polygon_latlon.representative_point()
    else: display_centroid = final_display_polygon_latlon.centroid
    area_marker_text = f"{processed_hull_area_m2:.1f} m²" if processed_hull_area_m2 > 0 else "N/A"
    folium.Marker(location=[display_centroid.y, display_centroid.x], tooltip=f"中心点 (估算面积: {area_marker_text})", icon=folium.DivIcon(icon_size=(150,36), icon_anchor=(75,18), html=f'<div style="font-size:{AREA_MARKER_FONT_SIZE};color:black;background-color:rgba(255,255,255,0.7);border-radius:4px;padding:2px 5px;text-align:center;white-space:nowrap;"><b>{area_marker_text}</b></div>')).add_to(m)
    
folium.LayerControl().add_to(m)
jitter_suffix = "_proj_jittered" if ADD_JITTER else "_projected"
html_filename = os.path.splitext(os.path.basename(file_path))[0] + f"_map_alphashape_single_{str(ALPHA_VALUE).replace('.', 'p')}{jitter_suffix}.html"
html_output_path = os.path.join(script_dir, html_filename)
m.save(html_output_path)
print(f"Alpha Shape 地图已保存: {html_output_path}")