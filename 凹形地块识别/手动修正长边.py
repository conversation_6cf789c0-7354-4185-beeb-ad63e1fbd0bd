import os
import pandas as pd
import matplotlib.pyplot as plt
import geopandas as gpd
from shapely.geometry import <PERSON>ygon, Point, LineString
from pyproj import CRS
from math import radians, cos, sin, asin, sqrt
import numpy as np

from cdBoundary.boundary import ConcaveHull

# ==== 1. 自动确定CSV路径 ====
current_dir = os.path.dirname(os.path.abspath(__file__))
csv_path = os.path.join(current_dir, '20240511_cleaned-2.csv')  # 改成你的CSV名字
output_geojson_path = os.path.join(current_dir, 'output_polygon_fixed.geojson')

# ==== 2. 读入数据 ====
df = pd.read_csv(csv_path)
points = df[['longitude', 'latitude']].values.tolist()  # 转换为list格式，cdBoundary需要

# Haversine公式，计算两经纬度点间真实地表距离（米）
def haversine(lon1, lat1, lon2, lat2):
    lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])
    dlon = lon2 - lon1
    dlat = lat2 - lat1
    a = sin(dlat/2)**2 + cos(lat1)*cos(lat2)*sin(dlon/2)**2
    c = 2 * asin(sqrt(a))
    r = 6371000  # 地球半径（米）
    return c * r

# ==== 3. 生成凹形多边形 ====
ch = ConcaveHull()
ch.loadpoints(points)
ch.calculatehull(tol=20)  # 使用20米阈值
shape = ch.hull  # 获取shapely Polygon对象

# ==== 4. 针对性修正长边问题 ====
print("开始处理原始多边形...")
exterior_coords = list(shape.exterior.coords)

# 找出136米左右的边（通常是左侧的边）
problem_edge_idx = None
for i in range(len(exterior_coords) - 1):
    p1 = exterior_coords[i]
    p2 = exterior_coords[i + 1]
    distance = haversine(p1[0], p1[1], p2[0], p2[1])
    if 130 < distance < 140:  # 识别约136米的边
        problem_edge_idx = i
        print(f"发现问题边: 顶点索引 {i} 和 {i+1}, 长度 {distance:.1f}米")
        break

if problem_edge_idx is not None:
    # 提取问题边的两个端点
    p1 = exterior_coords[problem_edge_idx]
    p2 = exterior_coords[problem_edge_idx + 1]
    
    # 获取所有点的numpy数组
    points_array = np.array(points)
    
    # 创建这条边的LineString对象
    edge_line = LineString([p1, p2])
    
    # 定义搜索范围
    search_dist = 0.0002  # 经纬度单位，约20米
    
    # 创建一个更加凹入的多边形
    new_vertices = exterior_coords.copy()
    
    # 找出接近问题边的点
    potential_replacement_points = []
    for point in points:
        # 计算点到边的距离
        point_geom = Point(point)
        dist_to_edge = edge_line.distance(point_geom)
        
        # 如果点足够接近边，且位于边的"内侧"
        if dist_to_edge < search_dist:
            # 计算点的投影位置，确定点是否在边的中段
            offset = edge_line.project(point_geom)
            relative_pos = offset / edge_line.length
            # 只考虑在边中段的点（避免端点附近的点）
            if 0.2 < relative_pos < 0.8:
                # 判断点是否在多边形内部
                if shape.contains(point_geom):
                    potential_replacement_points.append((point, offset))
    
    print(f"找到 {len(potential_replacement_points)} 个可能的替换点")
    
    if potential_replacement_points:
        # 按照到边起点的距离排序
        potential_replacement_points.sort(key=lambda x: x[1])
        
        # 选择1-3个点（根据点的数量）
        num_points_to_add = min(3, len(potential_replacement_points))
        if num_points_to_add > 0:
            # 找到均匀分布的点
            selected_indices = np.linspace(0, len(potential_replacement_points)-1, num_points_to_add, dtype=int)
            selected_points = [potential_replacement_points[i][0] for i in selected_indices]
            
            # 将这些点插入到多边形边界中
            new_vertices.insert(problem_edge_idx + 1, selected_points[0])
            print(f"在问题边中插入 {len(selected_points)} 个新点")
            for i in range(1, len(selected_points)):
                new_vertices.insert(problem_edge_idx + 1 + i, selected_points[i])
        
    # 创建修正后的多边形
    fixed_polygon = Polygon(new_vertices)
    
    # 如果修正没有效果，尝试手动插入凹陷点
    if len(new_vertices) == len(exterior_coords):
        print("未找到合适的内部点，尝试手动插入凹陷点...")
        # 计算边的中点
        mid_point = [(p1[0] + p2[0])/2, (p1[1] + p2[1])/2]
        
        # 计算向内凹陷的方向（垂直于边的方向）
        edge_vector = [p2[0] - p1[0], p2[1] - p1[1]]
        # 标准化
        edge_length = (edge_vector[0]**2 + edge_vector[1]**2)**0.5
        edge_unit_vector = [edge_vector[0]/edge_length, edge_vector[1]/edge_length]
        
        # 垂直方向（逆时针旋转90度）
        perp_vector = [-edge_unit_vector[1], edge_unit_vector[0]]
        
        # 检查垂直向量是否指向多边形内部
        test_point = [mid_point[0] + 0.0001*perp_vector[0], 
                      mid_point[1] + 0.0001*perp_vector[1]]
        
        if not shape.contains(Point(test_point)):
            # 如果指向外部，反转方向
            perp_vector = [edge_unit_vector[1], -edge_unit_vector[0]]
        
        # 创建向内凹陷的新点（凹陷距离约30米）
        indent_dist = 0.0003  # 经纬度单位
        indent_point = [mid_point[0] + indent_dist*perp_vector[0], 
                       mid_point[1] + indent_dist*perp_vector[1]]
        
        # 插入新点
        new_vertices.insert(problem_edge_idx + 1, indent_point)
        print("手动插入向内凹陷点")
        
        # 再次创建修正后的多边形
        fixed_polygon = Polygon(new_vertices)
else:
    print("未找到约136米的问题边")
    fixed_polygon = shape  # 使用原多边形

# ==== 5. 保存为GeoJSON和计算面积 ====
gdf = gpd.GeoDataFrame(geometry=[fixed_polygon], crs=CRS('EPSG:4326'))
gdf.to_file(output_geojson_path, driver='GeoJSON', encoding='utf-8')

# 计算面积
gdf_projected = gdf.to_crs(gdf.estimate_utm_crs())
area_m2 = gdf_projected['geometry'].area.iloc[0]
print(f"✅ 修正后的多边形面积约为: {area_m2:.2f} 平方米")

# ==== 6. 绘图展示对比效果 ====
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

# 获取原始点的numpy数组格式（用于绘图）
points_array = df[['longitude', 'latitude']].to_numpy()

# 在两个子图中都绘制原始点
ax1.scatter(points_array[:, 0], points_array[:, 1], s=5, color='blue', label='原始点')
ax2.scatter(points_array[:, 0], points_array[:, 1], s=5, color='blue', label='原始点')

# 在左图绘制原始多边形
ax1.plot(*shape.exterior.xy, color='red', linewidth=2, label='原始多边形 (tol=20m)')
# 标注长边
for i in range(len(exterior_coords) - 1):
    p1 = exterior_coords[i]
    p2 = exterior_coords[i + 1]
    mid_point = [(p1[0] + p2[0]) / 2, (p1[1] + p2[1]) / 2]
    distance = haversine(p1[0], p1[1], p2[0], p2[1])
    if distance > 100:  # 只标注长边
        ax1.text(mid_point[0], mid_point[1], f"{distance:.1f}m", fontsize=9, color='red')
        if 130 < distance < 140:  # 问题边添加箭头
            arrow_length = 0.0002
            # 边的方向向量
            dx, dy = p2[0] - p1[0], p2[1] - p1[1]
            # 垂直于边的方向
            perp_x, perp_y = -dy, dx
            # 标准化
            length = (perp_x**2 + perp_y**2)**0.5
            if length > 0:
                perp_x, perp_y = perp_x/length, perp_y/length
            # 箭头终点
            arrow_end = [mid_point[0] + arrow_length * perp_x, mid_point[1] + arrow_length * perp_y]
            # 绘制箭头
            ax1.annotate('', xy=arrow_end, xytext=mid_point, 
                      arrowprops=dict(facecolor='red', shrink=0.05, width=2, headwidth=8))

# 在右图绘制修正后的多边形
ax2.plot(*fixed_polygon.exterior.xy, color='green', linewidth=2, label='修正后的多边形')
# 标注长边
fixed_coords = list(fixed_polygon.exterior.coords)
for i in range(len(fixed_coords) - 1):
    p1 = fixed_coords[i]
    p2 = fixed_coords[i + 1]
    mid_point = [(p1[0] + p2[0]) / 2, (p1[1] + p2[1]) / 2]
    distance = haversine(p1[0], p1[1], p2[0], p2[1])
    if distance > 50:  # 标注所有较长的边
        ax2.text(mid_point[0], mid_point[1], f"{distance:.1f}m", fontsize=9, color='green')

# 设置标题和图例
ax1.set_title('原始cdBoundary多边形')
ax2.set_title('修正后的多边形')
ax1.legend()
ax2.legend()

# 设置坐标轴标签
for ax in [ax1, ax2]:
    ax.set_xlabel('经度')
    ax.set_ylabel('纬度')

plt.tight_layout()
plt.savefig(os.path.join(current_dir, 'fixed_polygon_comparison.png'), dpi=300)
plt.show()

print(f"✅ 对比图像已保存到: {os.path.join(current_dir, 'fixed_polygon_comparison.png')}")
print(f"✅ 修正后的GeoJSON已保存到: {output_geojson_path}") 