import pandas as pd
import numpy as np
from scipy.spatial import ConvexHull
from shapely.geometry import Polygon, LineString, Point
import matplotlib.pyplot as plt

# 第1步,读取CSV文件
file_path = 'polygon_and_inner_points2.csv'
df = pd.read_csv(file_path)

# 将DataFrame转换为NumPy数组
points = df.to_numpy()
# points = points[:130]

# 第2步,计算凸包
hull = ConvexHull(points)
hull_polygon = Polygon([points[vertex] for vertex in hull.vertices])

# 寻找距离凸包边最近的内部点的函数
def find_closest_point_on_edge_vectorized(edge, points):
    p1, p2 = edge.coords[0], edge.coords[1]
    edge_vector = np.array(p2) - np.array(p1)
    edge_length = np.linalg.norm(edge_vector)

    min_distance = np.inf
    closest_point = None

    for point in points:
        point_vector = np.array(point) - np.array(p1)
        dot = np.dot(point_vector, edge_vector)
        if 0 <= dot <= np.dot(edge_vector, edge_vector):
            proj_point = np.array(p1) + dot / np.dot(edge_vector, edge_vector) * edge_vector
            distance = np.linalg.norm(proj_point - np.array(point))
            if distance < min_distance:
                # 检查新形成的边是否比原边短
                new_edge_1 = LineString([edge.coords[0], point])
                new_edge_2 = LineString([point, edge.coords[1]])
                if new_edge_1.length < edge_length and new_edge_2.length < edge_length:
                    min_distance = distance
                    closest_point = point

    return closest_point


# 转换凸包顶点为元组形式，以便后续比较
hull_vertices = [tuple(vertex) for vertex in hull_polygon.exterior.coords]

def check_edges_intersection(vertices):
    """检查多边形的边是否相交"""
    for i in range(len(vertices)):
        line1 = LineString([vertices[i], vertices[(i + 1) % len(vertices)]])
        for j in range(i + 2, len(vertices) - (1 if i == 0 else 0)):
            line2 = LineString([vertices[j], vertices[(j + 1) % len(vertices)]])
            if line1.intersects(line2):
                return True
    return False

# 函数：处理投影，并更新多边形的顶点列表
def process_projection(hull, points, hull_polygon):
    processed_hull = hull_polygon
    iteration_count = 0  # 追踪迭代次数
    while True:
        iteration_count += 1
        print(f"Iteration: {iteration_count}")  # 打印当前迭代次数

        new_vertices = list(processed_hull.exterior.coords)[:-1]  # 排除重复的最后一个顶点

        print('new_vertices',len(new_vertices))
        changes_made = False  # 标记本轮是否进行了更新
        i = 0  # 从第一个顶点开始
        while i < len(new_vertices) :
            p1 = new_vertices[i]
            # 为了处理最后一条边，需要用取余数的方式找到下一个顶点
            p2 = new_vertices[(i + 1) % len(new_vertices)]
            edge = LineString([p1, p2])

            # 仅处理长度超过10米的边
            if edge.length > 6:
                print(edge.length)
                # 这里需要将点集转换为NumPy数组，以适应新的函数
                numpy_points = np.array(list(set(map(tuple, points)) - set(map(tuple, new_vertices))))
                closest_point = find_closest_point_on_edge_vectorized(edge, numpy_points)
                # closest_points = find_closest_points_on_edge(edge, points, processed_hull,new_vertices)
                # print(len(closest_points))
                if closest_point is not None:
                    # 插入新点到顶点列表中，并标记进行了更新
                    new_vertices.insert((i + 1) % len(new_vertices), closest_point)
                    changes_made = True
                    # 因为插入了一个新顶点，需要调整索引
                    i += 2  # 跳过新插入的顶点和下一条边的起点
                else:
                    i += 1  # 如果没有找到最近点，继续处理下一条边
            else:
                i += 1  # 如果边长度不足，继续处理下一条边

        # 如果这一轮没有进行更新，则结束循环
        if not changes_made:
            print("No changes made in this iteration. Ending the process.")
            break
        processed_hull = Polygon(new_vertices)  # 更新多边形

    return processed_hull


# 执行投影处理
processed_hull_polygon = process_projection(hull, points, hull_polygon)

# 计算处理后多边形的面积
processed_hull_area = processed_hull_polygon.area
print(f"经过投影处理后的多边形面积为: {processed_hull_area:.2f}")

# 获取最终多边形的坐标点
final_polygon_coords = list(processed_hull_polygon.exterior.coords)

# 展示结果
plt.figure(figsize=(12, 8))

# 绘制原始凸包
x_hull, y_hull = hull_polygon.exterior.xy
plt.plot(x_hull, y_hull, 'g--', label='Original Convex Hull')

# 绘制处理后的多边形
x_processed, y_processed = processed_hull_polygon.exterior.xy
plt.plot(x_processed, y_processed, 'r-', label='Processed Polygon')

# 绘制内部点
plt.scatter(points[:, 0], points[:, 1], label='Internal Points')

# 计算并标记原始凸包每条边的边长
for i in range(len(hull_polygon.exterior.coords) - 1):
    p1 = hull_polygon.exterior.coords[i]
    p2 = hull_polygon.exterior.coords[i + 1]
    edge = LineString([p1, p2])
    edge_length = edge.length
    mid_point = np.average([p1, p2], axis=0)
    plt.text(mid_point[0], mid_point[1], f'{edge_length:.2f}', color='purple', fontsize=9)

# 计算并标记处理后多边形每条边的边长
for i in range(len(processed_hull_polygon.exterior.coords) - 1):
    p1 = processed_hull_polygon.exterior.coords[i]
    p2 = processed_hull_polygon.exterior.coords[i + 1]
    edge = LineString([p1, p2])
    edge_length = edge.length
    mid_point = np.average([p1, p2], axis=0)
    plt.text(mid_point[0], mid_point[1], f'{edge_length:.2f}', color='blue', fontsize=9)

# 在图中标注多边形的面积
plt.text(min(x_processed), min(y_processed), f"Area: {processed_hull_area:.2f} sq units", fontsize=12, color='blue')

# 设置图表的其他属性
plt.xlabel('X Coordinate')
plt.ylabel('Y Coordinate')
plt.title('Comparison of Convex Hull Before and After Processing')
plt.legend()

# 显示图表
plt.show()