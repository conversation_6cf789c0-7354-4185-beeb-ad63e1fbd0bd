import numpy as np
import pandas as pd
from scipy.spatial import ConvexHull
from shapely.geometry import LineString, Point
import matplotlib.pyplot as plt

# 读取CSV文件并转换为NumPy数组的函数
def load_points_from_csv(file_path):
    df = pd.read_csv(file_path)
    return df[['X', 'Y']].values

# 判断点是否在边的垂直投影内的函数
def is_within_projection(point, edge_start, edge_end):
    edge_line = LineString([edge_start, edge_end])
    point = Point(point)

    # 创建垂直线段
    length = 1000  # 假设垂直线段的长度
    dx = edge_end[0] - edge_start[0]
    dy = edge_end[1] - edge_start[1]
    normal_vector = (-dy, dx)
    unit_vector = normal_vector / np.linalg.norm(normal_vector)
    vertical_line_end = (point.x + unit_vector[0] * length, point.y + unit_vector[1] * length)
    vertical_line = LineString([point, vertical_line_end])

    # 检查垂直线段与边的交点
    intersection = edge_line.intersection(vertical_line)
    return not intersection.is_empty

# 计算点到边的最近距离的函数
def distance_to_edge(point, edge_start, edge_end):
    edge_line = LineString([edge_start, edge_end])
    point = Point(point)
    return point.distance(edge_line)

# 投影法处理凸包边缘的函数
def projection_method(points, min_edge_length=20):
    convex_hull = ConvexHull(points)
    hull_points = points[convex_hull.vertices]
    remaining_points = set(map(tuple, points)) - set(map(tuple, hull_points))
    new_hull_points = list(map(tuple, hull_points))

    for i in range(len(hull_points)):
        edge_start, edge_end = hull_points[i], hull_points[(i + 1) % len(hull_points)]
        if np.linalg.norm(edge_start - edge_end) > min_edge_length:
            closest_point = None
            closest_distance = float('inf')
            for point in remaining_points:
                if is_within_projection(point, edge_start, edge_end):
                    distance = distance_to_edge(point, edge_start, edge_end)
                    if distance < closest_distance:
                        closest_distance = distance
                        closest_point = point
            if closest_point:
                insert_index = new_hull_points.index(edge_start) + 1
                new_hull_points.insert(insert_index, closest_point)
                remaining_points.remove(closest_point)

    return np.array(new_hull_points)

# 主函数
def main():
    try:
        file_path = '../Figure_12.csv'  # 请确保路径正确
        all_points = load_points_from_csv(file_path)
        new_hull_points = projection_method(all_points)

        plt.scatter(all_points[:, 0], all_points[:, 1], label='Original Points')
        new_hull_polygon = ConvexHull(new_hull_points)
        for simplex in new_hull_polygon.simplices:
            plt.plot(new_hull_points[simplex, 0], new_hull_points[simplex, 1], 'k-')
        plt.xlabel('X coordinate')
        plt.ylabel('Y coordinate')
        plt.legend()
        plt.show()
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == '__main__':
    main()
