import csv
import copy
import math
from shapely.geometry import Polygon, Point, LineString
import matplotlib.pyplot as plt


# 1. 读取坐标点数据
def read_coordinates_from_csv(file_path):
    points = []
    try:
        with open(file_path, 'r') as file:
            reader = csv.reader(file)
            next(reader)
            for row in reader:
                x, y = float(row[0]), float(row[1])
                points.append((x, y))
    except FileNotFoundError:
        print(f"文件 {file_path} 未找到，请检查文件路径是否正确。")
        return None
    return points


# 使用函数读取坐标点数据
file_path = '../Figure_12.csv'  # 确保此处的文件路径是正确的
points = read_coordinates_from_csv(file_path)
if points is not None:
    def calculate_convex_hull(points):
        hull = Polygon(points).convex_hull
        return hull


    hull_vertices = calculate_convex_hull(points)
    new_vertices = []  # 存储因投影操作而新增的顶点


    # 4. 对每条边进行投影处理
    def project_and_add_vertices(hull_vertices):
        min_length = 20  # 预设的最小长度，单位：米
        for i, coord in enumerate(hull_vertices.exterior.coords):
            p1 = Point(coord) if i == 0 else Point(hull_vertices.exterior.coords[i - 1])
            p2 = Point(hull_vertices.exterior.coords[(i + 1) % len(hull_vertices.exterior.coords)])
            distance = p1.distance(p2)
            if distance > min_length:
                polygon_points = [point for point in points if
                                  not hull_vertices.contains(point) and point not in new_vertices]
                distances = [point.distance(LineString([p1, p2])) for point in polygon_points]
                min_distance_idx = distances.index(min(distances))
                nearest_point = polygon_points[min_distance_idx]
                segment = LineString([p1, p2])
                if segment.contains(nearest_point) and segment.distance(nearest_point) < min_length:
                    insert_idx = (i if (nearest_point - p1).length > (p2 - nearest_point).length else i - 1) % len(
                        hull_vertices)
                    hull_vertices.insert(insert_idx, nearest_point)
                    new_vertices.append(nearest_point)
                    break  # 添加新顶点后，重新开始处理边


    # 5. 更新凸包顶点列表 和 6. 重复投影处理
    while True:
        hull_vertices_copy = copy.deepcopy(hull_vertices)
        project_and_add_vertices(hull_vertices_copy)
        if not new_vertices:
            break  # 如果没有新的顶点被添加，结束循环
        hull_vertices = hull_vertices_copy.copy()
        hull_vertices.extend(new_vertices)  # 将新顶点添加到凸包顶点列表
        new_vertices.clear()  # 清空新顶点列表，为下一轮迭代做准备

    # 7. 计算和展示最终结果
    final_polygon = Polygon(hull_vertices)
    original_area = Polygon(points).area
    projected_area = final_polygon.area
    print(f"原始面积: {original_area}, 投影后的面积: {projected_area}")
    plt.scatter([p[0] for p in points], [p[1] for p in points], c='r', label='原始坐标点')
    plt.scatter([p[0] for p in hull_vertices], [p[1] for p in hull_vertices], c='b', label='最终多边形顶点')
    final_polygon.plot(alpha=0.5)
    plt.legend()
    plt.show()