from math import radians, sin, cos


def calculate_spherical_polygon_area(coords, radius=6371000):
    """
    计算球面多边形的面积。
    :param coords: 经纬度坐标点列表，每个点是 (经度, 纬度) 格式。
    :param radius: 地球半径，默认为 6371000 米。
    :return: 多边形的面积，单位为平方米。
    """
    # 将角度转换为弧度
    coords = [(radians(lon), radians(lat)) for lon, lat in coords]

    # 计算多边形面积
    total = 0.0
    for i in range(len(coords)):
        lon1, lat1 = coords[i]
        lon2, lat2 = coords[(i + 1) % len(coords)]
        total += (lon2 - lon1) * (2 + sin(lat1) + sin(lat2))
    area = abs(total * radius ** 2 / 2)

    return area


# 示例数据：经纬度坐标点
data = [
 (125.98757, 49.107422),
 (125.990701, 49.106356),
 (125.994927, 49.106256),
 (125.995589, 49.106022),
 (125.996022, 49.105372),
 (125.996277, 49.104322),
 (125.995946, 49.102089),
 (125.983726, 49.099105),
 (125.978506, 49.105356),
 (125.982962, 49.106506)
]

# 计算面积
area = calculate_spherical_polygon_area(data)
print("多边形面积（平方米）:", area)
