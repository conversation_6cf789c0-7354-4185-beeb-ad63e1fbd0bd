import pandas as pd
import numpy as np
from scipy.spatial import Delaunay
from shapely.ops import unary_union, polygonize
from shapely.geometry import Polygon, LineString
import matplotlib.pyplot as plt
from math import radians, cos, sin, asin, sqrt

# 导入经纬度坐标
file_path = '20221016_1.csv'
df = pd.read_csv(file_path)

# 将DataFrame转换为NumPy数组
points = df[['longitude', 'latitude']].to_numpy()

def calculate_spherical_polygon_area(coords, radius=6371000):
    """
    计算球面多边形的面积。
    :param coords: 经纬度坐标点列表，每个点是 (经度, 纬度) 格式。
    :param radius: 地球半径，默认为 6371000 米。
    :return: 多边形的面积，单位为平方米。
    """
    # 将角度转换为弧度
    coords = [(radians(lon), radians(lat)) for lon, lat in coords]

    # 计算多边形面积
    total = 0.0
    for i in range(len(coords)):
        lon1, lat1 = coords[i]
        lon2, lat2 = coords[(i + 1) % len(coords)]
        total += (lon2 - lon1) * (2 + sin(lat1) + sin(lat2))
    area = abs(total * radius ** 2 / 2)

    return area

# Haversine公式，用于计算两个经纬度点之间的距离
def haversine(lon1, lat1, lon2, lat2):
    # 将十进制度数转换为弧度
    lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])
    # 计算经度和纬度的差值
    dlon = lon2 - lon1
    dlat = lat2 - lat1
    # Haversine公式
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * asin(sqrt(a))
    r = 6371000  # 地球半径, 单位为米
    return c * r

# 定义函数来检查三角形的边长是否满足条件
def valid_triangle(triangle, points, max_length=26):  # 单位为米
    """检查三角形的边是否都小于指定的最大长度（考虑球面距离）"""
    for i in range(3):
        point1 = points[triangle[i]]
        point2 = points[triangle[(i + 1) % 3]]
        if haversine(point1[0], point1[1], point2[0], point2[1]) > max_length:
            return False
    return True

# 定义函数，用于提取最外圈的边
def extract_outer_edges(triangles, points):
    """从三角形集合中提取构成最外侧闭合多边形的边缘"""
    edge_count = {}
    for tri in triangles:
        for i in range(3):
            edge = tuple(sorted([tri[i], tri[(i + 1) % 3]]))
            edge_count[edge] = edge_count.get(edge, 0) + 1

    outer_edges = [edge for edge, count in edge_count.items() if count == 1]
    return outer_edges

# 修改 line_length 函数以使用 Haversine 公式
def line_length(line):
    """计算线段的长度（考虑球面距离）"""
    p1, p2 = line.coords[0], line.coords[1]
    return haversine(p1[0], p1[1], p2[0], p2[1])

# 执行Delaunay三角剖分
tri = Delaunay(points)

# 筛选满足条件的三角形
valid_triangles = [t for t in tri.simplices if valid_triangle(t, points)]

# 提取最外圈的边
outer_edges = extract_outer_edges(valid_triangles, points)

# 提取最外圈的边并构建LineString对象
edge_lines = [LineString([points[edge[0]], points[edge[1]]]) for edge in outer_edges]

# 合并所有LineString对象，形成一个连续的轮廓
merged_lines = unary_union(edge_lines)

# 从连续轮廓中构建多边形
outer_polygon = list(polygonize(merged_lines))[0]  # 取第一个多边形，即最外围的多边形

# 获取外围多边形的顶点坐标点
outer_polygon_coords = list(outer_polygon.exterior.coords)

# 准备绘图
plt.figure(figsize=(8, 6))

# 绘制Delaunay三角剖分的三角形
for triangle in valid_triangles:
    triangle_points = points[triangle]
    plt.fill(triangle_points[:, 0], triangle_points[:, 1], color='lightgreen', alpha=0.3, zorder=1)

# 绘制所有的内部坐标点，使用蓝色的小x表示
plt.scatter(points[:, 0], points[:, 1], color='blue', marker='x', linewidths=0.5, zorder=2)

# 绘制外围多边形的边线，使用蓝色
plt.plot(*outer_polygon.exterior.xy, color='blue', zorder=3)

# # 标记外围多边形的顶点，使用红色点
# plt.scatter(*outer_polygon.exterior.xy, color='red', zorder=4)

# 获取当前坐标轴界限
x_min, x_max, y_min, y_max = plt.axis()

# 计算并显示外围多边形的面积（单位为平方米）
# 使用前面定义的计算球面多边形面积的函数
outer_polygon_area_m2 = calculate_spherical_polygon_area(outer_polygon_coords)
plt.text(x_max, y_min, f'Area: {outer_polygon_area_m2:.2f} m²', ha='right', va='bottom', color='blue', zorder=5)

# 只显示外围多边形的边长
for i, coord in enumerate(outer_polygon.exterior.coords[:-1]):  # 最后一个点是重复的起点
    p1 = coord
    p2 = outer_polygon.exterior.coords[i + 1]
    edge_line = LineString([p1, p2])
    length = line_length(edge_line)
    mid_point = np.average([p1, p2], axis=0)
    plt.text(mid_point[0], mid_point[1], f'{length:.2f}', color='red', zorder=6)

# 设置图表的标题和轴标签
plt.xlabel('X Coordinate')
plt.ylabel('Y Coordinate')
plt.title('Outer Polygon with Area and Correct Edge Lengths Displayed')
plt.show()
