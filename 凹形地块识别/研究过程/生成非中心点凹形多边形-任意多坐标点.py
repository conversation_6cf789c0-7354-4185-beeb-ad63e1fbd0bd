import numpy as np
import matplotlib.pyplot as plt
from scipy.spatial import ConvexHull
from shapely.geometry import Point, Polygon
import pandas as pd


def generate_irregular_polygon(num_vertices, concavity=0.8):
    points = np.random.rand(num_vertices, 2) * 100
    hull = ConvexHull(points)
    hull_points = points[hull.vertices]
    irregular_points = []
    for i in range(len(hull_points)):
        start, end = hull_points[i], hull_points[(i + 1) % len(hull_points)]
        # 选择边上的一个随机点而非中点
        random_factor = np.random.rand()
        random_point = start + random_factor * (end - start)
        vector = end - start
        normal = np.array([-vector[1], vector[0]])
        normal /= np.linalg.norm(normal)
        deformation = random_point + normal * np.random.rand() * concavity * np.linalg.norm(vector) * random_factor
        irregular_points.append(start)
        irregular_points.append(deformation)
    return np.array(irregular_points)


def generate_random_points_within_polygon(polygon, num_points):
    min_x, min_y, max_x, max_y = polygon.bounds
    points = []
    while len(points) < num_points:
        random_point = Point(np.random.uniform(min_x, max_x), np.random.uniform(min_y, max_y))
        if polygon.contains(random_point):
            points.append(random_point)
    return np.array([(point.x, point.y) for point in points])

num_vertices = 10
irregular_polygon = generate_irregular_polygon(num_vertices, concavity=0.5)
polygon = Polygon(irregular_polygon)

num_inner_points = 5000  # 可以调整生成的点的数量
inner_points = generate_random_points_within_polygon(polygon, num_inner_points)

points_df = pd.DataFrame(np.vstack((irregular_polygon, inner_points)), columns=['X', 'Y'])
csv_file_path = 'polygon_and_inner_points9.csv'
points_df.to_csv(csv_file_path, index=False)

plt.figure(figsize=(10, 8))
plt.plot(*polygon.exterior.xy, label='Irregular Polygon')
plt.scatter(inner_points[:, 0], inner_points[:, 1], color='green', marker='o', label='Inner Points')
plt.scatter(irregular_polygon[:, 0], irregular_polygon[:, 1], color='red', marker='x', label='Polygon Vertices')
plt.xlim(0, 100)
plt.ylim(0, 100)
plt.title('Irregular Polygon with Inner Points')
plt.xlabel('X Coordinate')
plt.ylabel('Y Coordinate')
plt.legend()
plt.grid(True)
plt.show()

