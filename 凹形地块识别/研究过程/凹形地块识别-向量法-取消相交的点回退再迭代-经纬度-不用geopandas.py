import pandas as pd
import numpy as np
from scipy.spatial import ConvexHull
from shapely.geometry import Polygon, LineString
import matplotlib.pyplot as plt
from math import radians, cos, sin, asin, sqrt


# 球面多边形面积计算公式
def calculate_spherical_polygon_area(coords, radius=6371000):
    """
    计算球面多边形的面积。
    :param coords: 经纬度坐标点列表，每个点是 (经度, 纬度) 格式。
    :param radius: 地球半径，默认为 6371000 米。
    :return: 多边形的面积，单位为平方米。
    """
    # 将角度转换为弧度
    coords = [(radians(lon), radians(lat)) for lon, lat in coords]

    # 计算多边形面积
    total = 0.0
    for i in range(len(coords)):
        lon1, lat1 = coords[i]
        lon2, lat2 = coords[(i + 1) % len(coords)]
        total += (lon2 - lon1) * (2 + sin(lat1) + sin(lat2))
    area = abs(total * radius ** 2 / 2)

    return area

# Haversine公式，用于计算两个经纬度点之间的距离
def haversine(lon1, lat1, lon2, lat2):
    lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])
    dlon = lon2 - lon1
    dlat = lat2 - lat1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * asin(sqrt(a))
    r = 6371000  # 地球半径, 单位为米
    return c * r

# 导入经纬度坐标
file_path = 'converted_coordinates.csv'
df = pd.read_csv(file_path)

# 将DataFrame转换为NumPy数组
points = df[['longitude', 'latitude']].to_numpy()

# 第2步,计算凸包
hull = ConvexHull(points)
hull_polygon = Polygon([points[vertex] for vertex in hull.vertices])

# 寻找距离凸包边最近的内部点的函数
def find_closest_point_on_edge_vectorized(edge, points):
    p1, p2 = edge.coords[0], edge.coords[1]
    edge_vector = np.array(p2) - np.array(p1)
    edge_length = np.linalg.norm(edge_vector)

    min_distance = np.inf
    closest_point = None

    for point in points:
        point_vector = np.array(point) - np.array(p1)
        dot = np.dot(point_vector, edge_vector)
        if 0 <= dot <= np.dot(edge_vector, edge_vector):
            proj_point = np.array(p1) + dot / np.dot(edge_vector, edge_vector) * edge_vector
            distance = np.linalg.norm(proj_point - np.array(point))
            if distance < min_distance:
                # 检查新形成的边是否比原边短
                new_edge_1 = LineString([edge.coords[0], point])
                new_edge_2 = LineString([point, edge.coords[1]])
                if new_edge_1.length < edge_length and new_edge_2.length < edge_length:
                    min_distance = distance
                    closest_point = point

    return closest_point

# 转换凸包顶点为元组形式，以便后续比较
hull_vertices = [tuple(vertex) for vertex in hull_polygon.exterior.coords]

def check_edges_intersection(vertices):
    """检查多边形的边是否相交"""
    for i in range(len(vertices)):
        line1 = LineString([vertices[i], vertices[(i + 1) % len(vertices)]])
        for j in range(i + 2, len(vertices) - (1 if i == 0 else 0)):
            line2 = LineString([vertices[j], vertices[(j + 1) % len(vertices)]])
            if line1.intersects(line2):
                return True
    return False

def process_projection(hull, points, hull_polygon):
    processed_hull = hull_polygon
    iteration_count = 0
    excluded_points = set()  # 记录因导致相交而被排除的点

    while True:
        iteration_count += 1
        print(f"Iteration: {iteration_count}")

        new_vertices = list(processed_hull.exterior.coords)[:-1]
        changes_made = False
        intersection_occurred = False

        i = 0
        while i < len(new_vertices):
            p1 = new_vertices[i]
            p2 = new_vertices[(i + 1) % len(new_vertices)]
            edge = LineString([p1, p2])  # 确保 edge 在这里被定义
            edge_length = haversine(p1[0], p1[1], p2[0], p2[1])

            if edge_length > 100:
                # 将 new_vertices 中的点转换为元组形式，确保可以正确从 points 中移除
                numpy_points = np.array(list(set(map(tuple, points)) - set(map(tuple, new_vertices)) - excluded_points))
                # 对于每条边，不断寻找最近的点，直到没有更多点或发生了相交
                while True:
                    closest_point = find_closest_point_on_edge_vectorized(edge, numpy_points)

                    if closest_point is not None:
                        temp_vertices = new_vertices.copy()
                        temp_vertices.insert((i + 1) % len(new_vertices), closest_point)

                        # 检查添加后是否有边缘相交
                        if check_edges_intersection(temp_vertices):
                            excluded_points.add(tuple(closest_point))  # 将 numpy.ndarray 转换为元组
                            # 更新 numpy_points 以排除当前点
                            numpy_points = np.array(list(set(map(tuple, numpy_points)) - {tuple(closest_point)}))
                        else:
                            # 插入点到多边形中，并移动到下一条边
                            new_vertices.insert((i + 1) % len(new_vertices), closest_point)
                            changes_made = True
                            i += 2  # 跳过新插入的点
                            break
                    else:
                        # 没有找到最近点，处理下一条边
                        i += 1
                        break
            else:
                i += 1

        if not changes_made:
            print("No changes made in this iteration. Ending the process.")
            break

        processed_hull = Polygon(new_vertices)

        # 如果本轮迭代没有发生相交，清空排除点列表
        if not intersection_occurred:
            excluded_points.clear()

    return processed_hull

# 以下代码为调用上述函数并显示结果的部分

# 执行投影处理
processed_hull_polygon = process_projection(hull, points, hull_polygon)

# 获取最终多边形的坐标点
final_polygon_coords = list(processed_hull_polygon.exterior.coords)

# 更新的绘图部分
plt.figure(figsize=(12, 8))

# 绘制原始凸包
x_hull, y_hull = hull_polygon.exterior.xy
plt.plot(x_hull, y_hull, 'g--', label='Original Convex Hull')

# 获取最终多边形的坐标点并计算面积
final_polygon_coords = list(processed_hull_polygon.exterior.coords)
polygon_area = calculate_spherical_polygon_area(final_polygon_coords)

# 绘制处理后的多边形
x_processed, y_processed = processed_hull_polygon.exterior.xy
plt.plot(x_processed, y_processed, 'b-', label='Processed Polygon')

# 使用红色标记绘制多边形顶点
plt.scatter(x_processed, y_processed, color='red', marker='o', label='Polygon Vertices')

# 使用蓝色“x”标记绘制内部点，可以通过调整linewidths参数来改变标记的粗细
plt.scatter(points[:, 0], points[:, 1], color='blue', marker='x', linewidths=0.5, label='Internal Points')

# 计算并在图上标注处理后多边形边的长度
for i in range(len(final_polygon_coords) - 1):
    p1 = final_polygon_coords[i]
    p2 = final_polygon_coords[i + 1]
    edge_length = haversine(p1[0], p1[1], p2[0], p2[1])
    mid_point = np.average([p1, p2], axis=0)
    plt.text(mid_point[0], mid_point[1], f'{edge_length:.2f} m', color='purple', fontsize=9)

# 使用球面多边形面积计算公式计算面积
final_polygon_coords = [(lon, lat) for lon, lat in zip(x_processed, y_processed)]
processed_hull_area = calculate_spherical_polygon_area(final_polygon_coords)

# 在图上标注多边形面积
plt.text(min(x_processed), min(y_processed), f"Area: {polygon_area:.2f} sq units", fontsize=12, color='blue')

# 设置图表的其他属性
plt.xlabel('X Coordinate')
plt.ylabel('Y Coordinate')
plt.title('Comparison of Convex Hull Before and After Processing')
plt.legend()

# 显示图表
plt.show()
