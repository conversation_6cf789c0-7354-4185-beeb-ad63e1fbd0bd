import pandas as pd
import json

# 替换为您的JSON文件的路径
file_path = 'grid686.json'

# 使用JSON库读取文件内容
with open(file_path, 'r') as file:
    data = json.load(file)

# 提取每个嵌套列表的第一个元素
flattened_data = [item[0] for item in data]

# 将数据转换为DataFrame
df = pd.DataFrame(flattened_data, columns=['lon', 'lat'])

# 打印DataFrame的前几行以确认数据
print(df.head())

# 将DataFrame导出为CSV文件
output_file_path = 'converted_coordinates.csv'
df.to_csv(output_file_path, index=False)

output_file_path
