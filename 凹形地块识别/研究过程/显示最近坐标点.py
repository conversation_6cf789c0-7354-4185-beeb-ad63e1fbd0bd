# 导入所需库
import pandas as pd
import numpy as np
from scipy.spatial import ConvexHull
from shapely.geometry import Polygon, LineString
import matplotlib.pyplot as plt

# 读取CSV文件
file_path = 'Figure_12.csv'
df = pd.read_csv(file_path)

# 将DataFrame转换为NumPy数组
points = df.to_numpy()

# 计算凸包
hull = ConvexHull(points)
hull_polygon = Polygon([points[vertex] for vertex in hull.vertices])

# 寻找距离凸包边最近的内部点的函数
def find_closest_point_on_edge_vectorized(edge, points):
    p1, p2 = edge.coords[0], edge.coords[1]
    edge_vector = np.array(p2) - np.array(p1)

    min_distance = np.inf
    closest_point = None

    for point in points:
        point_vector = np.array(point) - np.array(p1)
        dot = np.dot(point_vector, edge_vector)
        if 0 <= dot <= np.dot(edge_vector, edge_vector):
            proj_point = np.array(p1) + dot / np.dot(edge_vector, edge_vector) * edge_vector
            distance = np.linalg.norm(proj_point - np.array(point))
            if distance < min_distance:
                min_distance = distance
                closest_point = point

    return closest_point

# 排除凸包顶点
points_excluding_hull_vertices = np.array(list(set(map(tuple, points)) - set(map(tuple, hull_polygon.exterior.coords))))

# 寻找距离凸包边最近的内部点（排除凸包顶点）
closest_points_excluding_hull_vertices = []
for simplex in hull.simplices:
    edge = LineString([points[simplex[0]], points[simplex[1]]])
    closest_point = find_closest_point_on_edge_vectorized(edge, points_excluding_hull_vertices)
    if closest_point is not None:
        closest_points_excluding_hull_vertices.append(closest_point)

# 转换为NumPy数组，以便于绘图
closest_points_np_excluding_hull = np.array(closest_points_excluding_hull_vertices)

# 绘制图形
plt.figure(figsize=(10, 8))

# 绘制原始点
plt.scatter(points[:, 0], points[:, 1], label='Original Points')

# 绘制凸包
for simplex in hull.simplices:
    plt.plot(points[simplex, 0], points[simplex, 1], 'k-')

# 绘制找到的最近点（排除凸包顶点）
if len(closest_points_np_excluding_hull) > 0:
    plt.scatter(closest_points_np_excluding_hull[:, 0], closest_points_np_excluding_hull[:, 1], color='red', label='Closest Points Excluding Hull Vertices')

plt.xlabel('X Coordinate')
plt.ylabel('Y Coordinate')
plt.title('Closest Points on Convex Hull Edges (Excluding Hull Vertices)')
plt.legend()
plt.show()