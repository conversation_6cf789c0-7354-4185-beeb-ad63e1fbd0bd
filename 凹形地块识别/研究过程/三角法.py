import pandas as pd
import numpy as np
from scipy.spatial import <PERSON><PERSON>nay
from shapely.geometry import Polygon, LineString
from shapely.ops import unary_union, polygonize
import matplotlib.pyplot as plt

# 定义函数来检查三角形的边长是否满足条件
def valid_triangle(triangle, points, max_length=10):
    """检查三角形的边是否都小于指定的最大长度"""
    for i in range(3):
        point1 = points[triangle[i]]
        point2 = points[triangle[(i + 1) % 3]]
        if np.linalg.norm(point1 - point2) > max_length:
            return False
    return True

# 定义函数，用于提取最外圈的边
def extract_outer_edges(triangles, points):
    """从三角形集合中提取构成最外侧闭合多边形的边缘"""
    edge_count = {}
    for tri in triangles:
        for i in range(3):
            edge = tuple(sorted([tri[i], tri[(i + 1) % 3]]))
            edge_count[edge] = edge_count.get(edge, 0) + 1

    outer_edges = [edge for edge, count in edge_count.items() if count == 1]
    return outer_edges

# 定义函数，用于计算线段的长度
def line_length(line):
    """计算线段的长度"""
    return np.linalg.norm(np.array(line.coords[0]) - np.array(line.coords[1]))

# 读取CSV文件中的坐标点
file_path = 'polygon_and_inner_points3.csv'  # 文件路径
points_df = pd.read_csv(file_path)
points = points_df.to_numpy()

# 执行Delaunay三角剖分
tri = Delaunay(points)

# 筛选满足条件的三角形
valid_triangles = [t for t in tri.simplices if valid_triangle(t, points)]

# 提取最外圈的边
outer_edges = extract_outer_edges(valid_triangles, points)

# 提取最外圈的边并构建LineString对象
edge_lines = [LineString([points[edge[0]], points[edge[1]]]) for edge in outer_edges]

# 合并所有LineString对象，形成一个连续的轮廓
merged_lines = unary_union(edge_lines)

# 从连续轮廓中构建多边形
outer_polygon = list(polygonize(merged_lines))[0]  # 取第一个多边形，即最外围的多边形

# 获取外围多边形的顶点坐标点
outer_polygon_coords = list(outer_polygon.exterior.coords)

# 创建一个新的多边形对象
outer_polygon_from_vertices = Polygon(outer_polygon_coords)

# 计算新多边形的面积
outer_polygon_from_vertices_area = outer_polygon_from_vertices.area

# 准备绘图
plt.figure(figsize=(8, 6))

# 绘制Delaunay三角剖分的三角形
for triangle in valid_triangles:
    triangle_points = points[triangle]
    plt.fill(triangle_points[:, 0], triangle_points[:, 1], color='lightgreen', alpha=0.3, zorder=1)

# 绘制所有的内部坐标点，使用蓝色的小x表示
plt.scatter(points[:, 0], points[:, 1], color='blue', marker='x', zorder=2)

# 绘制外围多边形的边线，使用蓝色
plt.plot(outer_polygon_from_vertices.exterior.xy[0], outer_polygon_from_vertices.exterior.xy[1], color='blue', zorder=3)

# 标记外围多边形的顶点，使用红色点
plt.scatter(outer_polygon_from_vertices.exterior.xy[0], outer_polygon_from_vertices.exterior.xy[1], color='red', zorder=4)

# 获取当前坐标轴界限
x_min, x_max, y_min, y_max = plt.axis()

# 显示面积在图形的右下方
plt.text(x_max, y_min, f'Area: {outer_polygon_from_vertices_area:.2f}', ha='right', va='bottom', color='blue', zorder=5)

# 只显示外围多边形的边长
for i, coord in enumerate(outer_polygon_from_vertices.exterior.coords[:-1]):  # 最后一个点是重复的起点
    p1 = coord
    p2 = outer_polygon_from_vertices.exterior.coords[i + 1]
    edge_line = LineString([p1, p2])
    length = line_length(edge_line)
    mid_point = np.average([p1, p2], axis=0)
    plt.text(mid_point[0], mid_point[1], f'{length:.2f}', color='red', zorder=6)

# 设置图表的标题和轴标签
plt.xlabel('X Coordinate')
plt.ylabel('Y Coordinate')
plt.title('Outer Polygon with Area and Correct Edge Lengths Displayed')
plt.show()
