import pandas as pd
import numpy as np
from scipy.spatial import ConvexHull
from shapely.geometry import Polygon, LineString, Point
import matplotlib.pyplot as plt

# 第1步,读取CSV文件
file_path = 'polygon_and_edge_points.csv'
df = pd.read_csv(file_path)

# 将DataFrame转换为NumPy数组
points = df.to_numpy()

# 第2步,计算凸包
hull = ConvexHull(points)
hull_polygon = Polygon([points[vertex] for vertex in hull.vertices])

# 第3步,找出距离边最近的坐标点,确认为新增多边形顶点
# 创建垂直于边的直线的函数
def create_perpendicular_line(point, line):
    # 计算线的斜率，用于确定垂线的方向
    dx = line.xy[0][1] - line.xy[0][0]
    dy = line.xy[1][1] - line.xy[1][0]
    # 处理垂直和水平线的特殊情况
    if dx == 0:  # 原线垂直
        return LineString([(point[0], point[1] - 1000), (point[0], point[1] + 1000)])
    elif dy == 0:  # 原线水平
        return LineString([(point[0] - 1000, point[1]), (point[0] + 1000, point[1])])
    else:
        # 对于一般情况，计算垂线的斜率并构造一条线
        perp_slope = -dx / dy
        x1, y1 = point[0] - 1000, point[1] - 1000 * perp_slope
        x2, y2 = point[0] + 1000, point[1] + 1000 * perp_slope
        return LineString([(x1, y1), (x2, y2)])

# 寻找距离凸包边最近的内部点的函数
def find_closest_point_on_edge(edge, points, hull_vertices):
    closest_point = None
    min_distance = float('inf')
    # 遍历所有点，寻找距离当前边最近的点
    for point in points:
        point_obj = Point(point)
        if point_obj.within(hull_polygon) and not point_obj.equals(Point(edge.coords[0])) and not point_obj.equals(Point(edge.coords[1])):
            perp_line = create_perpendicular_line(point, edge)
            # 检查垂直线是否与边相交
            if perp_line.intersects(edge):
                distance = point_obj.distance(edge)
                if distance < min_distance:
                    min_distance = distance
                    closest_point = point
    return closest_point

# 转换凸包顶点为元组形式，以便后续比较
hull_vertices = [tuple(vertex) for vertex in hull_polygon.exterior.coords]

# 特定边，用于后续的测试和高亮显示
test_edge_coords = [(85.37977602, 17.71462102), (8.64174297, 30.18582432)]
test_edge = LineString(test_edge_coords)

# 找到距离特定测试边最近的内部点
closest_point = find_closest_point_on_edge(test_edge, points, hull_vertices)

# 绘图展示
plt.figure()
plt.plot(points[:, 0], points[:, 1], 'o', label='Points')
x, y = hull_polygon.exterior.xy
plt.plot(x, y, 'g', label='Convex Hull')

# 高亮显示测试边附近的最近内部点
if closest_point is not None:
    plt.plot(*closest_point, 'ro', markersize=10, label='Closest Inner Point on Test Edge')

plt.xlabel('X Coordinate')
plt.ylabel('Y Coordinate')
plt.title('Closest Inner Points to Test Edge')
plt.legend()
plt.show()
