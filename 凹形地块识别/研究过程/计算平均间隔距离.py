# 完整的Python代码 - 计算平均间隔和平均速度

from math import radians, cos, sin, asin, sqrt
import pandas as pd


def haversine(lon1, lat1, lon2, lat2):
    """
    计算两个经纬度坐标之间的距离。
    参数:
    lon1, lat1 -- 第一个坐标的经度和纬度
    lon2, lat2 -- 第二个坐标的经度和纬度
    返回值:
    两点间的距离（米）
    """
    # 将十进制度数转换为弧度
    lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])

    # haversine公式
    dlon = lon2 - lon1
    dlat = lat2 - lat1
    a = sin(dlat / 2) ** 2 + cos(lat1) * cos(lat2) * sin(dlon / 2) ** 2
    c = 2 * asin(sqrt(a))
    r = 6371  # 地球平均半径，单位为公里
    return c * r * 1000  # 返回单位为米的结果


# 读取文件
file_path = '3.7万个时间坐标点.csv'
data = pd.read_csv(file_path)

# 确保时间列的格式是正确的
data = pd.read_csv(file_path, parse_dates=['datetime'], dayfirst=False)

# 初始化距离和时间差列表
distances = []
time_diffs = []

# 遍历数据，计算每对相邻坐标之间的距离和时间差
for i in range(len(data) - 1):
    lon1, lat1, time1 = data.iloc[i][['longitude', 'latitude', 'datetime']]
    lon2, lat2, time2 = data.iloc[i + 1][['longitude', 'latitude', 'datetime']]

    # 计算距离
    distance = haversine(lon1, lat1, lon2, lat2)
    distances.append(distance)

    # 计算时间差（秒）
    time_diff = (time2 - time1).total_seconds()
    time_diffs.append(time_diff)

# 重新计算速度、平均距离、平均速度、总长度和坐标点数量
speeds = [d / t if t != 0 else 0 for d, t in zip(distances, time_diffs)]
average_distance = sum(distances) / len(distances)
average_speed = sum(speeds) / len(speeds)
total_length = sum(distances)
num_points = len(data)
# 计算总时间（第一个点和最后一个点之间的时间差，秒）
total_time = (data.iloc[-1]['datetime'] - data.iloc[0]['datetime']).total_seconds()

# 计算总速度（总长度除以总时间）
total_speed = total_length / total_time if total_time != 0 else 0

# 使用 print 函数来显示所有计算结果
print("平均间隔距离：", average_distance, "米")
print("平均速度：", average_speed, "米/秒")
print("总长度：", total_length, "米")
print("坐标点数量：", num_points, "个")
print("总时间：", total_time, "秒")
print("总速度（总长度/总时间）：", total_speed, "米/秒")
