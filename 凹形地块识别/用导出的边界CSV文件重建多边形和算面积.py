import pandas as pd
import matplotlib.pyplot as plt
from math import radians, sin, cos, sqrt, asin
from shapely.geometry import Polygon
from shapely.ops import unary_union

# 定义函数，使用Haversine公式计算两点之间的地球表面距离
def haversine(lon1, lat1, lon2, lat2):
    EARTH_RADIUS = 6371000  # 地球半径，米
    lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])  # 将角度转换为弧度
    dlon = lon2 - lon1
    dlat = lat2 - lat1
    a = sin(dlat / 2) ** 2 + cos(lat1) * cos(lat2) * sin(dlon / 2) ** 2
    c = 2 * asin(sqrt(a))
    return EARTH_RADIUS * c  # 返回计算的距离

# 定义函数，用于计算球面多边形的面积
def calculate_spherical_polygon_area(coords, radius=6371000):
    coords = [(radians(lon), radians(lat)) for lon, lat in coords]  # 将经纬度转换为弧度
    total = 0.0
    for i in range(len(coords)):
        lon1, lat1 = coords[i]
        lon2, lat2 = coords[(i + 1) % len(coords)]  # 循环列表实现闭环
        total += (lon2 - lon1) * (2 + sin(lat1) + sin(lat2))
    return abs(total * radius ** 2 / 2)

# 读取新的 CSV 文件
file_path = 'extracted_edges.csv'
df = pd.read_csv(file_path)

# 提取外圈和内圈的坐标点
outer_coords = df[df['type'] == 'outer'][['longitude', 'latitude']].to_numpy()
inner_coords_list = df[df['type'] == 'inner']

# 计算外圈周长和面积
outer_perimeter = sum(haversine(outer_coords[i][0], outer_coords[i][1], outer_coords[(i + 1) % len(outer_coords)][0], outer_coords[(i + 1) % len(outer_coords)][1]) for i in range(len(outer_coords)))
outer_area = calculate_spherical_polygon_area(outer_coords)

# 计算所有内圈的面积
inner_areas = []
inner_polygons = []
inner_groups = inner_coords_list['group'].unique()
for group in inner_groups:
    group_coords = inner_coords_list[inner_coords_list['group'] == group][['longitude', 'latitude']].to_numpy()
    if len(group_coords) > 0:
        inner_area = calculate_spherical_polygon_area(group_coords)
        inner_areas.append(inner_area)
        inner_polygons.append(Polygon(group_coords))

total_inner_area = sum(inner_areas)
net_area = outer_area - total_inner_area

# 绘制地图
plt.figure(figsize=(10, 10))

# 绘制外圈边界
if len(outer_coords) > 0:
    outer_lon, outer_lat = zip(*outer_coords)
    plt.plot(outer_lon, outer_lat, 'r-', label='Outer Boundary', zorder=5)

# 绘制内圈边界
for group, poly in zip(inner_groups, inner_polygons):
    x, y = poly.exterior.xy
    plt.plot(x, y, 'r--', label=f'Inner Boundary {group}', zorder=5)

# 填充外圈和内圈之间的区域
outer_polygon = Polygon(outer_coords)
inner_polygons_union = unary_union(inner_polygons)
filled_area = outer_polygon.difference(inner_polygons_union)

x, y = filled_area.exterior.xy
plt.fill(x, y, color='lightgreen', alpha=0.5, zorder=1)

for interior in filled_area.interiors:
    x, y = interior.xy
    plt.fill(x, y, color='white', zorder=2)

# 设置图表的标题和轴标签
plt.xlabel('Longitude')
plt.ylabel('Latitude')
plt.title('Extracted Edges')
plt.legend()
plt.axis('equal')
plt.grid(True)

# 在图形上显示计算结果
plt.text(0.05, 0.95, f'Outer Perimeter: {outer_perimeter:.2f} m\nOuter Area: {outer_area:.2f} m²\nNet Area: {net_area:.2f} m²',
         ha='left', va='top', transform=plt.gca().transAxes, color='blue', fontsize=12)

# 显示图表
plt.show()
