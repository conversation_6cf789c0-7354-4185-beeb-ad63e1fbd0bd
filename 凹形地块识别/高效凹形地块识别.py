#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
高效凹形地块识别
---------------
结合alphashape和cdBoundary优点的高性能凹形地块提取算法
可处理GPS轨迹数据，生成精确地块边界

特点:
- 支持JSON格式轨迹数据输入
- 多种算法（alphashape/cdBoundary）可选
- 内置多边形优化功能
- 高性能并行处理
- 详细的图表和数据输出
"""

import json
import time
import os
import numpy as np
import matplotlib.pyplot as plt
import geopandas as gpd
from shapely.geometry import Polygon, Point
from pyproj import CRS
from math import radians, cos, sin, asin, sqrt
import concurrent.futures
import random
from pathlib import Path
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# 尝试导入可选的依赖包
try:
    import alphashape
    HAVE_ALPHASHAPE = True
except ImportError:
    HAVE_ALPHASHAPE = False
    print("提示: alphashape包未安装。运行 'pip install alphashape' 以支持alphashape算法。")

try:
    from cdBoundary.boundary import ConcaveHull
    HAVE_CDBOUNDARY = True
except ImportError:
    HAVE_CDBOUNDARY = False
    print("提示: cdBoundary包未安装。运行 'pip install cdBoundary' 以支持cdBoundary算法。")

# =============== 可调整的参数 ===============
class Config:
    # 算法参数
    ALGORITHM = "combined"  # 可选: "alphashape", "cdboundary", "combined"
    ALPHA = 0.0005          # alphashape参数: 控制凹陷程度 (值越小，凹陷越明显)
    CD_TOL = 15             # cdBoundary参数: 边长阈值 (米)
    
    # 数据处理参数
    MAX_POINTS = None       # 最大处理点数，设为None使用全部点
    TIME_FILTER = True      # 是否按时间间隔过滤点
    TIME_INTERVAL = 5       # 时间间隔秒数
    SIMPLIFY_TOLERANCE = 0.5  # 简化多边形容差(米)
    
    # 输出参数
    SAVE_RESULTS = True     # 是否保存结果
    OUTPUT_DIR = "凹形地块识别/output"  # 输出目录
    SHOW_INTERMEDIATE = False  # 是否显示中间结果

# =============== 工具函数 ===============

def haversine(lon1, lat1, lon2, lat2):
    """计算两个经纬度点之间的距离(米)"""
    # 将十进制度数转换为弧度
    lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])
    # 计算经度和纬度的差值
    dlon = lon2 - lon1
    dlat = lat2 - lat1
    # Haversine公式
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * asin(sqrt(a))
    r = 6371000  # 地球半径, 单位为米
    return c * r

def compute_edge_lengths(polygon):
    """计算多边形各边的长度，返回边长列表和统计信息"""
    coords = list(polygon.exterior.coords)
    edge_lengths = []
    for i in range(len(coords) - 1):
        p1 = coords[i]
        p2 = coords[i+1]
        length = haversine(p1[0], p1[1], p2[0], p2[1])
        edge_lengths.append(length)
    
    if edge_lengths:
        return {
            "lengths": edge_lengths,
            "max": max(edge_lengths),
            "min": min(edge_lengths),
            "avg": sum(edge_lengths) / len(edge_lengths),
            "total": sum(edge_lengths),
            "count": len(edge_lengths)
        }
    return {
        "lengths": [],
        "max": 0,
        "min": 0,
        "avg": 0,
        "total": 0,
        "count": 0
    }

def parse_datetime(dt_str):
    """解析日期时间字符串"""
    try:
        from datetime import datetime
        return datetime.strptime(dt_str, "%Y-%m-%d %H:%M:%S")
    except:
        return None

def filter_points_by_time(data, interval_seconds=5):
    """按时间间隔过滤点"""
    if not data or len(data) < 2:
        return data
    
    filtered = [data[0]]
    last_time = parse_datetime(data[0]["time"])
    
    for point in data[1:]:
        current_time = parse_datetime(point["time"])
        if not last_time or not current_time:
            filtered.append(point)
            continue
            
        time_diff = (current_time - last_time).total_seconds()
        if time_diff >= interval_seconds:
            filtered.append(point)
            last_time = current_time
            
    return filtered

def simplify_polygon(polygon, tolerance_meters=1):
    """简化多边形，减少不必要的顶点"""
    # 转换到UTM投影进行精确简化
    temp_gdf = gpd.GeoDataFrame(geometry=[polygon], crs=CRS('EPSG:4326'))
    utm_crs = temp_gdf.estimate_utm_crs()
    temp_gdf = temp_gdf.to_crs(utm_crs)
    
    # 执行简化
    simplified = temp_gdf.geometry.iloc[0].simplify(tolerance_meters)
    
    # 转回WGS84
    temp_gdf = gpd.GeoDataFrame(geometry=[simplified], crs=utm_crs)
    temp_gdf = temp_gdf.to_crs(CRS('EPSG:4326'))
    
    return temp_gdf.geometry.iloc[0]

# =============== 核心算法 ===============

def create_alphashape_polygon(points, alpha=0.0005):
    """使用alphashape算法创建凹形多边形"""
    if not HAVE_ALPHASHAPE:
        print("错误: 未安装alphashape库")
        return None
        
    try:
        # 创建凹多边形
        concave_hull = alphashape.alphashape(points, alpha)
        
        # 如果结果是MultiPolygon，选择最大的一个
        if hasattr(concave_hull, 'geoms'):
            concave_hull = max(concave_hull.geoms, key=lambda p: p.area)
            
        return concave_hull
    except Exception as e:
        print(f"alphashape算法执行失败: {str(e)}")
        return None

def create_cdboundary_polygon(points, tol=20):
    """使用cdBoundary算法创建凹形多边形"""
    if not HAVE_CDBOUNDARY:
        print("错误: 未安装cdBoundary库")
        return None
        
    try:
        # 转换为需要的格式
        points_list = points.tolist() if isinstance(points, np.ndarray) else points
        
        # 创建凹多边形
        ch = ConcaveHull()
        ch.loadpoints(points_list)
        
        # 可以先尝试自动估算参数
        if Config.SHOW_INTERMEDIATE:
            estimated_tol = ch.estimate()
            print(f"cdBoundary自动估算的边长阈值: {estimated_tol:.2f}m")
        
        # 使用指定阈值
        ch.calculatehull(tol=tol)
        shape = ch.hull
        
        # 如果结果是MultiPolygon，选择最大的一个
        if hasattr(shape, 'geoms'):
            shape = max(shape.geoms, key=lambda p: p.area)
            
        return shape
    except Exception as e:
        print(f"cdBoundary算法执行失败: {str(e)}")
        return None

def create_combined_polygon(points, alpha=0.0005, tol=20):
    """结合alphashape和cdBoundary的优点创建凹形多边形"""
    # 首先尝试使用alphashape（更好地捕获整体形状）
    alphashape_polygon = create_alphashape_polygon(points, alpha)
    
    # 然后尝试使用cdBoundary（更好地捕获局部细节）
    cdboundary_polygon = create_cdboundary_polygon(points, tol)
    
    # 如果两个都成功，选择面积更合理、形状更好的那个
    if alphashape_polygon and cdboundary_polygon:
        # 计算两个多边形的面积和周长
        alpha_area = alphashape_polygon.area
        cd_area = cdboundary_polygon.area
        alpha_perimeter = alphashape_polygon.length
        cd_perimeter = cdboundary_polygon.length
        
        # 计算形状复杂度（圆形度）：4π × 面积 / 周长²
        # 值越接近1，形状越接近圆形，越规则
        alpha_circularity = (4 * np.pi * alpha_area) / (alpha_perimeter ** 2) if alpha_perimeter > 0 else 0
        cd_circularity = (4 * np.pi * cd_area) / (cd_perimeter ** 2) if cd_perimeter > 0 else 0
        
        # 计算平均边长
        alpha_edges = compute_edge_lengths(alphashape_polygon)
        cd_edges = compute_edge_lengths(cdboundary_polygon)
        
        if Config.SHOW_INTERMEDIATE:
            print(f"alphashape 边数: {alpha_edges['count']}, 面积: {alpha_area:.2f}, 圆形度: {alpha_circularity:.4f}")
            print(f"cdBoundary 边数: {cd_edges['count']}, 面积: {cd_area:.2f}, 圆形度: {cd_circularity:.4f}")
        
        # 如果边数差异很大，可能其中一个捕获了更多细节
        # 如果面积差异很大，选择面积更小的（通常更准确，因为可能有缺口）
        # 如果圆形度差异不大，选择圆形度更高的
        
        # 启发式选择策略
        if alpha_edges['count'] > 1.5 * cd_edges['count']: 
            # alphashape捕获了更多细节
            return alphashape_polygon
        elif cd_edges['count'] > 1.5 * alpha_edges['count']:
            # cdBoundary捕获了更多细节
            return cdboundary_polygon
        elif alpha_area < 0.8 * cd_area:
            # alphashape面积明显更小，可能更准确
            return alphashape_polygon
        elif cd_area < 0.8 * alpha_area:
            # cdBoundary面积明显更小，可能更准确
            return cdboundary_polygon
        else:
            # 选择圆形度更高的
            return alphashape_polygon if alpha_circularity > cd_circularity else cdboundary_polygon
    
    # 如果只有一个成功，则返回那个
    return alphashape_polygon or cdboundary_polygon

def process_data(data_file):
    """处理JSON数据文件"""
    start_time = time.time()
    print(f"开始处理文件: {data_file}")
    
    # 创建输出目录
    if Config.SAVE_RESULTS:
        os.makedirs(Config.OUTPUT_DIR, exist_ok=True)
    
    # 从文件名提取基础名称（不含路径和扩展名）
    base_name = os.path.basename(data_file).split('.')[0]
    
    # ---- 1. 读取数据 ----
    print("读取JSON数据...")
    with open(data_file, 'r') as f:
        data = json.load(f)
    
    print(f"原始数据点数: {len(data)}")
    
    # ---- 2. 数据预处理 ----
    # 时间过滤
    if Config.TIME_FILTER:
        data = filter_points_by_time(data, Config.TIME_INTERVAL)
        print(f"时间过滤后点数: {len(data)}")
    
    # 限制处理点数
    if Config.MAX_POINTS and len(data) > Config.MAX_POINTS:
        random.seed(42)  # 确保结果可重复
        data = random.sample(data, Config.MAX_POINTS)
        print(f"采样后点数: {len(data)}")
    
    # 提取坐标
    points_list = [(point['longitude'], point['latitude']) for point in data]
    points = np.array(points_list)
    
    # ---- 3. 算法选择与多边形生成 ----
    print(f"使用算法: {Config.ALGORITHM}")
    
    if Config.ALGORITHM == "alphashape":
        polygon = create_alphashape_polygon(points, Config.ALPHA)
    elif Config.ALGORITHM == "cdboundary":
        polygon = create_cdboundary_polygon(points, Config.CD_TOL)
    else:  # combined
        polygon = create_combined_polygon(points, Config.ALPHA, Config.CD_TOL)
    
    if not polygon:
        print("多边形生成失败，请尝试不同的算法或参数")
        return
    
    # ---- 4. 多边形后处理 ----
    # 简化多边形，减少不必要的顶点
    if Config.SIMPLIFY_TOLERANCE > 0:
        original_vertex_count = len(list(polygon.exterior.coords))
        polygon = simplify_polygon(polygon, Config.SIMPLIFY_TOLERANCE)
        simplified_vertex_count = len(list(polygon.exterior.coords))
        print(f"多边形简化: {original_vertex_count} → {simplified_vertex_count} 个顶点")
    
    # ---- 5. 计算多边形属性 ----
    # 创建GeoDataFrame并转换坐标以计算面积
    gdf = gpd.GeoDataFrame(geometry=[polygon], crs=CRS('EPSG:4326'))
    gdf_utm = gdf.to_crs(gdf.estimate_utm_crs())
    area_m2 = gdf_utm.geometry.area.iloc[0]
    
    # 计算边长统计
    edge_stats = compute_edge_lengths(polygon)
    
    # 输出多边形属性
    print(f"\n=== 多边形属性 ===")
    print(f"面积: {area_m2:.2f} 平方米")
    print(f"边数: {edge_stats['count']}")
    print(f"最长边: {edge_stats['max']:.2f} 米")
    print(f"最短边: {edge_stats['min']:.2f} 米")
    print(f"平均边长: {edge_stats['avg']:.2f} 米")
    print(f"总周长: {edge_stats['total']:.2f} 米")
    
    # ---- 6. 生成可视化图表 ----
    print("生成可视化图表...")
    
    plt.figure(figsize=(12, 8))
    
    # 绘制原始点云
    plt.scatter(points[:, 0], points[:, 1], color='gray', marker='.', 
                s=3, alpha=0.4, label='GPS轨迹点')
    
    # 绘制凹形多边形
    x, y = polygon.exterior.xy
    plt.plot(x, y, color='red', linewidth=2, label='凹形地块边界')
    
    # 绘制多边形顶点
    plt.scatter(x, y, color='blue', marker='o', s=30, label='边界顶点')
    
    # 标注较长的边的长度
    coords = list(polygon.exterior.coords)
    for i in range(len(coords) - 1):
        p1 = coords[i]
        p2 = coords[i+1]
        mid_point = ((p1[0] + p2[0])/2, (p1[1] + p2[1])/2)
        length = edge_stats['lengths'][i]
        
        # 标注长边
        if length > 0.5 * edge_stats['max']:
            plt.text(mid_point[0], mid_point[1], f'{length:.1f}m', 
                    color='black', fontsize=8, ha='center', va='center', 
                    bbox=dict(facecolor='white', alpha=0.7, pad=1, boxstyle='round,pad=0.1'))
    
    # 图表标题和标签
    algo_name = {"alphashape": f"AlphaShape (α={Config.ALPHA})", 
                "cdboundary": f"cdBoundary (tol={Config.CD_TOL}m)",
                "combined": "混合算法"}
    
    plt.title(f'凹形地块识别 - {algo_name[Config.ALGORITHM]}')
    plt.xlabel('经度')
    plt.ylabel('纬度')
    plt.grid(True, linestyle='--', alpha=0.5)
    
    # 在图中标注面积和顶点数
    bbox_props = dict(boxstyle="round,pad=0.5", fc="white", ec="blue", lw=1, alpha=0.8)
    info_text = (f"面积: {area_m2:.2f} 平方米\n"
                f"顶点数: {edge_stats['count']}\n"
                f"周长: {edge_stats['total']:.2f} 米")
    plt.text(min(x), min(y), info_text, fontsize=10, color='black', bbox=bbox_props)
    
    plt.legend(loc='best')
    plt.tight_layout()
    
    # ---- 7. 保存结果 ----
    if Config.SAVE_RESULTS:
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        
        # 保存GeoJSON
        output_geojson = os.path.join(Config.OUTPUT_DIR, f"{base_name}_{Config.ALGORITHM}_{timestamp}.geojson")
        gdf.to_file(output_geojson, driver='GeoJSON', encoding='utf-8')
        
        # 保存图像
        output_image = os.path.join(Config.OUTPUT_DIR, f"{base_name}_{Config.ALGORITHM}_{timestamp}.png")
        plt.savefig(output_image, dpi=300, bbox_inches='tight')
        
        print(f"结果已保存为GeoJSON: {output_geojson}")
        print(f"可视化图表已保存为: {output_image}")
    
    # 计算处理时间
    end_time = time.time()
    total_time = end_time - start_time
    print(f"总处理时间: {total_time:.2f} 秒")
    
    # 显示图表
    plt.show()
    
    return {
        "file": data_file,
        "points": len(data),
        "area": area_m2,
        "vertices": edge_stats['count'],
        "processing_time": total_time
    }

def main():
    print("凹形地块识别 - 高性能版")
    print("=" * 50)
    
    # 列出当前目录下的JSON文件
    current_dir = os.path.dirname(os.path.abspath(__file__))
    json_files = [f for f in os.listdir(current_dir) if f.endswith('.json')]
    
    if not json_files:
        print("未找到JSON文件！请确保轨迹数据文件位于当前目录中。")
        return
    
    print(f"找到 {len(json_files)} 个JSON文件:")
    for i, f in enumerate(json_files):
        print(f"  {i+1}. {f}")
    
    # 用户选择文件
    while True:
        try:
            choice = input("\n请选择要处理的文件编号 (输入'q'退出): ")
            if choice.lower() == 'q':
                return
                
            choice = int(choice)
            if 1 <= choice <= len(json_files):
                selected_file = os.path.join(current_dir, json_files[choice-1])
                break
            else:
                print("无效选择，请重试")
        except ValueError:
            print("请输入有效的数字")
    
    # 处理所选文件
    result = process_data(selected_file)
    
    # 输出摘要
    if result:
        print("\n处理摘要:")
        print(f"文件: {os.path.basename(result['file'])}")
        print(f"处理点数: {result['points']}")
        print(f"地块面积: {result['area']:.2f} 平方米")
        print(f"边界顶点数: {result['vertices']}")
        print(f"处理时间: {result['processing_time']:.2f} 秒")

if __name__ == "__main__":
    main() 