<!DOCTYPE html>
<html>
<head>
    
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    
        <script>
            L_NO_TOUCH = false;
            L_DISABLE_3D = false;
        </script>
    
    <style>html, body {width: 100%;height: 100%;margin: 0;padding: 0;}</style>
    <style>#map {position:absolute;top:0;bottom:0;right:0;left:0;}</style>
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap-glyphicons.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.0/css/all.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/python-visualization/folium/folium/templates/leaflet.awesome.rotate.min.css"/>
    
            <meta name="viewport" content="width=device-width,
                initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
            <style>
                #map_78ea66c379711a83054558cfc0c2ebbd {
                    position: relative;
                    width: 100.0%;
                    height: 100.0%;
                    left: 0.0%;
                    top: 0.0%;
                }
                .leaflet-container { font-size: 1rem; }
            </style>
        
</head>
<body>
    
    
            <div class="folium-map" id="map_78ea66c379711a83054558cfc0c2ebbd" ></div>
        
</body>
<script>
    
    
            var map_78ea66c379711a83054558cfc0c2ebbd = L.map(
                "map_78ea66c379711a83054558cfc0c2ebbd",
                {
                    center: [48.78500044532059, 126.91993212460146],
                    crs: L.CRS.EPSG3857,
                    ...{
  "zoom": 15,
  "zoomControl": true,
  "preferCanvas": false,
}

                }
            );

            

        
    
            var tile_layer_2294fe8036ec6bbd6731d2d1b30b00ed = L.tileLayer(
                "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
                {
  "minZoom": 0,
  "maxZoom": 19,
  "maxNativeZoom": 19,
  "noWrap": false,
  "attribution": "\u0026copy; \u003ca href=\"https://www.openstreetmap.org/copyright\"\u003eOpenStreetMap\u003c/a\u003e contributors",
  "subdomains": "abc",
  "detectRetina": false,
  "tms": false,
  "opacity": 1,
}

            );
        
    
            tile_layer_2294fe8036ec6bbd6731d2d1b30b00ed.addTo(map_78ea66c379711a83054558cfc0c2ebbd);
        
    
        function geo_json_6a0605807d3d3272f4aab318e1ddf3b7_styler(feature) {
            switch(feature.id) {
                default:
                    return {"color": "red", "fillColor": "#FF0000", "fillOpacity": 0.25, "weight": 3};
            }
        }

        function geo_json_6a0605807d3d3272f4aab318e1ddf3b7_onEachFeature(feature, layer) {
            layer.on({
            });
        };
        var geo_json_6a0605807d3d3272f4aab318e1ddf3b7 = L.geoJson(null, {
                onEachFeature: geo_json_6a0605807d3d3272f4aab318e1ddf3b7_onEachFeature,
            
                style: geo_json_6a0605807d3d3272f4aab318e1ddf3b7_styler,
            ...{
}
        });

        function geo_json_6a0605807d3d3272f4aab318e1ddf3b7_add (data) {
            geo_json_6a0605807d3d3272f4aab318e1ddf3b7
                .addData(data);
        }
            geo_json_6a0605807d3d3272f4aab318e1ddf3b7_add({"bbox": [126.91725159, 48.78194808999999, 126.9250946, 48.795749660000006], "features": [{"bbox": [126.91725159, 48.78194808999999, 126.9250946, 48.795749660000006], "geometry": {"coordinates": [[[126.92302704000001, 48.79467772999999], [126.91883850000002, 48.78619766], [126.91773224, 48.78384399], [126.91730499, 48.782787320000004], [126.91725159, 48.78258514000001], [126.91733551, 48.782375339999994], [126.91734314, 48.782363889999985], [126.91777039, 48.78198242], [126.91780853, 48.78195571999999], [126.91781616, 48.7819519], [126.91783142, 48.78194808999999], [126.91783905, 48.78194809], [126.91785431, 48.78194808999999], [126.91786194, 48.78194808999999], [126.91787720000002, 48.78194808999999], [126.91802216, 48.781986239999995], [126.91812133999998, 48.782016750000004], [126.91814422999998, 48.782024379999996], [126.91854858, 48.78217315999999], [126.91858673, 48.78218842], [126.91930389, 48.78251647999999], [126.91960907, 48.78271103000001], [126.92508698000002, 48.795639040000005], [126.9250946, 48.79565811], [126.9250946, 48.79566955999999], [126.92489624, 48.795749660000006], [126.92488861, 48.79574965999999], [126.92477417, 48.79574585000001], [126.92408752, 48.795581819999995], [126.92359161, 48.79526138000001], [126.92346954000001, 48.79516219999999], [126.92322540000002, 48.79494094999999], [126.9230423, 48.79470062], [126.92302704000001, 48.79467772999999]]], "type": "Polygon"}, "id": "0", "properties": {}, "type": "Feature"}], "type": "FeatureCollection"});

        
    
            geo_json_6a0605807d3d3272f4aab318e1ddf3b7.bindTooltip(
                `<div>
                     最终地块面积: 221013.44 m² (α=0.05, Projected)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
        var popup_172be9ba3137dca2f7376989250d15aa = L.popup({
  "maxWidth": 350,
});

        
            
                var html_926b64319dd35fd932183021062a90cb = $(`<div id="html_926b64319dd35fd932183021062a90cb" style="width: 100.0%; height: 100.0%;"><div style="font-family:Arial,sans-serif;font-size:13px;"><h4>地块信息</h4><p><strong>最终地块面积: 221013.44 m² (α=0.05, Projected)</strong></p></div></div>`)[0];
                popup_172be9ba3137dca2f7376989250d15aa.setContent(html_926b64319dd35fd932183021062a90cb);
            
        

        geo_json_6a0605807d3d3272f4aab318e1ddf3b7.bindPopup(popup_172be9ba3137dca2f7376989250d15aa)
        ;

        
    
    
            geo_json_6a0605807d3d3272f4aab318e1ddf3b7.addTo(map_78ea66c379711a83054558cfc0c2ebbd);
        
    
            var marker_cf099a2613a180408bce18f6e570887d = L.marker(
                [48.788432731385335, 126.92100192697677],
                {
}
            ).addTo(map_78ea66c379711a83054558cfc0c2ebbd);
        
    
            var div_icon_328a9a84186501afcc7b3a8dbe361ec0 = L.divIcon({
  "html": "\u003cdiv style=\"font-size:10pt;color:black;background-color:rgba(255,255,255,0.7);border-radius:4px;padding:2px 5px;text-align:center;white-space:nowrap;\"\u003e\u003cb\u003e221013.4 m\u00b2\u003c/b\u003e\u003c/div\u003e",
  "iconSize": [150, 36],
  "iconAnchor": [75, 18],
  "className": "empty",
});
            marker_cf099a2613a180408bce18f6e570887d.setIcon(div_icon_328a9a84186501afcc7b3a8dbe361ec0);
        
    
            marker_cf099a2613a180408bce18f6e570887d.bindTooltip(
                `<div>
                     中心点 (估算面积: 221013.4 m²)
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
                marker_cf099a2613a180408bce18f6e570887d.setIcon(div_icon_328a9a84186501afcc7b3a8dbe361ec0);
            
    
            var layer_control_c2d0ce71b2906161aa8273cecaaccc50_layers = {
                base_layers : {
                    "openstreetmap" : tile_layer_2294fe8036ec6bbd6731d2d1b30b00ed,
                },
                overlays :  {
                    "\u6700\u7ec8\u5730\u5757\u8fb9\u754c" : geo_json_6a0605807d3d3272f4aab318e1ddf3b7,
                },
            };
            let layer_control_c2d0ce71b2906161aa8273cecaaccc50 = L.control.layers(
                layer_control_c2d0ce71b2906161aa8273cecaaccc50_layers.base_layers,
                layer_control_c2d0ce71b2906161aa8273cecaaccc50_layers.overlays,
                {
  "position": "topright",
  "collapsed": true,
  "autoZIndex": true,
}
            ).addTo(map_78ea66c379711a83054558cfc0c2ebbd);

        
</script>
</html>