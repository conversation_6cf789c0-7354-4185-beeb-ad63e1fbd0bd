import cv2
import numpy as np
import os
from PIL import Image, ImageFont, ImageDraw
import matplotlib.font_manager as fm
import re
import datetime

def parse_bbox_tag_format(bbox_text):
    """
    解析<bbox>格式的边界框数据
    支持格式如: <bbox>21 707 101 789</bbox>
    
    参数:
    - bbox_text: 包含边界框数据的文本
    
    返回:
    - 解析后的边界框数据列表 [[x1, y1, x2, y2], ...]
    """
    try:
        # 使用正则表达式匹配 <bbox>数字 数字 数字 数字</bbox> 的模式
        pattern = r'<bbox>(\d+)\s+(\d+)\s+(\d+)\s+(\d+)</bbox>'
        matches = re.findall(pattern, bbox_text)
        
        # 将匹配结果转换为整数列表
        bboxes = [[int(x1), int(y1), int(x2), int(y2)] for x1, y1, x2, y2 in matches]
        
        print(f"成功解析 {len(bboxes)} 个边界框")
        return bboxes
    except Exception as e:
        print(f"解析边界框数据时出错: {e}")
        return []

def parse_point_data(point_text):
    """
    解析无逗号格式的点数据
    支持格式如: [42 734], [82 443]
    
    参数:
    - point_text: 包含点数据的文本
    
    返回:
    - 解析后的点数据列表
    """
    try:
        # 去掉所有空格以便于使用正则表达式
        point_text = re.sub(r'\s+', ' ', point_text.strip())
        
        # 使用正则表达式匹配 [数字 数字] 的模式
        pattern = r'\[(\d+)\s+(\d+)\]'
        matches = re.findall(pattern, point_text)
        
        # 将匹配结果转换为整数列表
        points = [[int(x), int(y)] for x, y in matches]
        
        print(f"成功解析 {len(points)} 个点")
        return points
    except Exception as e:
        print(f"解析点数据时出错: {e}")
        return []

# <bbox>格式的边界框数据
bbox_tag_data = """
<bbox>48 302 207 448</bbox><bbox>97 368 267 535</bbox><bbox>207 459 392 645</bbox><bbox>208 278 362 416</bbox><bbox>265 345 435 509</bbox><bbox>354 239 507 375</bbox><bbox>392 428 577 593</bbox><bbox>411 553 618 760</bbox><bbox>418 305 587 459</bbox><bbox>497 214 642 337</bbox><bbox>537 673 782 915</bbox><bbox>570 397 757 571</bbox><bbox>577 286 740 432</bbox><bbox>608 201 782 337</bbox><bbox>614 522 827 726</bbox><bbox>741 293 908 439</bbox><bbox>752 390 920 560</bbox><bbox>771 218 928 342</bbox>
"""

# 解析边界框数据 - 直接使用<bbox>格式
bbox_data = parse_bbox_tag_format(bbox_tag_data)

"""
# 无逗号格式的点数据示例 - 如果需要从点生成边界框，取消注释此部分
no_comma_point_data = '''
[42 734]
[82 443]
...
'''
# 解析点数据
point_data = parse_point_data(no_comma_point_data)

# 根据点生成边界框 - 每个点周围创建合适大小的框
bbox_size = 80  # 边界框的一半宽度/高度
bbox_data = []
for point in point_data:
    x, y = point
    # 创建以点为中心的边界框 [x1, y1, x2, y2]
    bbox = [max(0, x - bbox_size), max(0, y - bbox_size), 
            x + bbox_size, y + bbox_size]
    bbox_data.append(bbox)
"""

# 图片路径
image_path = r"F:\Python\图像识别\网球.jpg"

def read_image_with_pil(image_path):
    """使用PIL读取图像，然后转换为OpenCV格式"""
    try:
        # 使用PIL读取图像
        pil_image = Image.open(image_path)
        # 转换为RGB(如果是RGBA，去掉透明通道)
        if pil_image.mode == 'RGBA':
            pil_image = pil_image.convert('RGB')
        # 转换为numpy数组
        image_np = np.array(pil_image)
        # 转换颜色通道顺序从RGB到BGR(OpenCV使用BGR)
        image_cv = cv2.cvtColor(image_np, cv2.COLOR_RGB2BGR)
        return image_cv
    except Exception as e:
        print(f"读取图像出错: {e}")
        return None

def save_image_with_pil(image, output_path):
    """使用PIL保存OpenCV格式的图像"""
    try:
        # OpenCV图像是BGR格式，转换为RGB
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        # 转换为PIL图像
        pil_image = Image.fromarray(image_rgb)
        # 保存图像
        pil_image.save(output_path)
        print(f"使用PIL成功保存图像至: {output_path}")
        return True
    except Exception as e:
        print(f"使用PIL保存图像时出错: {e}")
        return False

def scale_bboxes(bboxes, original_size, target_size):
    """
    缩放边界框坐标以匹配目标图像尺寸
    
    参数:
    - bboxes: 原始边界框列表 [x1, y1, x2, y2]
    - original_size: 原始坐标系尺寸 (width, height)
    - target_size: 目标图像尺寸 (width, height)
    
    返回:
    - 缩放后的边界框列表
    """
    scaled_bboxes = []
    for bbox in bboxes:
        x1, y1, x2, y2 = bbox
        
        # 计算缩放比例
        scale_x = target_size[0] / original_size[0]
        scale_y = target_size[1] / original_size[1]
        
        # 应用缩放
        scaled_x1 = int(x1 * scale_x)
        scaled_y1 = int(y1 * scale_y)
        scaled_x2 = int(x2 * scale_x)
        scaled_y2 = int(y2 * scale_y)
        
        # 确保坐标在图像范围内
        scaled_x1 = max(0, min(scaled_x1, target_size[0]-1))
        scaled_y1 = max(0, min(scaled_y1, target_size[1]-1))
        scaled_x2 = max(0, min(scaled_x2, target_size[0]-1))
        scaled_y2 = max(0, min(scaled_y2, target_size[1]-1))
        
        scaled_bboxes.append([scaled_x1, scaled_y1, scaled_x2, scaled_y2])
    
    return scaled_bboxes

def draw_bboxes_on_image(image_path, bboxes, output_path=None, original_size=(1000, 1000)):
    """
    在图像上绘制边界框
    
    参数:
    - image_path: 原始图片路径
    - bboxes: 边界框列表，每个边界框为 [x1, y1, x2, y2] 格式
    - output_path: 输出图片路径，如果为None则显示图片
    - original_size: 原始标注坐标的参考尺寸
    
    返回:
    - 绘制了边界框的图像
    """
    # 读取图像
    print(f"尝试读取图像: {image_path}")
    print(f"图像文件是否存在: {os.path.exists(image_path)}")
    
    # 使用PIL读取图像
    image = read_image_with_pil(image_path)
    if image is None:
        print(f"无法读取图像: {image_path}")
        return None
    else:
        print(f"成功读取图像，尺寸: {image.shape}")
    
    # 缩放边界框以匹配图像尺寸
    target_size = (image.shape[1], image.shape[0])  # (width, height)
    scaled_bboxes = scale_bboxes(bboxes, original_size, target_size)
    
    # 绘制每个边界框
    for i, bbox in enumerate(scaled_bboxes):
        x1, y1, x2, y2 = bbox
        
        # 生成随机颜色，使每个边界框更容易区分
        # 对草莓使用更合适的颜色 - 红色系和绿色系
        colors = [
            (0, 0, 255),     # 红色
            (0, 128, 0),     # 深绿色
            (0, 255, 0),     # 绿色
            (128, 0, 255),   # 紫色
            (0, 165, 255),   # 橙色
            (0, 215, 255),   # 黄色
            (255, 0, 0),     # 蓝色
            (255, 255, 0),   # 青色
            (255, 0, 255),   # 紫红色
            (128, 128, 0),   # 橄榄色
            (0, 128, 128),   # 褐色
            (128, 0, 0),     # 深蓝色
            (0, 0, 128),     # 深红色
            (192, 192, 192), # 银色
            (128, 128, 128)  # 灰色
        ]
        color = colors[i % len(colors)]
        
        thickness = 2
        cv2.rectangle(image, (x1, y1), (x2, y2), color, thickness)
        
        # 在边界框旁边标注编号
        label = f"#{i+1}"
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.7
        text_thickness = 2
        
        # 计算文本大小
        text_size = cv2.getTextSize(label, font, font_scale, text_thickness)[0]
        
        # 确保标签位置在图像内
        label_x = x1
        label_y = y1 - 10 if y1 - 10 > text_size[1] else y1 + text_size[1] + 10
        
        # 添加文本背景以增强可读性
        cv2.rectangle(image, 
                     (label_x, label_y - text_size[1] - 5), 
                     (label_x + text_size[0], label_y + 5), 
                     (0, 0, 0), 
                     -1)  # 填充黑色背景
        
        # 绘制标签文本
        cv2.putText(image, label, (label_x, label_y), font, font_scale, (255, 255, 255), text_thickness)
    
    # 计算草莓数量
    strawberry_count = len(bboxes)
    
    # 使用PIL来绘制中文以避免乱码
    # 先将OpenCV图像转换为PIL图像
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    pil_image = Image.fromarray(image_rgb)
    draw = ImageDraw.Draw(pil_image)
    
    # 查找系统中的中文字体
    fonts = fm.findSystemFonts(fontpaths=None, fontext='ttf')
    chinese_fonts = [font for font in fonts if os.path.basename(font).startswith(('sim', 'msyh', 'microsoft', 'simsun', 'simhei', 'kaiti'))]
    
    font_path = None
    if chinese_fonts:
        font_path = chinese_fonts[0]
    else:
        print("未找到中文字体，将使用默认字体")
    
    try:
        # 尝试加载中文字体
        if font_path:
            font = ImageFont.truetype(font_path, 30)
        else:
            font = ImageFont.load_default()
            
        # 绘制黑色背景
        draw.rectangle((10, 10, 220, 50), fill=(0, 0, 0))
        # 使用中文绘制文本
        draw.text((20, 15), f"数量: {strawberry_count}", fill=(255, 255, 255), font=font)
        
        # 将PIL图像转回OpenCV格式
        image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
    except Exception as e:
        print(f"绘制中文文本时出错: {e}")
        # 如果中文渲染失败，回退到原始方法
        cv2.rectangle(image, (10, 10), (220, 50), (0, 0, 0), -1)
        cv2.putText(image, f"Strawberry Count: {strawberry_count}", (20, 40), cv2.FONT_HERSHEY_SIMPLEX, 
                    0.9, (255, 255, 255), 2)
    
    # 保存或显示图像
    if output_path:
        # 直接使用PIL保存图像，跳过OpenCV保存尝试
        save_result = save_image_with_pil(image, output_path)
        if save_result:
            print(f"已保存标注图像至: {output_path}")
        else:
            print(f"保存图像失败: {output_path}")
    
    return image

def main():
    # 输出文件路径 - 使用时间戳创建唯一文件名
    current_dir = os.path.dirname(os.path.abspath(__file__))
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 从原始图像路径提取文件名（不含扩展名）
    base_filename = os.path.splitext(os.path.basename(image_path))[0]
    output_path = os.path.join(current_dir, f"{base_filename}_annotated_{timestamp}.jpeg")
    
    # 绘制边界框并保存图像
    # 使用原始坐标系大小为1000x1000，这样可以根据实际图片尺寸进行适当缩放
    result_image = draw_bboxes_on_image(image_path, bbox_data, output_path, original_size=(1000, 1000))
    
    if result_image is not None:
        print("处理完成！标注图像已保存。")
        print(f"图像已保存至: {output_path}")

if __name__ == "__main__":
    main() 